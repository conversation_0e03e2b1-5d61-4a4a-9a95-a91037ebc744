@use '@immobiliare-labs/imgeditor/index.scss';
@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.modal-body, .gx-modal__body.modal-image-editor {
  display: flex;
  padding: 0;
  align-items: stretch;

  .modal-image-editor__container {
    width: 100%;
    min-height: 50vh;
  }
}

.lie-button {
  height: ($gx-unit-size * 5);
  position: relative;
  border-radius: radius(sm);
  @include typography(title-2);

  &--primary {
    background-color: color(background-accent);
    border-color: color(background-accent);
  }

  &--link {
    color: color(content-action);
  }
}

.lie-zoom {
  &__progress {
    background-color: color(background-brand);
  }

  &__handle {
    border-color: color(border-selected);
  }
}
