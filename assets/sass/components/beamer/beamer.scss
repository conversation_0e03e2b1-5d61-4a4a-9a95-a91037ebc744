@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

// Beamer custom icon positioning
.gx-header {
  .beamer_icon {
    &.active {
      // to overwrite in app style
      background-color: color(background-notification) !important;
      border: 0.1rem solid color(border-reversed);
      font-family: inherit;
      font-weight: 600;
      box-sizing: content-box;
    }
  }
}

.beamerAnnouncementPopup {
  @include media('screen', '<#{emConverter(800)}') {
    width: 100%;
    height: 100%;
    max-height: 100%;
  }
}
