@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

$gx-message-radius: 0.6rem;
$messaging-accent: color(background-brand-alt);
$gx-read-accent: #6bc5f2;

.messages {
  .gtx-ah-footer-spacing {
    display: none;
  }

  .gx-filtersWrapper {
    @include media('screen', '<=#{breakpoint(md)}') {
      padding: space(md) space(xl) 0;
    }

    @include media('screen', '<=#{breakpoint(sm)}') {
      padding: space(md) space(md) 0;
    }

    .filter-box__section {
      display: flex;
      align-items: flex-end;

      @include media('screen', '<=#{breakpoint(sm)}') {
        align-items: flex-start;
        margin-top: space(md);
      }
    }
  }
}

.messaging-filter-preferred {
  width: 100%;
}

.messaging__infoBox {
  justify-content: space-between;
  padding: space(md) space(xl);
  border-bottom: 0.1rem solid color(border-main);

  a {
    text-decoration: none;
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    padding: space(sm) space(md);
    border-bottom: 0.1rem solid color(border-main);
  }

  &--noBorder {
    @include media('screen', '>=#{breakpoint(sm)}') {
      border-bottom: none;
    }
  }
}

.messaging__bulkActionsBar {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  align-items: center;
  width: 100%;
  height: ($gx-unit-size * 7);
  padding: space(sm) space(xl);
  border-top: 0.1rem solid color(border-main);
  border-bottom: 0.1rem solid color(border-selected);

  &--sticky {
    position: fixed;
    left: $gx-new-menu-width;
    top: $gx-new-menu-header-height;
    @include z-index(base, 3);
    background-color: color(background-main);

    @include media('screen', '<#{breakpoint(xl)}') {
      left: $gx-header-size;
    }

    @include media('screen', '<#{breakpoint(md)}') {
      left: 0;
    }

    .has-menu-fixed & {
      left: $gx-new-menu-width + 26rem;

      @include media('screen', '<#{breakpoint(md)}') {
        left: 0;
      }
    }
  }

  .styled-checkbox {
    margin-right: space(md);

    .styled-check {
      width: 2rem;
      height: 2rem;
    }
  }

  .gx-button + .gx-button {
    margin-left: space(sm);
  }

  @include media('screen', '<#{breakpoint(md)}') {
    position: static;
    justify-content: space-between;
    padding: space(md) space(md);
    border-bottom: 0.1rem solid color(border-selected);

    &--sticky {
      position: fixed;
      left: 0;
    }
  }

  &Info {
    display: flex;
    align-items: center;
    margin-right: space(md);

    .gx-badge {
      margin-right: space(sm);
    }
  }
}

.messaging__actionBar {
  display: flex;
  justify-content: space-between;
  padding: space(md) space(xl);

  &Buttons {
    display: flex;

    .gx-checkbox {
      margin-right: space(md);
    }

    .btn--rimuovi--filtri {
      min-width: 14rem;
    }
  }

  .gx-input-group {
    max-width: 50rem;

    .gx-button {
      flex-grow: 0;
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    justify-content: flex-end;
    border-bottom: 0.1rem solid color(border-main);

    form {
      width: 100%;
    }

    .gx-input-group {
      margin-right: space(md);
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    padding: space(sm) space(md);

    .gx-input-group {
      max-width: 100%;
    }
  }
}

.gx-table__field--preferred {
  svg {
    cursor: default;
  }

  .gx-table__cell {
    min-width: 2rem;
  }
}

.gx-table__field--preferred,
.gx-table__field--checkbox {
  box-sizing: border-box;
}

.list__field--autoWidth {
  width: 0.1rem;
  white-space: nowrap;
}

.list__field--contactName {
  padding-left: space(4xl);
}

.list__field--alignRight {
  text-align: right;
}

.list__field--small {
  width: space(3xl);
}

.messaging-threadInput {
  display: flex;
  flex-shrink: 0;
  align-items: flex-end;
  padding: space(md) space(lg);
  border-top: 0.1rem solid color(border-main);
  overflow: hidden;

  @include media('screen', '<#{breakpoint(md)}') {
    width: 100%;
    padding: space(md) space(sm) space(sm);
    background-color: color(background-main);
    @include z-index(base, 2);
  }

  .no--border {
    border: 0;
  }

  .gx-button .gx-icon {
    @include icon-size(md);
  }
}

.messaging-messageInput {
  position: relative;
  width: 100%;
  min-height: 4rem;
  max-height: 8rem;
  resize: none;
  @include z-index(base, 1);

  &Wrapper {
    position: relative;
    flex: 1 1 auto;
    margin: 0 space(sm);
  }
}

.messaging-messagePlaceholder {
  position: absolute;
  top: 50%;
  left: $gx-unit-size;
  transform: translateY(-50%);
  color: color(content-low);
}

.gx-filtersWrapper {
  padding: space(md) space(xl) 0;
  border-top: 0.1rem solid color(border-main);
}

.messagingFooter {
  .pager {
    text-align: center;

    .gx-icon {
      &.loading {
        animation: gx-spin 1.25s infinite linear;
      }
    }
  }
}

// Detail

.messaging-mobileRef {
  display: none;
  padding: space(md);
  border-bottom: 0.1rem solid color(border-main);

  @include media('screen', '<#{breakpoint(md)}') {
    display: block;
  }

  .messaging-reInfo {
    margin-bottom: 0;
  }
}

.messaging__agentInfo {
  display: flex;
  align-items: center;
  padding: space(md) space(lg);
  border-top: 0.1rem solid color(border-main);
}

.gx-agent__profileImage {
  width: 4rem;
  height: 4rem;
  margin-right: space(sm);
  border-radius: radius(rounded);
  background-color: color(background-alt);
  overflow: hidden;

  img {
    max-width: 100%;
    max-height: 100%;
  }
}

.gx-agent__profileInfoLabel {
  color: color(content-medium);
  font-size: 1.2rem;
  text-transform: uppercase;
}

.gx-agent__profileInfoName {
  margin-top: space(xs);
}

.messaging-threadCanvas {
  display: flex;
  position: relative;
  flex: 1;
  flex-direction: column;
  height: 100%;
  overflow: auto;

  &__messages {
    display: flex;
    flex-direction: column;
    padding: space(xl);

    @include media('screen', '<=#{breakpoint(sm)}') {
      padding: space(md);
    }
  }
}

.messaging-threadDate {
  position: relative;
  margin: space(lg) 0;
  text-align: center;

  &:after {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 0.1rem;
    transform: translateY(-50%);
    background-color: color(border-main);
    content: '';
  }

  span {
    position: relative;
    padding: 0 space(lg);
    background-color: #fff;
    color: color(content-low);
    font-size: 1.4rem;
    @include z-index(base, 1);
  }

  @include media('screen', '<=#{breakpoint(sm)}') {
    margin: space(md) 0;
  }
}

.messaging-message {
  align-self: flex-start;
  min-width: 22rem;
  max-width: 45rem;
  margin: space(sm) 0;
  padding: space(md) space(md) 2rem;
  border-radius: $gx-message-radius;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  background-color: color(background-alt);
  font-size: 1.6rem;
  border-radius: radius(sm);
  overflow: hidden;

  @include media('screen', '<#{breakpoint(md)}') {
    max-width: 32rem;
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    max-width: 70%;
  }

  &-phone-reply {
    align-self: center;
    max-width: none;
    background-color: color(background-main);
    border: 0.1rem solid color(border-main);
    padding: space(md);

    @include media('screen', '<#{breakpoint(sm)}') {
      max-width: 100%;
    }

    &--replied {
      background-color: color(background-info);
      border: 0;
    }

    &__content {
      display: flex;
      color: color(content-medium);
      align-items: center;

      .gx-pointer {
        font-weight: 700;
      }

      .messaging-message-phone-reply--replied & {
        margin-bottom: space(md);
      }

      span {
        @include typography(body-small);
      }

      .gx-icon {
        margin-right: space(sm);
        flex-shrink: 0;
        @include icon-size(md);
      }
    }
  }

  &--attachment {
    min-width: 0;
    max-width: 24rem;
  }

  &--visit {
    width: 100%;
    max-width: 42rem;
    padding: 0;

    &:not(.messaging-message--self) {
      .messaging-messageVisitContent__info {
        font-size: ($gx-unit-size * 2);
      }

      p:last-of-type {
        margin-top: space(md);
        margin-bottom: 0;
      }
    }
  }

  &--image {
    position: relative;
    align-self: flex-start;
    min-width: 0;
    width: 30rem;
    height: 16.8rem;
    padding: 0;
    border: 0.1rem solid color(border-main);
    cursor: pointer;

    &:after {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      height: 50%;
      background: linear-gradient(180deg, rgba(0, 0, 0, 0.0001) 45.07%, rgba(0, 0, 0, 0.6) 100%);
      content: '';
    }

    img {
      position: absolute;
      width: 100%;
      height: 100%;
      object-fit: cover;
      object-position: center;
    }
  }

  &--received {
    &.messaging-message--isFirst {
      border-top-left-radius: $gx-message-radius;
    }

    &.messaging-message--isLast {
      border-bottom-left-radius: $gx-message-radius;
    }

    & + & {
      margin-top: (-(space(xs)));
    }
  }

  &--self {
    align-self: flex-end;
    border: 0.1rem solid color(border-main);
    border-top-left-radius: $gx-message-radius;
    border-top-right-radius: 0;
    border-bottom-left-radius: $gx-message-radius;
    border-bottom-right-radius: 0;
    background-color: color(background-alt);
    background-color: white;

    &.messaging-message--isFirst {
      border-top-right-radius: $gx-message-radius;
    }

    &.messaging-message--isLast {
      border-bottom-right-radius: $gx-message-radius;
    }

    & + & {
      margin-top: (-(space(xs)));
    }

    .messaging-message__hour {
      justify-content: flex-end;

      .gx-icon {
        margin-left: space(sm);

        &--read {
          color: $gx-read-accent;
        }
      }
    }
  }

  &__sender {
    margin-bottom: space(sm);
    font-weight: 600;
    line-height: 2.2rem;
  }

  &__text {
    margin-bottom: space(sm);
    color: #5e5e5e;
  }

  &__reference {
    display: flex;
    align-items: center;
    float: left;
    color: #9f9f9f;
    font-size: 1.4rem;
  }

  &__hour {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    float: right;
    color: #9f9f9f;
    font-size: 1.4rem;

    .gx-icon {
      @include icon-size(md);
    }

    &--image {
      position: absolute;
      right: space(md);
      bottom: space(sm);
      @include z-index(base, 1);

      span {
        color: white;
      }
    }

    &--visit {
      justify-content: flex-end;
      padding: space(sm) space(md) 2rem;
    }
  }
}

.messaging-noUserMail {
  padding: space(lg);
  color: color(content-low);
  text-align: center;
}

.messaging-reInfo {
  display: flex;
  margin-bottom: space(md);

  &--disabled {
    position: relative;

    &:after {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: white;
      opacity: 0.7;
      content: '';
    }
  }

  .annuncio-image {
    align-self: flex-start;
    width: auto;
    max-width: 7rem;
    height: auto;
    max-height: 6rem;
  }

  &--visit {
    .annuncio-image {
      max-width: 5rem;
      max-height: 4rem;
    }
  }

  &__content {
    margin-left: space(sm);
    @include typography(body-tiny);
  }

  &__rif {
    color: color(content-action);
  }
}

.messaging-reStatus {
  width: 100%;
  margin-bottom: space(md);
}

// Hacks to have pages always at 100% of height
#container {
  &.fixed-height {
    height: 100vh;
    min-height: 0;

    .gx-side-menu {
      height: calc(100vh - #{$gx-header-size});
      min-height: 0;
      overflow: hidden;

      &__nav {
        height: 100%;
        overflow: auto;
        -ms-overflow-style: none;

        &::-webkit-scrollbar {
          display: none !important;
          width: 0 !important;
        }
      }
    }

    .gtx-ah-footer-spacing,
    .gtx-ask-help {
      display: none;
    }

    #content-wrapper {
      height: calc(100vh - #{$gx-new-menu-header-height});
      min-height: 0;
    }

    #content {
      height: 100%;
    }
  }
}

.messaging--unread {
  margin: space(md);
  text-align: center;
}

.messaging-attachments {
  display: flex;
  flex-wrap: wrap;

  .gx-tag {
    margin-right: space(xs);
    margin-bottom: space(sm);
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    width: calc(100% + #{$gx-unit-size * 6});
    margin-left: -(space(2xl));
  }
}

.messaging-messageAttachment {
  display: flex;
  align-items: center;

  &__icon {
    flex-shrink: 0;
    padding-right: space(sm);
    border-right: 0.1rem solid color(border-main);
    color: color(content-low);
    font-size: ($gx-unit-size * 2.5);
  }

  &__data {
    padding-left: space(sm);
  }

  &__dataName {
    max-width: 17.5rem;
    font-size: ($gx-unit-size * 1.5);
    font-weight: 700;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;
  }

  &__dataView {
    color: color(content-low);
    font-size: ($gx-unit-size * 1.5);
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

// List

.gx-table__body .messaging-listMessage {
  cursor: pointer;

  &:hover {
    background-color: $messaging-accent;
  }
}

.gx-table__field--checkbox {
  width: 2rem;

  .styled-checkbox .styled-check {
    width: 2rem;
    height: 2rem;
    cursor: default;
  }
}

.messaging-listMessage {
  &--unread {
    background-color: $messaging-accent;

    & > td {
      background-color: $messaging-accent;
    }

    .gx-table-new__cell {
      font-weight: 700;
    }
  }

  &__ref {
    .gx-list {
      .messaging-listMessage--unread & {
        font-weight: 700;
      }
    }
  }

  &__statusIcon {
    display: flex;
    justify-content: center;
    color: color(content-low);

    &--unread {
      color: color(content-action);

      .gx-icon {
        fill: currentColor;
      }
    }

    .gx-icon {
      margin: 0;
      fill: currentColor;
      @include icon-size(md);
    }
  }

  &__contact {
    display: flex;
    align-items: center;

    @include media('screen', '<#{breakpoint(sm)}') {
      @include typography(body-small);
    }

    .gx-notification-badge {
      margin-left: space(sm);
    }
  }

  &__preview {
    color: color(content-high);
    word-break: break-all;
    width: 35rem;

    span:not(.messaging-preview-date) {
      color: color(content-low);
    }

    // Capitalize moment date
    .messaging-preview-date {
      text-transform: capitalize;
    }
  }

  &__ref {
    display: flex;
    align-items: center;

    @include media('screen', '<#{breakpoint(md)}') {
      padding-top: space(md);
    }

    // @include media('screen', '<#{breakpoint(sm)}') {
    //   .list__field__thumb {
    //     flex-shrink: 0;
    //     width: 6.9rem;
    //     height: 5rem;
    //   }
    // }

    .list__field__thumb {
      margin-right: space(sm);
      flex-shrink: 0;
    }
  }

  &__actions {
    display: flex;
    margin-top: space(md);

    .gx-button:first-child {
      width: 100%;
    }

    @include media('screen', '<#{breakpoint(md)}') {
      .gx-dropupButton {
        margin-left: space(sm);
      }
    }
  }
}

.messaging-iconReply {
  display: inline-flex;
  position: relative;

  @include media('screen', '<#{breakpoint(sm)}') {
    display: flex;
  }

  img {
    position: absolute;
    top: -0.6rem;
    right: -0.6rem;
    fill: #4cae4c;
  }
}

.messaging-card {
  padding: space(md);
  border-bottom: 0.1rem solid color(border-main);
  background-color: white;
  cursor: pointer;
  width: 100%;

  .generic-content-block {
    color: color(content-high);
  }

  &--unread {
    background-color: $messaging-accent;
    font-weight: 700;
  }

  & + & {
    margin-top: space(sm);
    border-top: 0.1rem solid color(border-main);
  }

  &__topbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 0 space(sm);
    border-bottom: 0.1rem solid color(border-main);
    @include typography(body-small);
  }

  .messaging-listMessage__statusIcon {
    margin-right: space(md);
  }

  &__info {
    display: flex;
    align-items: center;

    .styled-checkbox {
      margin-right: space(md);

      .styled-check {
        width: 2rem;
        height: 2rem;
      }
    }
  }
}

@include media('screen', '<#{breakpoint(md)}') {
  #container {
    &.fixed-height {
      height: var(--height);

      #content-wrapper {
        height: calc(100% - #{$gx-unit-size * 9});
      }
    }
  }

  .list__field--messages {
    display: table-cell;
    width: auto;
    padding: space(md);
  }

  .list__item--messages {
    display: table-row;
  }

  .gx-head-section-dropdown {
    @include z-index(navigation, 2);
  }

  // Details
  .fixed-height {
    .gx-head-section {
      position: relative;
      padding: 0 space(md) 0 0;
      border: 0;
      background-color: color(background-main);
      box-shadow: elevation(fixed-top);
      @include z-index(navigation, 1);

      @include media('screen', '<#{breakpoint(md)}') {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        background-color: color(background-main);
      }

      &__actions {
        .gx-button {
          margin: 0;
        }
      }

      &__back {
        position: static;
      }

      &__title,
      &__actions {
        display: flex !important;
      }

      &__title {
        @include media('screen', '<#{breakpoint(sm)}') {
          display: flex !important;
        }
      }
    }
  }

  .messages {
    // List
    .gx-filtersWrapper {
      .filter-box__actions {
        justify-content: flex-end;
      }

      .filter-box__section {
        display: flex;

        @include media('screen', '<=#{breakpoint(sm)}') {
          margin-top: 0;
        }
      }

      .filter-box__section__item {
        margin: 0;
        margin-right: space(md);

        @include media('screen', '<=#{breakpoint(sm)}') {
          width: 100%;

          .gx-checkbox {
            width: 100%;
            margin-top: space(sm);
          }
        }
      }

      button {
        width: auto;
      }
    }

    .list__item + .list__item {
      border-top: 0.1rem solid color(border-main);
    }

    .list__head {
      background-color: #fafafa;
    }

    .list {
      display: table;
    }

    .list__body {
      display: table-row-group;
    }
  }
}

@include media('screen', '<#{breakpoint(md)}') {
  .fixed-height {
    .gtx-ask-help {
      display: none;
    }
  }
}

@include media('screen', '<#{breakpoint(sm)}') {
  // Details
  .fixed-height {
    .gx-head-section {
      .gx-title-2 {
        font-size: 1.6rem;
      }

      &__actions {
        .gx-button {
          flex-grow: 0;
          margin-left: auto;
        }
      }
    }
  }

  .messages {
    // List
    .gx-filtersWrapper {
      .filter-box__actions {
        justify-content: flex-end;
      }

      .filter-box__section {
        flex-direction: column;
      }

      .filter-box__section__item {
        margin-right: 0;
      }

      .filter-box__section__item + .filter-box__section__item {
        margin-top: space(md);
      }

      button {
        width: 100%;
      }
    }
  }
}

//PhotoViewer
.messaging-threadPhotoViewer {
  display: flex;
  position: fixed;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: color(background-main);
  overflow: hidden;
  @include z-index(navigation, 2);
  inset: 0;

  &__header {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: space-between;
    height: ($gx-unit-size * 7);
    padding: 0 space(lg);
    border-bottom: 0.1rem solid color(border-main);
    font-size: 1.8rem;

    span {
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;

      @include media('screen', '<=#{breakpoint(sm)}') {
        max-width: 20rem;
      }
    }

    &Close {
      cursor: pointer;
    }

    .gx-icon {
      color: color(content-low);
    }
  }

  &__content {
    display: flex;
    align-items: center;
    justify-content: center;
    height: calc(100% - (#{$gx-unit-size * 7}));

    img {
      max-width: 100%;
      max-height: 100%;
    }
  }
}

.messaging-messageVisitContent {
  display: flex;
  padding: space(md) space(md) 0;

  &__icon {
    flex-shrink: 0;
    padding: 0 space(sm);
    border-right: 0.1rem solid color(border-main);
    color: #9f9f9f;
    font-size: 2rem;
  }

  &__info {
    padding-left: space(sm);
    color: color(content-high);
    font-size: 1.2rem;

    &--modal {
      font-size: 1.4rem;

      .messaging-reInfo__content {
        font-size: 1.4rem;
      }

      .annuncio-image {
        max-width: 8rem;
        max-height: 6.4rem;
      }
    }

    ul {
      margin: 0;
      padding: 0;
      list-style: none;

      li {
        text-transform: capitalize;
      }
    }

    p:last-of-type {
      strong {
        display: block;
      }
    }
  }

  &__infoDate {
    margin: space(sm) 0;
    font-size: ($gx-unit-size * 2);
    font-weight: 700;
    text-transform: capitalize;
  }

  &__infoAction {
    margin-bottom: space(sm);
  }

  &__infoProperty {
    margin-top: space(md);
  }

  &__infoNotes {
    margin-top: space(sm);

    p {
      font-size: ($gx-unit-size * 2);
    }
  }

  &__infoLink {
    word-break: break-all;
  }
}

.messaging-messageVisitAction {
  padding: 2rem space(md);
}

.message-thread-action-preferredActive {
  > a > .gx-icon {
    color: color(content-action);
  }
}

.messaging-filter--noMargin {
  margin-top: auto;
}

.messaging-filter-preferred {
  width: 100%;
}

.messaging-select-tabs,
.messaging-search-form {
  padding: space(sm) space(md);
  border-bottom: 0.1rem solid color(border-main);
}

.messaging-search-form {
  .gx-input-wrapper {
    flex-grow: 1;
  }

  .gx-button {
    flex-grow: 0;
  }
}

.messaging-remove-filters {
  display: flex;
  align-items: center;
  margin-left: space(md);
  color: color(content-action);
  font-size: 1.4rem;
  font-weight: 600;
  cursor: pointer;
}

.messaging-search-results {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: space(sm) space(md) space(sm) space(xl);
  background-color: color(background-brand-alt);
  border-bottom: 0.1rem solid color(border-main);

  @include media('screen', '<#{breakpoint(sm)}') {
    padding: space(sm) 0 space(sm) space(md);
  }

  span {
    color: color(content-action);
  }
}

.messaging-table-head {
  height: ($gx-unit-size * 7);
  @include z-index(base, 4);

  &--invisible {
    border-bottom: 0;
    opacity: 0;
    visibility: hidden;
  }
}

.messaging-contact-info {
  &__list {
    margin: 0;
    padding: 0;
    list-style: none;

    li {
      display: flex;
      align-items: center;
      margin-bottom: space(md);
      font-size: 1.4rem;
      word-break: break-all;
    }

    .gx-icon {
      flex-shrink: 0;
      margin-right: space(sm);
      color: color(background-brand);
      @include icon-size(md);
    }
  }

  &__requests {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    padding: 0 2rem;
    border-radius: radius(sm);
    font-size: 1.2rem;

    span {
      color: color(content-medium);
      font-weight: 400;
      text-transform: uppercase;
    }

    strong {
      margin-right: space(xs);
    }

    .gx-icon {
      color: color(background-brand);
      @include icon-size(md);
    }
  }

  .gx-button {
    margin-top: space(sm);
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    padding: space(md);
  }
}

.messaging-searching-for {
  &__button {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: ($gx-unit-size * 5);
    margin-top: space(md);
    padding: space(sm) space(md);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    cursor: pointer;

    & > div {
      display: flex;
      align-items: center;
    }

    strong {
      margin-right: space(sm);
      color: color(content-action);
      font-size: 2rem;
    }

    span {
      font-size: 1.4rem;
    }

    .gx-icon {
      color: color(background-brand);
    }
  }
}

.messaging-notes {
  &__add {
    margin-bottom: space(sm);
  }

  &__note {
    margin-top: space(sm);
    padding: space(md);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
  }

  &__noteHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div:not(.gx-buttonGroup) {
      display: flex;
      align-items: center;

      svg {
        @include icon-size(md);
      }
    }

    .gx-icon {
      color: color(background-brand);
    }

    span {
      margin-left: space(sm);
    }

    .gx-button {
      margin-left: 0;
    }
  }

  &__noteBody {
    margin-top: space(sm);
  }
}

// Properties Sidebar (Proponi, Incroci, Proposti)
.messaging-properties-sidebar {
  display: flex;
  position: absolute;
  top: 0;
  left: 0;
  flex: 1;
  flex-direction: column;
  width: 100%;
  height: 100%;
  height: 100%;
  transform: translateX(100%);
  transition: transform 0.25s ease-in-out;
  background-color: color(background-main);
  @include z-index(base, 1);

  &--open {
    transform: translateX(0);
    transition: transform 0.25s ease-in-out;
  }

  &__header {
    display: flex;
    align-items: center;
    padding: 0 space(lg);
    box-shadow: 0 -0.1rem 0 color(border-main) inset;

    .gx-button {
      margin-right: space(md);
    }
  }

  &__intro {
    padding: space(lg);
    border-bottom: 0.1rem solid color(border-main);

    p {
      margin: 0;
      font-size: 1.4rem;
      line-height: 2.4rem;
    }
  }

  &__search {
    padding: space(md) space(lg);
    border-bottom: 0.1rem solid color(border-main);
  }

  &__body {
    flex: 1;
    height: 100%;
    overflow: auto;
  }

  &__list {
    padding: space(lg) space(lg) 9.6rem;
  }

  &__row {
    display: flex;
    align-items: center;

    & + & {
      margin-top: space(md);
    }

    .gx-property-item {
      max-width: calc(100% - #{$gx-unit-size * 6});
    }

    .styled-checkbox {
      margin-right: space(md);
    }

    .styled-check {
      width: 2rem;
      height: 2rem;
    }
  }

  &__actions {
    display: flex;
    position: absolute;
    bottom: 0;
    left: 0;
    justify-content: center;
    width: 100%;
    padding: space(md) space(lg);
    border-top: 0.1rem solid color(border-main);
    background-color: color(background-main);
  }
}

.messaging-date-col {
  min-width: 9.6rem;
}

.messaging-preview-col {
  width: 40rem;
  padding-right: space(xl);
  white-space: normal;

  .gx-table__cell {
    display: block;
  }
}

.messaging-preview-attachment {
  .gx-icon {
    margin-right: space(xs);
  }
}
