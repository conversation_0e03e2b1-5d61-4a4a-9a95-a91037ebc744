@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.auth2-box {
  position: absolute;
  top: 2rem;
  left: 50%;
  width: 100%;
  max-width: 48rem;
  padding: space(md);
  transform: translateX(-50%);

  .gx-button {
    margin-top: space(md);
  }
}

.a2f-footer {
  margin-top: space(md);
}

.gx-input-password-recovery {
  [data-role='spinner'] {
    margin-left: space(sm);
  }
}

[data-role='error-message'] {
  margin-bottom: space(sm);
}

.auth2-resend {
  margin-bottom: space(md);
}

.auth2-box__logo {

  img {
    width: 24rem;
    height: 4rem;
    margin: 0 auto;
    object-fit: contain;
    object-position: center bottom;
  }
}

.auth2-box__title {
  margin: 5rem 0 space(sm);
  @include typography(title-1);
  color: color(content-medium);
}

.auth2-box__text {
  @include typography(title-2);
  color: color(content-medium);
}

.auth2-box__help {
  text-decoration: underline;
  display: inline-block;

  &:hover {
    text-decoration: none;
  }
}

@include media('screen', '>=medium') {
  .auth2-box {
    top: 8rem;
  }
}