@use '../../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.estimate-price {
  display: flex;
  width: 100%;
  margin-bottom: 2.4rem;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  &__item {
    flex: 1 0 auto;
    text-align: center;

    &Label {
      color: color(content-medium);
      font-size: 1.2rem;
      text-transform: uppercase;
    }

    &Value {
      height: space(xl);
      font-size: 1.6rem;
      line-height: space(xl);
    }

    &--inEvidence {
      flex-basis: 100%;

      .estimate-price__itemValue {
        font-size: 2.4rem;
        font-weight: 600;
      }
    }
  }

  @include media('screen', '<#{breakpoint(sm)}') {
    flex-wrap: wrap;

    &__item {
      order: 2;
      margin: space(md) 0;
      padding: 0 space(md);

      & + &:not(.estimate-price__item--inEvidence) {
        border-left: 0.1rem solid color(border-main);
      }

      &--inEvidence {
        order: 1;
        margin: 0;
        padding: space(md);
        border-bottom: 0.1rem solid color(border-main);
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    &__item {
      padding: space(md);

      & + & {
        border-left: 0.1rem solid color(border-main);
      }

      &--inEvidence {
        flex-basis: 40%;
      }
    }
  }
}

.user-search-card {
  width: 100%;
  padding: space(md) space(md) 0;
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  & + & {
    margin-top: space(md);
  }

  &__title {
    margin-bottom: space(md);
    font-weight: 600;
  }

  &__map {
    max-width: 14rem;
    margin-bottom: space(md);
  }

  &__badges {
    margin-bottom: space(sm);

    .gx-badge {
      margin: 0 space(sm) space(sm) 0;
    }

    .show-more {
      color: color(content-action);
      font-weight: 600;
      cursor: pointer;
    }
  }

  .popover-link {
    color: color(content-action);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    padding: space(lg) space(lg) space(sm);

    & + & {
      margin-top: space(lg);
    }

    &__map {
      max-width: 16rem;
      margin-right: space(lg);
      margin-bottom: space(lg);
      float: left;
    }

    &__badges {
      width: calc(100% - 18.4rem);
      float: left;
    }

    &__list {
      clear: both;
    }
  }
}

// Nuova pagina valutazione

%item-child {
  display: flex;
  align-items: center;
  height: 4rem;
}

.gx-surfaces-box {
  margin-bottom: space(xl);
  padding: space(md);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  &__item {
    position: relative;

    & + & {
      padding-top: space(md);
      border-top: 0.1rem solid color(border-main);
    }

    &Label {
      @extend %item-child;
      margin-bottom: space(md);
    }

    &Field {
      display: flex;
      justify-content: space-between;
      margin-bottom: space(md);

      &::before {
        width: calc(50% - #{space(sm)});
        color: color(content-low);
        text-transform: uppercase;
        content: attr(data-field-label);
        @extend %item-child;
      }
    }

    &Value {
      width: calc(50% - #{space(sm)});

      &--commercial {
        @extend %item-child;
      }
    }

    &Delete {
      position: absolute;
      top: space(md);
      right: 0;
    }
  }

  &__footer {
    display: flex;

    @include media('screen', '<#{breakpoint(sm)}') {
      position: relative;
      flex-wrap: wrap;

      .gx-dropupButton {
        width: 100%;

        .gx-button {
          flex-grow: 1;
        }
      }

      &Summary {
        width: 100%;
        margin-top: space(lg);

        > div {
          display: flex;
          justify-content: space-between;

          & + div {
            margin-top: 0.8rem;
          }

          span {
            width: calc(50% - 0.8rem);
          }
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-bottom: space(2xl);
    padding: space(md) space(xl);

    &__head {
      display: flex;
      justify-content: space-between;
      margin-bottom: space(sm);
      padding: 0 7.2rem 0 calc(25% - #{space(sm)});

      span {
        width: calc(#{percentage(1 / 3)} - #{space(lg)});
        color: color(content-low);
        text-transform: uppercase;
      }
    }

    &__item {
      display: flex;
      justify-content: space-between;
      padding-right: 7.2rem;

      &Label {
        width: calc(25% - 2.4rem);
      }

      &Field {
        width: calc(25% - 2.4rem);

        &::before {
          content: none;
        }
      }

      &Value {
        width: 100%;
      }
    }

    &__footer {
      justify-content: space-between;
      padding-top: space(md);
      padding-right: 7.2rem;
      border-top: 0.1rem solid color(border-main);

      &Summary {
        width: calc(50% - #{space(md)});

        > div {
          display: flex;
          justify-content: space-between;

          span {
            width: calc(50% - #{space(md)});

            &:first-child {
              text-align: right;
            }
          }
        }
      }
    }
  }
}

.evaluation-block {
  margin-bottom: space(xl);
  padding: space(md);
  border: 0.1rem solid color(border-main);
  border-radius: radius(sm);

  &__item {
    position: relative;

    & + & {
      padding-top: space(md);
      border-top: 0.1rem solid color(border-main);
    }

    &Label {
      @extend %item-child;
      margin-bottom: space(md);
    }

    &Field {
      display: flex;
      justify-content: space-between;
      margin-bottom: space(md);

      .evaluation-block__item--fixed & {
        height: ($gx-unit-size * 11);
      }
    }

    &Value {
      width: 100%;

      .gx-input-group {
        width: calc(100% - #{space(4xl)});
      }
    }

    &Price {
      display: flex;
      position: absolute;
      right: 0;
      bottom: 0;
      align-items: center;
      justify-content: flex-end;
      width: ($gx-unit-size * 10);
      height: ($gx-unit-size * 5);
    }
  }

  &__footer {
    display: flex;

    > span {
      color: color(content-low);
    }

    @include media('screen', '<#{breakpoint(sm)}') {
      position: relative;
      flex-wrap: wrap;

      &Action {
        width: 100%;
        margin-top: space(sm);
      }

      .gx-dropupButton {
        width: 100%;

        .gx-button {
          flex-grow: 1;
        }
      }

      &Summary {
        width: 100%;
        margin-top: space(lg);

        > div {
          display: flex;
          justify-content: space-between;

          & + div {
            margin-top: 0.8rem;
          }

          span {
            width: calc(50% - 0.8rem);
          }
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    margin-bottom: space(2xl);
    padding: space(md) space(xl);

    &__head {
      display: flex;
      justify-content: space-between;
      margin-bottom: space(sm);
      padding: 0 7.2rem 0 calc(25% - #{space(sm)});

      span {
        width: calc(#{percentage(1 / 3)} - #{space(lg)});
        color: color(content-low);
        text-transform: uppercase;
      }
    }

    &__item {
      display: flex;
      align-items: flex-start;
      justify-content: space-between;

      &:first-child {
        align-items: center;

        .evaluation-block__itemLabel {
          margin-top: 0;
        }
      }

      &Label {
        width: calc(25% - #{$gx-unit-size * 3});
        margin-top: 2.5rem;
      }

      &Field {
        width: calc(25% - #{$gx-unit-size * 3});
      }

      &Value {
        .gx-input-group {
          width: 100%;
        }
      }

      &Price {
        position: static;
        justify-content: flex-start;
        width: auto;
        height: auto;
        margin-top: space(sm);
      }
    }

    &__footer {
      align-items: center;
      justify-content: space-between;
      padding-top: space(md);
      border-top: 0.1rem solid color(border-main);

      &Summary {
        width: calc(50% - #{space(md)});

        > div {
          display: flex;
          justify-content: space-between;

          span {
            width: calc(50% - #{space(md)});

            &:first-child {
              text-align: right;
            }
          }
        }
      }
    }
  }

  @include media('screen', '>=#{breakpoint(md)}') {
    &__item {
      &Label {
        width: 13%;
      }

      &Field {
        width: calc(29% - #{$gx-unit-size * 3});
      }
    }
  }
}

.agency-estimates {
  &-edit,
  &-add,
  &-detail {
    //TODO: fixare, rompe height step 4/5
    // .gtx-ah-footer-spacing {
    //   height: auto;
    // }

    > div + .gtx-ask-help {
      bottom: 7.2rem;

      @include media('screen', '>=#{breakpoint(md)}') {
        bottom: ($gx-unit-size * 11);
      }

      @include media('screen', '>=#{emConverter(1600)}') {
        bottom: space(lg);
      }
    }
  }

  &-list #root + .gtx-ask-help {
    @include media('screen', '<#{breakpoint(sm)}') {
      & + .gtx-ah-footer-spacing {
        background-color: color(background-alt);
      }
    }
  }
}

// Dettaglio

$estimate-bar-background: #a5d5fa;
$estimate-bar-border: #4c93cb;
$estimate-bar-size: (space(md));
$estimate-bar-size-light: (space(xs));
$estimate-box-size: ($gx-unit-size * 12);
$estimate-box-size-light: ($gx-unit-size * 7);
$estimate-box-maxWidth: 17rem;

$root-estimate-box: estimate-box;

.#{$root-estimate-box} {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;

  & + & {
    margin-top: space(xl);
  }

  &__label {
    color: color(content-low);
    font-size: 1.2rem;
    text-transform: uppercase;
  }

  &__pricePrimary {
    font-size: 2rem;
    font-weight: 600;
    color: color(content-high);
  }

  &__priceSecondary {
    font-size: 1.4rem;
  }

  &__wrapBar {
    flex-grow: 1;
    width: 100%;
    height: $estimate-bar-size;
    margin: (($estimate-box-size - $estimate-bar-size) / 2) 0 (($estimate-box-size - $estimate-bar-size) / 2) 0;
    border: 0.1rem solid $estimate-bar-border;
    border-radius: radius(sm);
    background-color: $estimate-bar-background;
  }

  &__bar {
    position: relative;
    width: calc(100% - #{$estimate-box-maxWidth} - #{$estimate-bar-size / 2});
    height: 100%;
    margin: 0 auto;
  }

  &__priceEst {
    display: flex;
    position: absolute;
    top: 50%;
    left: 50%; //IE11
    left: calc((var(--priceEst) - var(--priceMin)) / (var(--priceMax) - var(--priceMin)) * 100%);
    flex-direction: column;
    justify-content: center;
    max-width: $estimate-box-maxWidth;
    height: $estimate-box-size;
    padding: space(md);
    transform: translate(-50%, -50%);
    transition: all 0.5s ease-in-out;
    border: 0.1rem solid $estimate-bar-border;
    border-radius: radius(sm);
    background: #ffffff;
    text-align: center;

    .#{$root-estimate-box} {
      &__pricePrimary {
        color: color(content-action);
        font-size: 2.4rem;
      }

      &__priceSecondary {
        color: color(content-action);
      }
    }
  }

  &__price {
    margin-top: space(md);

    &--max {
      text-align: right;
    }
  }

  &--rent {
    .#{$root-estimate-box} {
      &__priceEst {
        left: 50%; //IE11
        left: calc((var(--priceRentEst) - var(--priceRentMin)) / (var(--priceRentMax) - var(--priceRentMin)) * 100%);
      }

      &__wrapBar {
        height: $estimate-bar-size-light;
        margin: (($estimate-box-size-light - $estimate-bar-size-light) / 2) space(md)
          (($estimate-box-size-light - $estimate-bar-size-light) / 2) space(md);
        border: none;
      }

      &__priceEst {
        height: $estimate-box-size-light;
        padding-top: space(sm);
        padding-bottom: space(sm);

        .#{$root-estimate-box}__priceSecondary {
          font-weight: 600;
        }
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    align-items: center;

    &__wrapBar {
      width: auto;
      margin-right: space(md);
      margin-left: space(md);
    }

    &__price {
      margin-bottom: space(md);

      &--min {
        order: -1;
      }
    }

    &--rent {
      .#{$root-estimate-box}__price {
        margin-top: space(sm);
        margin-bottom: space(sm);
      }
    }
  }
}

// Estimate property info
.estimate-property-info {
  display: flex;
  flex-wrap: wrap;
  border-width: 0 0.1rem 0.1rem 0;
  border-style: solid;
  border-color: color(border-main);

  &__item {
    display: flex;
    align-items: center;
    width: 50%;
    padding: space(md);
    border-top: 0.1rem solid color(border-main);
    border-left: 0.1rem solid color(border-main);
    background-color: color(background-main);

    @include media('<#{breakpoint(sm)}') {
      &--fullWidth-xs {
        width: 100%;
      }
    }

    .gx-icon {
      flex-shrink: 0;
      margin-right: space(md);
      color: color(content-action);
      font-size: ($gx-unit-size * 4);

      use {
        fill: color(content-action) !important;
      }
    }

    &Content {
      display: flex;
      flex-direction: column;
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &__item {
      padding-top: space(lg);
      padding-bottom: space(lg);

      .gx-icon {
        font-size: ($gx-unit-size * 6);
      }
    }
  }
}

// Surface analysis
$root-surface-analysis: surface-analysis;

.#{$root-surface-analysis} {
  &__item {
    display: flex;
    padding: space(sm) 0;
    border-bottom: 0.1rem solid color(border-main);

    &Label {
      width: 50%;
    }

    &Value {
      padding-right: space(sm);
    }
  }

  &__sum {
    width: 100%;
    margin-top: space(sm);

    .#{$root-surface-analysis}__item {
      align-items: center;
      justify-content: flex-end;
      width: 100%;
      border-bottom: none;

      &Label,
      &Value {
        width: auto;
      }

      &Value {
        font-weight: 600;
      }

      &Label {
        margin-right: space(md);
      }
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    display: flex;
    flex-wrap: wrap;

    &__item {
      flex-grow: 1;
      justify-content: flex-start;
      width: 50%;

      &Label {
        width: ($gx-unit-size * 30);
      }

      &Value {
        width: ($gx-unit-size * 12);
        text-align: right;
      }
    }
  }
}

.gx-leaflet {
  height: 100%;
}

.gx-map-modal {
  .gx-modal__body {
    padding: 0;
    overflow-y: hidden;

    .gx-property-map {
      position: relative;
      width: 100%;
      height: 100%;
    }

    .gx-property-map-alert {
      position: absolute;
      right: 0;
      bottom: 0;
      left: 0;
      max-width: none;
      padding: space(md);
      background-color: color(background-main);
      @include z-index(base, 2);

      .content {
        color: color(content-medium);
        font-size: 1.2rem;
        font-weight: 600;
        text-align: left;
      }

      button {
        float: right;
      }
    }

    @include media('screen', '>=#{breakpoint(sm)}') {
      .gx-property-map {
        height: 40rem;
      }

      .gx-property-map-alert {
        right: 25%;
        bottom: 7%;
        left: 25%;
        @include z-index(base, 1);
      }
    }
  }

  .gx-modal__footer {
    position: relative;

    div {
      position: absolute;
      left: space(lg);
      padding-top: 0.8rem;
    }

    @include media('screen', '<=#{breakpoint(sm)}') {
      div {
        position: relative;
        left: 0;
        width: 100%;
        padding: space(sm);
        padding: space(md) 0;
        text-align: left;
      }

      button {
        width: 100%;
      }
    }
  }
}

//Similar Properties

.alert-properties {
  margin-top: (-(space(sm)));

  @include media('>=#{breakpoint(sm)}') {
    margin-top: -2.2rem;
  }
}

.similar-properties {
  margin-bottom: space(md);

  %similar-properties-cell {
    display: inline-flex;
    align-items: center;
    justify-content: center;
  }

  &__head {
    display: flex;
    justify-content: space-between;
    padding: space(md) space(xl);
    border-bottom: 0.1rem solid color(border-main);
    background-color: color(background-alt);

    &Selected {
      display: flex;
      position: absolute;
      top: 5.6rem;
      left: 3rem;
      align-items: center;
      height: ($gx-unit-size * 5);

      @include media('screen', '>=#{breakpoint(sm)}') {
        top: 7.2rem;
      }

      > div {
        margin-right: space(xs);
      }
    }

    &Property {
      width: calc(100% - 9.6rem - 20rem);

      .gx-badge {
        margin-right: space(xs);
      }
    }

    &Affinity {
      width: 9.6rem;
      @extend %similar-properties-cell;
    }

    &Date {
      width: 20rem;
      @extend %similar-properties-cell;
    }
  }

  &__item {
    padding: space(md) 0;
    border-bottom: 0.1rem solid color(border-main);
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    border: 0.1rem solid color(border-main);

    &__item {
      display: flex;
      padding-right: space(xl);
      padding-left: space(xl);

      &:last-child:not(:only-child) {
        border-bottom: 0;
      }
    }

    &__property {
      width: calc(100% - 9.6rem - 20rem);

      &Check {
        margin-right: space(md);
      }
    }

    &__affinity {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 9.6rem;
    }

    &__date {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20rem;
    }
  }

  @include media('<#{breakpoint(sm)}') {
    &__head {
      display: none;

      &Affinity,
      &Date {
        display: none;
      }
    }

    &__affinity,
    &__date {
      display: none;
    }
  }

  &-modalHead {
    display: flex;
    justify-content: flex-end;
    margin: (- space(md)) (- space(md)) 0;
    padding: space(sm) space(lg);
    border-bottom: 0.1rem solid color(border-main);

    @include media('>=#{breakpoint(sm)}') {
      margin: (- space(md)) (- space(xl)) 0;

      .gx-alert + & {
        margin-top: (- space(md));
      }
    }
  }

  .gx-modal__body & {
    height: 40rem;
    margin: 0 (- space(xl)) (- space(md));
    border-top: none;
    border-right: none;
    border-bottom: none;
    border-left: none;
    overflow-y: auto;

    @include media('<#{breakpoint(sm)}') {
      height: calc(100vh - 17.8rem);
      margin-right: (- space(md));
      margin-bottom: 0;
      margin-left: (- space(md));
      background-color: color(background-alt);

      &__head {
        display: block;
        height: 0;
        padding: 0;
        border: none;

        &Selected {
          left: space(md);
        }

        &Affinity,
        &Date {
          display: block;
        }

        & + .similar-properties__item {
          margin-top: 0;
          border-top: none;
        }
      }

      &__item {
        display: flex;
        position: relative;
        flex-wrap: wrap;
        padding-right: 1.8rem;
        padding-left: 5rem;
        border-top: 0.1rem solid color(border-main);
        background-color: color(background-main);

        & + .similar-properties__item {
          margin-top: space(md);
        }
      }

      &__property {
        width: 100%;
        margin-bottom: 1.6rem;
        padding-bottom: 1.6rem;
        border-bottom: 0.1rem solid color(border-main);

        &Check {
          position: absolute;
          top: 50%;
          left: 1.8rem;
          transform: translateY(-50%);
        }
      }

      &__affinity,
      &__date {
        display: block;
        width: 50%;

        &::before {
          display: block;
          color: color(content-low);
          font-size: typography-scale(size-10);
          text-transform: uppercase;
          content: attr(label) '';
        }
      }
    }
  }
}

.alert-properties + .similar-properties-modalHead + form .similar-properties__headSelected {
  top: 7.2rem;
}

// Report cover

.report-cover {
  &__preview {
    &Wrap {
      position: relative;
      width: 50%;
      width: 14.4rem;
      height: 20.4rem;
      overflow: hidden;

      img {
        position: absolute;
        top: 50%;
        left: 50%;
        width: auto;
        height: 100%;
        transform: translate(-50%, -50%);
      }

      &--title--top {
        position: absolute;
        top: 20%;
        left: 50%;
        width: 72%;
        padding: space(sm);
        transform: translate(-50%, -50%);
        background-color: color(background-main);
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &--title--center {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 72%;
        padding: space(sm);
        transform: translate(-50%, -50%);
        background-color: color(background-main);
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &--title--bottom {
        position: absolute;
        top: 80%;
        left: 50%;
        width: 72%;
        padding: space(sm);
        transform: translate(-50%, -50%);
        background-color: color(background-main);
        text-align: center;
        text-overflow: ellipsis;
        overflow: hidden;
      }

      &.gx-loader-overlay + img {
        opacity: 0.8;
      }

      &.gx-loader-overlay {
        width: 95%;
        height: 98%;
      }
    }
  }

  &__edit {
    margin-top: space(lg);

    &Position {
      label {
        display: block;
      }
    }

    &Image {
      margin-top: space(lg);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    display: flex;

    &__preview {
      flex-shrink: 0;
      margin-right: space(xl);

      &Wrap {
        width: 100%;
        width: 20rem;
        height: 25.8rem;
        border: 0.1rem solid color(border-main);
      }
    }

    &__edit {
      width: calc(100% - 23.2rem);
      margin-top: 0;

      .gx-multiButton {
        display: flex;
      }
    }
  }
}

// Cover select
.cover-select {
  display: flex;
  justify-content: space-between;
  margin-bottom: space(lg);

  &__nav {
    display: flex;
    flex-shrink: 0;
    align-items: center;
    justify-content: center;
    height: 16.8rem;
    border: 0.1rem solid color(border-main);

    &:hover {
      background-image: linear-gradient(to top, rgba(0, 0, 0, 0.06), rgba(0, 0, 0, 0.06));
    }

    .gx-icon {
      color: color(content-action);
      @include icon-size(md);
    }

    &--prev {
      border-radius: radius(sm) 0 0 radius(sm);
    }

    &--next {
      border-radius: 0 radius(sm) radius(sm) 0;
    }
  }

  .gx-loader-overlay {
    background: none;
  }

  &__select {
    flex-grow: 1;
    border-top: 0.1rem solid color(border-main);
    border-bottom: 0.1rem solid color(border-main);
    background: color(background-alt);
    overflow: hidden;

    &--active {
      background-color: #e5e5e5;
    }

    > .gx-loader-overlay {
      top: 3.5rem;
      background: none;
    }

    &Items {
      min-width: 46.4rem; //TO-DO metterlo inline, va calcolato ogni volta
      padding: space(md) space(sm) space(sm) (space(md));
    }

    &Items--large {
      min-width: 55.2rem;
      padding: space(md) space(sm) space(sm) (space(md));
    }

    &Item {
      position: relative;
      width: space(4xl);
      height: space(3xl);
      margin: 0 space(sm) space(sm) 0;
      float: left;
      border: 0.1rem solid color(border-main);
      background-color: color(background-main);
      cursor: pointer;

      > .gx-icon {
        color: color(content-action);
        @include icon-size(md);
      }

      .img-delete {
        position: absolute;
        top: 0;
        right: 0;
        transform: translate(50%, -50%);
        display: flex;
        width: ($gx-unit-size * 2);
        height: ($gx-unit-size * 2);
        padding: 0;
        overflow: hidden;
        font-size: ($gx-unit-size * 2);
        background: color(background-main);
        border-radius: radius(rounded);
      }

      &--selected {
        box-shadow: 0 0 0 0.1rem color(border-selected);
      }

      &--sort {
        opacity: 0.8;

        &:hover {
          cursor: move;
        }
      }

      &--add {
        display: flex;
        align-items: center;
        justify-content: center;

        &:hover {
          background-image: linear-gradient(to top, rgba(0, 0, 0, 0.06), rgba(0, 0, 0, 0.06));
        }
      }

      .gx-badge {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.9rem;

        & > .gx-icon {
          fill: color(content-accent);
        }
      }

      .gx-loader-overlay ~ .gx-badge {
        display: none;
      }

      .gx-loader-overlay + img {
        opacity: 0.5;
      }

      .gx-loader-overlay {
        position: absolute;
        height: 3.2rem;
      }

      &NoImg {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        font-size: ($gx-unit-size * 3);

        span {
          text-align: center;
        }
      }
    }
  }

  &__img-wrap {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;

    img {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }

    .img-cover-vertical {
      width: 100%;
      height: auto;
    }

    .img-cover-horizontal {
      width: auto;
      height: 100%;
    }
  }
}

// Data agency box
$gx-data-agency-box-pic-width: 25.6rem;
$data-agency-box: data-agency-box;

.#{data-agency-box} {
  &__pic {
    position: relative;
    max-width: 40rem;
    height: auto;
    max-height: 40rem;
    margin-bottom: space(lg);

    img {
      width: 100%;
      height: auto;
    }

    .gx-button {
      position: absolute;
      top: space(sm);
      right: space(sm);
    }
  }

  &__agent {
    border: 0.1rem solid color(border-main);

    &Show {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: space(md);
    }

    &Detail {
      padding: space(md);
      border-top: 0.1rem solid color(border-main);

      .gx-box-row,
      .gx-title-2 {
        margin-bottom: space(md);
      }

      .gx-badge--info {
        margin-left: space(sm);
      }
    }

    &Comment {
      textarea.form-control {
        //TO-DO: modificare una volta tolto BootStrap
        height: space(4xl);
      }
    }

    &--detail {
      padding-bottom: space(lg);
      border-width: 0 0 0.1rem;

      svg {
        margin-right: space(sm);
        color: color(content-action);
        @include icon-size(md);
      }

      .#{data-agency-box}__contact {
        display: flex;
        align-items: center;
        margin-top: space(md);
      }
    }
  }

  &__agency {
    margin-top: space(lg);

    &Logo {
      width: space(4xl);
      height: space(4xl);
      margin-bottom: space(md);

      img {
        width: 100%;
        height: auto;
      }
    }

    &Info {
      .gx-title-2 {
        margin-bottom: space(sm);
      }
    }

    &Contacts {
      margin: space(md) 0 space(2xl);

      .gx-icon {
        margin-right: space(sm);
        color: color(content-action);
        @include icon-size(md);
      }
    }

    &Mail {
      margin-bottom: space(md);
    }
  }

  @include media('screen', '>=#{breakpoint(sm)}') {
    display: flex;

    &__pic {
      width: $gx-data-agency-box-pic-width;
      height: $gx-data-agency-box-pic-width;
      margin-right: space(xl);
    }

    &__info {
      flex-grow: 1;
    }

    &__agent {
      &Show {
        padding: space(md) space(xl);
      }

      &Detail {
        padding: space(md) space(xl) space(sm);
      }
    }

    &__agency {
      display: flex;

      &Logo {
        margin-right: space(md);
        margin-bottom: 0;
      }

      &Contacts {
        display: flex;
      }

      &Mail {
        margin-right: space(md);
        margin-bottom: 0;
      }
    }
  }
}

// Evaluation doc
.evaluation-doc {
  &__page {
    & + & {
      margin-top: space(xl);
    }
  }

  &__section {
    & + & {
      margin-top: ($gx-unit-size * 5);
    }

    &.only-print + & {
      margin-top: 0;
    }

    &Title {
      margin-bottom: space(lg);
    }
  }

  .gx-summary-list {
    &__item:last-child {
      margin-bottom: 0;
    }
  }
}

.only-print {
  display: none;
}

.gtx-list-heading {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 2rem;
  background-color: color(background-alt);
}

.gtx-cst-note-list .gx-table > tbody > tr > td {
  padding: 2rem;

  &:first-child {
    width: 2rem;
  }

  &:nth-child(2) {
    width: 9.6rem;
  }
}

.gtx-cst-no-notes {
  padding: space(sm);
}

// print-mode

//Variables
$page-width: 21cm;
$page-height: 29.5cm;
$page-horizontal-padding: 1.5cm;
$page-vertical-padding: 1cm;
$page-widthContent: ($page-width - ($page-horizontal-padding * 2));
$page-heightContent: ($page-height - ($page-vertical-padding * 2));

@page {
  size: A4;
  margin: 0;
}

.gx-container.print-mode {
  padding: 0;
}

.print-mode {
  .only-print {
    display: block;
  }

  .gx-map-box__image {
    height: 24rem;
  }

  .gx-summary-list__label {
    color: color(content-low) !important;
  }

  .evaluation-doc {
    counter-reset: page;

    &__page {
      display: flex;
      flex-direction: column;
      height: 29cm;
      padding: 1cm $page-horizontal-padding;
      page-break-before: always;

      & + .evaluation-doc__page {
        margin-top: 0;
      }

      &Head {
        display: flex;
        justify-content: space-between;
        margin-bottom: 1cm;
        padding-bottom: 0.25cm;
        border-bottom: 0.1rem solid color(border-main);
        font-size: 1rem;

        &::before {
          content: var(--agencyName);
        }

        &::after {
          content: var(--agencyContacts);
        }
      }

      &Foot {
        display: flex;
        justify-content: space-between;
        margin-top: auto;
        margin-top: auto;
        padding-top: 0.25cm;
        border-top: 0.1rem solid color(border-main);
        font-size: 1rem;

        &::before {
          content: 'Report del ' var(--lastUpdate);
        }

        &::after {
          content: counter(page);
          counter-increment: page;
        }
      }
    }

    &__section {
      &.only-print + .evaluation-doc__section {
        margin-top: 0.5cm;
      }

      & + .evaluation-doc__section {
        margin-top: 1cm;
      }

      &.only-print + & {
        margin-top: space(xl);
      }
    }
  }

  .#{$root-estimate-box} {
    align-items: center;

    &__wrapBar {
      width: auto;
      margin-right: space(md);
      margin-left: space(md);
      background-color: $estimate-bar-background !important;
    }

    &__price {
      margin-bottom: space(md);

      &Est {
        background-color: color(content-accent) !important;
      }

      &--min {
        order: -1;
      }
    }

    &--rent {
      .#{$root-estimate-box}__price {
        margin-top: space(sm);
        margin-bottom: space(sm);
      }
    }
  }

  .gx-head-section {
    display: none;
  }

  .gx-container {
    padding: 0;

    &--maxWidth {
      max-width: none;
    }

    &__footer {
      display: none;
    }
  }

  .estimate-property-info {
    &__item {
      width: percentage(1/3);

      .gx-icon {
        color: color(content-action) !important;
      }

      &Content {
        .gx-title-2 {
          font-size: 1.3rem;
        }
      }
    }
  }

  .#{$root-surface-analysis} {
    display: flex;
    flex-wrap: wrap;

    &__sum {
      .#{$root-surface-analysis}__item {
        justify-content: flex-end;
        width: 100%;
      }
    }

    &__item {
      flex-grow: 1;
      justify-content: flex-start;
      width: 50%;

      &Label {
        width: ($gx-unit-size * 30);
      }
    }
  }
}

#agencyEstimateFrame {
  display: none;
}

// Add report
.add-report {
  &Wrap {
    padding: space(lg);
  }

  &__button {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: space(lg);
    border: 0.1rem solid color(border-main);
    border-radius: radius(sm);
    color: color(content-medium);

    &Content {
      text-align: center;
    }

    & + & {
      margin-top: space(md);
    }

    b {
      display: block;
      margin-bottom: space(sm);
    }

    img {
      flex-shrink: 0;
      width: ($gx-unit-size * 9);
      height: ($gx-unit-size * 9);
      margin-bottom: space(md);
    }

    &:hover {
      color: color(content-medium);
      text-decoration: none;
    }

    &--selected {
      box-shadow: 0 0 0 0.2rem color(background-selected);
    }
  }

  @include media('>=#{breakpoint(sm)}') {
    &Wrap {
      padding: space(4xl) 0 0;
    }

    display: flex;
    justify-content: space-between;
    width: 68rem;
    margin: 0 auto;

    &__button {
      flex-direction: column;
      align-items: center;
      justify-content: space-between;
      width: calc(50% - #{space(md)});
      padding: space(xl);

      & + & {
        margin-top: 0;
      }

      &Content {
        display: flex;
        flex-direction: column;
        flex-grow: 1;
        align-items: center;

        p {
          margin-bottom: space(xl);
        }

        .gx-button {
          margin-top: auto;
        }
      }

      img {
        margin-right: 0;
        margin-bottom: space(md);
      }
    }
  }
}

.report-customer-form-notes {
  padding: space(sm);
  background-color: color(background-alt);
  border-bottom: 0.1rem solid color(border-main);
  display: flex;
  border-top-left-radius: radius(md);
  border-top-right-radius: radius(md);

  .gx-input-master-wrapper {
    width: 100%;
  }

  .gx-input-addon-wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }

  .gx-button {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    margin-left: -0.1rem;
  }
}

.report-customer-no-notes {
  text-align: center;
  color: color(content-low);
  padding: space(sm);
}

.gtx-cst-note-container .styled-checkbox .styled-check {
  width: 2rem;
  height: 2rem;
}

// TO-DO spostare su gx-design
.gx-dropdown--intoModal {
  @include z-index(modal, 1);
}

@import 'report-full';
