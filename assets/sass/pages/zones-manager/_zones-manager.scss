@use '../../variables/variables' as *;
@use '@gx-design/theme/styles' as *;
@use '@gx-design/tools/styles' as *;

.zones-manager-page {
  .gx-card {
    &--rounded {
      box-shadow: elevation(fixed-top);
      border: none;
      border-radius: radius(lg);
    }
  }

  & > .gx-container {
    & > .gx-card + .gx-card {
      margin-top: space(xl);
    }
  }
}

.zones-manager-intro {
  display: flex;
  justify-content: center;
  border-radius: radius(md);

  .gx-container {
    padding-left: 0;
    padding-right: 0;
  }

  @include media('<#{breakpoint(sm)}') {
    padding: space(xl) space(md) 0;
  }

  &__text {
    text-align: center;
    max-width: 55.5rem;
    margin: 0 auto space(3xl);

    .gx-badge {
      margin-bottom: space(lg);
    }

    h1 {
      @include typography(display-2);
    }

    p {
      @include typography(body);
      margin-bottom: space(lg);
      color: color(content-medium);
    }
  }

  &__benefit {
    background-color: color(background-main);
    border-radius: radius(md);
    padding: space(lg) 0;
    text-align: center;

    @include media('<#{breakpoint(sm)}') {
      margin-bottom: space(md);
    }

    img {
      margin: 0 auto space(lg);
      max-width: 100%;
    }

    h3 {
      @include typography(title-2);
      margin-bottom: space(sm);
    }

    p {
      @include typography(body-small);
      color: color(content-high);
      margin-bottom: 0;
      line-height: 2.1rem;
    }
  }
}

.zones-manager-packages {
  padding: 0 space(xl);
  display: flex;
  justify-content: center;

  @include media('<#{breakpoint(sm)}') {
    padding: 0;
  }

  .gx-container {
    width: 100%;

    @include media('<#{breakpoint(sm)}') {
      padding-left: 0;
      padding-right: 0;
    }
  }

  h2 {
    text-align: center;
  }

  &__info {
    margin: space(3xl) 0 space(3xl);

    @include media('<#{breakpoint(sm)}') {
      margin-top: space(xl);
    }

    &Package {
      text-align: center;
      padding: 0 space(md);

      img {
        margin: 0 auto space(md);
        max-width: 6.4rem;
      }

      h3 {
        margin-bottom: space(sm);
      }

      p {
        @include typography(body-small);
        color: color(content-high);
        margin-bottom: 0;
        line-height: 2.1rem;
      }

      @include media('<#{breakpoint(sm)}') {
        margin-top: 4rem;
      }
    }
  }

  &__container {
    display: flex;
    flex-wrap: wrap;
    height: 100%;
  }

  &__package {
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 33.33%;
    border-left: 1px solid color(border-main);
    position: relative;

    .zones-manager__coming-soon {
      position: absolute;
      top: -1.2rem;
      min-width: 12rem;

      @include media('<#{breakpoint(sm)}') {
        top: -2.8rem;
      }
    }

    @include media('<#{breakpoint(sm)}') {
      border-left: 0;
    }

    &:nth-child(2) {
      background-color: color(background-alt);
    }

    h3 {
      @include typography(display-subtitle);
      font-weight: 600;
    }
  }

  &__table {
    display: flex;
    justify-content: center;
    padding: 0;
    margin: space(xl) 0 space(lg);

    .gx-row {
      margin: 0;
      height: 100%;
    }

    [class*='gx-col-'] {
      padding: 0;
      height: 100%;
    }

    h3 {
      font-weight: 600;
    }

    @include media('>=#{breakpoint(sm)}') {
      height: 7.2rem;
      display: block;
      margin: 0;
      border: 1px solid color(border-main);
      border-top-left-radius: radius(lg);
      border-top-right-radius: radius(lg);
    }
  }

  &__perks {
    padding: space(lg) 0 0;
    border: 1px solid color(border-main);
    border-top: 0;
    border-bottom-left-radius: radius(lg);
    border-bottom-right-radius: radius(lg);

    h5 {
      @include typography(display-subtitle);
      font-weight: 600;

      @include media('<#{breakpoint(sm)}') {
        @include typography(body);
        font-weight: 600;
      }
    }

    @include media('<#{breakpoint(sm)}') {
      border: 1px solid color(border-main);
      border-radius: radius(md);
      padding: 0;
    }
  }

  &__perksHeader {
    padding: space(md);
    display: flex;
    justify-content: center;
    border-bottom: 1px solid color(border-main);
  }

  &__perksContent {
    padding: space(lg);

    &--disabled {
      .zones-manager-packages__includeTable > div {
        background-color: color(background-alt);
      }
    }

    &Row {
      @include media('<#{breakpoint(sm)}') {
        margin-bottom: space(lg);
      }
    }
  }

  &__perk {
    ul {
      list-style: none;
      padding: 0;
      margin: 0;
    }
  }

  &__single-perk {
    display: flex;
    align-items: center;
    padding: space(md) 0 space(md) space(md);
    border-bottom: 1px solid color(border-main);
    height: 5.6rem;

    @include media('<#{breakpoint(sm)}') {
      padding-left: 0;
      padding-right: space(lg);
      height: 6.4rem;
    }

    &:last-of-type {
      border-bottom: none;
    }

    .gx-icon {
      @include icon-size(md);
      margin-right: space(sm);
      flex-shrink: 0;
    }

    span {
      @include typography(body-small);
    }
  }

  &__row {
    margin: space(md) 0 space(2xl);

    &:last-of-type {
      margin-bottom: 0;

      [class*='gx-col-'] {
        border-bottom: 0;
      }
    }

    [class*='gx-col-'] {
      padding: 0;
      border-top: 1px solid color(border-main);
      border-bottom: 1px solid color(border-main);

      @include media('<#{breakpoint(sm)}') {
        border: 0;
      }
    }

    .gx-col-sm-8 {
      border-left: 1px solid color(border-main);
    }

    @include media('<#{breakpoint(sm)}') {
      margin-top: 0;
      margin-bottom: 0;
    }
  }

  &__iconSuccess {
    color: color(content-success);
  }

  &__includeTable {
    border-radius: radius(md);
    display: flex;
    flex-wrap: wrap;
    overflow: hidden;

    @include media('>=#{breakpoint(sm)}') {
      > :nth-child(3n + 2) {
        background-color: color(background-alt);
      }
    }

    @include media('<#{breakpoint(sm)}') {
      height: 100%;
      border: 1px solid color(border-main);
      border-radius: radius(lg);
    }

    & > div {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: space(md);
      flex-grow: 1;
      width: 33.33%;
      height: 5.6rem;
      text-align: center;
      border-bottom: 1px solid color(border-main);
      border-right: 1px solid color(border-main);
      @include typography(body-small);

      &:nth-last-child(-n + 3) {
        border-bottom: 0;
      }

      &:nth-child(3n) {
        border-right: 0;
      }

      .gx-icon {
        @include icon-size(md);
      }

      @include media('<#{breakpoint(sm)}') {
        width: 100%;
        border-right: 0;
        border-bottom: 1px solid color(border-main);
        @include typography(body-tiny);
        padding: space(sm);
        height: 6.4rem;

        &:nth-last-child(-n + 3) {
          border-bottom: 1px solid color(border-main);
        }

        &:last-child {
          border-bottom: 0;
        }
      }
    }
  }
}

.zones-manager-footer {
  text-align: center;

  @include media('<#{breakpoint(md)}') {
    margin: space(md) 0;
  }

  &__text {
    margin-top: space(lg);
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;

    .gx-icon {
      @include icon-size(md);
      margin: 0 space(sm);
    }
  }
}

.zones-manager-active-packages {
  h2 {
    @include typography(title-1);
    margin-bottom: space(lg);
  }

  .gx-card {
    max-height: 350px;
    overflow: hidden;
    position: relative;

    &--expanded {
      overflow: auto;
      max-height: none;

      .gx-card__content {
        padding-bottom: space(2xl);
      }
    }

    @include media('<#{breakpoint(md)}') {
      margin-bottom: space(lg);
      border: 1px solid color(border-main);
      border-radius: radius(md);
    }
  }

  .gx-row {
    margin-left: space(sm) * -1;
    margin-right: space(sm) * -1;
  }

  div[class^='gx-col-'] {
    padding-left: space(sm);
    padding-right: space(sm);
  }
}

.zones-manager-active-package {
  position: relative;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: space(md);
    border-bottom: 1px solid color(border-main);

    &--no-border {
      border-bottom: 0;
      padding-bottom: 0;
    }

    img {
      max-width: 4.8rem;
      margin-right: space(md);
    }

    & > div {
      display: flex;

      & > div {
        display: flex;
        flex-direction: column;

        h3 {
          @include typography(title-2);
        }

        span {
          @include typography(body);
        }
      }
    }
  }

  &__content {
    padding: space(md) 0 0;
  }

  &__zone {
    & + & {
      margin-top: space(md);
    }

    h5 {
      margin: 0;
      font-size: 1.4rem;
      font-weight: 700;
    }

    .gx-badge {
      margin: space(sm) space(sm) 0 0;
    }
  }
}

.zones-manager-show-more {
  padding: space(md);
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.9) 15%,
    rgba(255, 255, 255, 1) 100%
  );
  width: 100%;
  @include typography(body-small);
  color: color(content-action);
  margin-top: space(md);
  position: absolute;
  bottom: 0;
  left: 0;

  span {
    cursor: pointer;
  }
}
