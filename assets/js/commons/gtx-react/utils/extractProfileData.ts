


export type Association = {
    id: string;
    description: string;
};

export type UserContact = {
    fkAgente?: number;
    /** prefix is included */
    numero?: string;
    tipo?: string;
    preferito?: boolean;
    idContattoAgente?: number;
    pubblico?: boolean;
    /** the same as numero, with spaces between number-groups */
    fNumero?: string;
    prefisso?: string;
    fNumeroSenzaPrefisso?: string;
    /** like 'it' */
    shortCode?: string;
};

export type UserBio = {
    fkAgente?: number;
    lingua?: string;
    testo?: string;
    idBiografiaAgente?: number;
};

export type User = {
    /** fkAgente will always have the same value as this */
    idAgente?: number;
    cognome?: string;
    nome?: string;
    email?: string;
    chat?: boolean;
    fkImmagineAgente?: number;
    titolare?: boolean;
    emailVerificata?: boolean;
    status?: number;
    profilo?: {
        fkAgente?: number;
        // YYYY/MM/DD
        dataNascita?: string;
        sesso?: 0 | 1; // M/F
        codiceFiscale?: string;
        comune?: {
            nome?: string;
            sigla_provincia?: string;
            idComune?: number;
        };
        indirizzo?: string;
        cap: string;
        codiceREA?: string;
        partitaIVA?: string;
        associazioneDiCategoria?: {
            idAssociazioneDiCategoria?: number;
            nome?: string;
        };
        contatti?: Array<UserContact>;
        biografia?: Array<UserBio>;
    };
    ruolo?: {
        idRuoloAgente: number;
        /** like 'Amministratore' */
        ruolo?: string;
    };
    uuid?: string;
    fkAgenzia?: number;
    mostraInPubblicita?: boolean;
    /** full URL */
    urlImmagineAgente?: string;
    urlImmagineAgenteThumb?: string;
};

export type CountryCallingCode = {
    /** example: 'Italia */
    name?: string;
    /** example: '+39' */
    callingCode?: string;
    /** example: 'it */
    shortCode?: string;
};

export type ProfileData = {
    [x: string]: any;
    user: User;
    associations: Array<Association>;
    countryCallingCodes: Array<CountryCallingCode>;
};


export const extractUserProfileDataFromDom = (): ProfileData => {
    try {
        const profileDataElem = document.getElementById('profile-data') 
        if(!profileDataElem) {
            throw new Error('No profile-data element')
        }else{
            const data: ProfileData = JSON.parse(
                profileDataElem.innerHTML
            );
            if (data) {
                return { ...data };
            } else {
                throw new Error('No profile data found');
            }
        }
    } catch (error) {
        throw new Error('Error parsing JSON to extract profile data');
    }
};
