import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { isAfter } from 'date-fns';
import { formatDate } from 'gtx-react/components/DatePicker';
import Calendar from 'react-calendar';

type CalendarRangeInputProps = {
    onSelectStart: (date: Date | null) => void;
    onSelectEnd: (range: [Date | null, Date | null]) => void;
    startValue: Date | null;
    endValue: Date | null;
    onChange: (range: [string, string]) => void;
    locale: string;
    defaultActiveStartDate?: Date;
};

export default function CalendarRangeInput({
    endValue,
    startValue,
    onSelectEnd,
    onSelectStart,
    defaultActiveStartDate,
    onChange,
    locale,
}: CalendarRangeInputProps) {
    return (
        <Calendar
            value={[startValue, endValue]}
            onChange={(range) => {
                if (Array.isArray(range)) {
                    const [newStartValue, newEndValue] = range;

                    // if newStart exists, focus the end input
                    if (newStartValue) {
                        onSelectStart(newStartValue);
                    }

                    // if is reversed
                    if (
                        newStartValue &&
                        newEndValue &&
                        isAfter(newStartValue, newEndValue)
                    ) {
                        const formattedStartDate = formatDate(newStartValue);
                        const formattedEndDate = formatDate(newEndValue);

                        // swap
                        onChange([formattedEndDate, formattedStartDate]);
                    }

                    const formattedStartDate = newStartValue
                        ? formatDate(newStartValue)
                        : '';

                    const formattedEndDate = newEndValue
                        ? formatDate(newEndValue)
                        : '';

                    if (newEndValue) {
                        onSelectEnd([newStartValue, newEndValue]);
                    }

                    onChange([formattedStartDate, formattedEndDate]);
                } else {
                    throw new Error('Not implemented feature');
                }
            }}
            selectRange
            allowPartialRange
            prevLabel={
                <Button as="div" iconOnly>
                    <Icon name="arrow-left" />
                </Button>
            }
            nextLabel={
                <Button as="div" iconOnly>
                    <Icon name="arrow-right" />
                </Button>
            }
            next2Label={null}
            prev2Label={null}
            showNeighboringMonth
            defaultActiveStartDate={defaultActiveStartDate}
            minDetail="year"
            locale={locale}
        />
    );
}
