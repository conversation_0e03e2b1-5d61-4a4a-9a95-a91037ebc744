/**
 * This is a collection of hooks to manage the state of a Tanstack table in some handy ways.
 * Also, it includes a hook to persist the state of a table in the local storage, useful for preserving the state of the table between page reloads.
 */

import {
    type ColumnOrderState as TanstackColumnOrderState,
    type ColumnSizingState as TanstackColumnSizingState,
    type ColumnSizingInfoState as TanstackColumnSizingInfoState,
    type PaginationState as TanstackPaginationState,
    type SortingState as TanstackSortingState,
    type VisibilityState as TanstackVisibilityState,
    ColumnSizingInfoState,
} from '@tanstack/react-table';
import { useEffect, useMemo, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

type PaginationState = { pageIndex: number; pageSize: number };

/**
 * Hook to manage the pagination state of a Tanstack table.
 */
export function usePaginationState(defaultState: PaginationState) {
    return useState<TanstackPaginationState>(() => defaultState);
}

type FiltersParamsAccepetdTypes = string | number | boolean;

/**
 * Generica per search params
 */
export const useFiltersSearchParams = <
    T extends Record<string, FiltersParamsAccepetdTypes | Array<FiltersParamsAccepetdTypes>>,
>() => {
    const [searchParams, setSearchParams] = useSearchParams();

    const onChangeFilter = (value: Partial<T>) => {
        setSearchParams((sp) => {
            if (sp.has('page')) {
                // reset page
                sp.set('page', '1');
            }

            for (const [key, val] of Object.entries(value)) {
                if (Array.isArray(val) && val.length > 0) {
                    sp.delete(key);

                    for (const v of val) {
                        sp.append(key, v.toString());
                    }
                } else if (val) {
                    sp.set(key, val.toString());
                } else {
                    sp.delete(key);
                }
            }

            return sp;
        });
    };

    /**
     *
     * @param keys reset filters by keys
     */
    const handleReset = <T extends Record<string, unknown>>(object: Array<keyof T>) =>
        /**
         * @description Reset filters
         */
        function resetFilters() {
            setSearchParams((sp) => {
                if (sp.has('page')) {
                    // reset page
                    sp.set('page', '1');
                }

                for (const key of object) {
                    sp.delete(key.toString());
                }
                return sp;
            });
        };

    return [searchParams, onChangeFilter, handleReset] as const;
};

/**
 * Hook to manage the pagination state of a Tanstack table with search params.
 *
 * **Remember:** the page index is 0-based, so it should be incremented by 1 for our current convention.
 * @example
 * const [pagination, setPagination] = usePaginationSearchParams({ pageIndex: 0, pageSize: 10 });
 * // Following our convention: ?page=1&results=10
 */
export function usePaginationSearchParams(defaultState: PaginationState) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Retrieve the pagination state from the URL parameters or use the default
    const pagination: PaginationState = useMemo(
        () => ({
            pageIndex: searchParams.has('page') ? Number(searchParams.get('page')) - 1 : defaultState.pageIndex,
            pageSize: Number(searchParams.get('results') ?? defaultState.pageSize),
        }),
        [searchParams, defaultState]
    );

    // Function to update pagination in URL parameters
    const setPagination: (update: PaginationState | ((prev: PaginationState) => PaginationState)) => void = (
        update
    ) => {
        const newPagination = typeof update === 'function' ? update(pagination) : update;

        const newParams = new URLSearchParams(searchParams);
        newParams.set('page', String(newPagination.pageIndex + 1));
        newParams.set('results', String(newPagination.pageSize));

        setSearchParams(newParams, { replace: true });
    };

    return [pagination, setPagination] as const;
}

type SortingState = { id: string; desc: boolean }[];
/**
 * Hook to manage the sorting state of a Tanstack table.
 */
export function useSortingState(defaultState?: SortingState) {
    const sortingState = useState<TanstackSortingState>(() => defaultState || []);

    return sortingState;
}

/**
 * Hook to manage the sorting state of a Tanstack table with search params.
 */
export function useSortingSearchParams(defaultState?: SortingState) {
    const [searchParams, setSearchParams] = useSearchParams();

    // Retrieve sort state from URL parameters or use default
    const sorting: SortingState = useMemo(() => {
        const sortParam = searchParams.get('sort');
        if (!sortParam) {
            return defaultState ?? [];
        }

        const sortItems = sortParam.split(',');
        const result: SortingState = [];

        for (const item of sortItems) {
            const [id, descStr] = item.split(':');
            if (id) {
                result.push({ id, desc: descStr === 'desc' });
            }
        }

        return result;
    }, [searchParams, defaultState]);

    // Function to update the sorting in the URL parameters
    const setSorting: (update: SortingState | ((prev: SortingState) => SortingState)) => void = (update) => {
        const newSorting = typeof update === 'function' ? update(sorting) : update;

        const newParams = new URLSearchParams(searchParams);
        if (newSorting.length === 0) {
            newParams.delete('sort');
        } else {
            const sortParam = newSorting.map(({ id, desc }) => `${id}:${desc ? 'desc' : 'asc'}`).join(',');
            newParams.set('sort', sortParam);
            if (newParams.has('page')) {
                // Reset the page
                newParams.set('page', '1');
            }
        }

        setSearchParams(newParams, { replace: true });
    };

    return [sorting, setSorting] as const;
}

/**
 * Hook to manage the visibility state of a Tanstack table.
 */
type VisibilityState = Record<string, boolean>;

export function useColumnVisibilityState(defaultState?: VisibilityState) {
    const columnVisibilityState = useState<TanstackVisibilityState>(() => defaultState || {});

    return columnVisibilityState;
}

type ColumnOrderState = string[];

/**
 * Hook to manage the column order state of a Tanstack table.
 */
export function useColumnOrderState(defaultState?: ColumnOrderState) {
    const columnOrderState = useState<TanstackColumnOrderState>(() => defaultState || []);

    return columnOrderState;
}

type ColumnSizingState = Record<string, number>;

/**
 * Hook to manage the column sizing state of a Tanstack table.
 */
export function useColumnSizingState(defaultState?: ColumnSizingState) {
    return useState<TanstackColumnSizingState>(() => defaultState || {});
}

/**
 * Hook to manage the column sizing state of a Tanstack table.
 */
export function useColumnSizingInfoState(defaultState?: ColumnSizingInfoState) {
    const defaultValue: ColumnSizingInfoState = defaultState ?? {
        columnSizingStart: [],
        deltaOffset: 0,
        deltaPercentage: 0,
        isResizingColumn: false,
        startOffset: 0,
        startSize: 0,
    };
    return useState<TanstackColumnSizingInfoState>(() => defaultValue);
}

export const useTableStatePersist = <T extends object>(key: string, initialValue: T) => {
    useEffect(() => {
        localStorage.setItem(key, JSON.stringify(initialValue));
    }, [initialValue, key]);
};

/**
 * Utility function to get the default ordering columns.
 * @param columns
 * @returns
 */
export const useDefaultOrderingColumns = <T extends { id?: string; accessorKey?: string }>(columns: T[]) => {
    return columns.reduce<Array<string>>((acc, column) => {
        if (column.id) {
            acc.push(column.id);
        } else if (column.accessorKey) {
            acc.push(column.accessorKey);
        }
        return acc;
    }, []);
};
