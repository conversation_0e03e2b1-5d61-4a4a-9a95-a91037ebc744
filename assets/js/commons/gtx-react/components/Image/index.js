import React, { Fragment, useState, useEffect, useRef } from 'react';
/**
 * @typedef {Object} ImageProps
 * @property {string} [className]
 * @property {string} [src]
 * @property {string} [fallbackSrc]
 * @property {string} [loading]
 * @property {function} [onLoad]
 */

/**
 * @param {ImageProps} props
 */
export const Image = (props) => {
    const { fallbackSrc, className, src, ...rest } = props;
    const [mySrc, setMySrc] = useState(src);
    const [loaded, setLoaded] = useState(false);
    const style = loaded ? {} : { visibility: 'hidden' };
    const imageRef = useRef(null);

    const handleError = () => {
        fallbackSrc ? setMySrc(fallbackSrc) : null;
        setLoaded(true);
    };

    const handleLoad = () => {
        setLoaded(true);

        if (props.onLoad && imageRef && imageRef.current) {
            const imageRect = imageRef.current.getBoundingClientRect();
            props.onLoad(imageRect);
        }
    };

    useEffect(() => setMySrc(src ? src : fallbackSrc), [src]);

    return (
        <Fragment>
            {/* TODO: rendere il loader compatibile anche con le miniature */}
            {/* <Loader loading={!loaded} inlineOverlay={true} fixedOverlay={false} /> */}
            <img
                {...rest}
                ref={imageRef}
                className={className}
                style={style}
                src={mySrc}
                onError={handleError}
                onLoad={handleLoad}
                loading={props.loading ? props.loading : null}
            />
        </Fragment>
    );
};
