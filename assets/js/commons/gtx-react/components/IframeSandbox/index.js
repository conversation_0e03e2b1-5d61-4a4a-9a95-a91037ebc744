import React, { Component } from 'react';
import { createPortal } from 'react-dom';

/**
 * @typedef {Object} IframeSandboxProps
 * @property {Array<string|function>} [stylesheets]
 * @property {boolean} [useinlinestylesheet]
 * @property {React.ReactNode} [children]
 */

/**
 * @extends {Component<IframeSandboxProps>}
 */
export class IframeSandbox extends Component {
    static defaultProps = {
        useinlinestylesheet: false,
        stylesheets: [],
    };

    state = {};

    launchSandbox = (document) => {
        let m = document.createElement('meta');
        m.httpEquiv = 'Content-Type';
        m.content = 'text/html; charset=utf-8';

        document.head.appendChild(m);

        if (this.props.stylesheets) {
            let noFlash = document.createElement('style');
            noFlash.innerHTML = '* {display:none !important}';
            document.head.appendChild(noFlash);

            let loading = this.props.stylesheets
                .map((value) => (typeof value === 'function' ? value() : value))
                .map((href) =>
                    Promise.resolve(href).then((href) => {
                        if (this.props.useinlinestylesheet) {
                            return fetch(href)
                                .then((res) => res.text())
                                .then((txt) => {
                                    let s = document.createElement('style');
                                    s.textContent = txt;
                                    document.head.appendChild(s);
                                });
                        } else {
                            return new Promise((resolve) => {
                                let l = document.createElement('link');
                                l.onload = resolve;
                                l.rel = 'stylesheet';
                                l.href = href;

                                document.head.appendChild(l);
                            });
                        }
                    })
                );

            Promise.all(loading).then(() => {
                document.head.removeChild(noFlash);
                this.setState({ document });
            });
        } else {
            this.setState({ document });
        }
    };

    createSandbox = (el) => {
        if (!el) {
            return;
        }

        if (el.contentDocument.readyState === 'complete') {
            this.launchSandbox(el.contentDocument);
        } else {
            el.onload = () => {
                this.launchSandbox(el.contentDocument);
            };
        }
    };

    get document() {
        return this.state.document;
    }

    get documentBody() {
        return this.document && this.document.body;
    }

    get contentHTML() {
        return this.document && `<html>${this.document.documentElement.innerHTML}</html>`;
    }

    render() {
        let { children, stylesheets, useinlinestylesheet, ...other } = this.props;

        return [
            <iframe ref={this.createSandbox} frameBorder={0} {...other} key={1} />,
            this.documentBody && createPortal(this.props.children, this.documentBody, 2),
        ].filter(Boolean);
    }
}
