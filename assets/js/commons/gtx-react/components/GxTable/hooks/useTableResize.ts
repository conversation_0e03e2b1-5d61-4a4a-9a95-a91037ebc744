import { useEffect, useState, useRef, RefObject } from 'react';

const useTableResize = (
    tableContent: RefObject<HTMLDivElement | null>,
    tableWrap: RefObject<HTMLElement | null>
) => {
    const [
        stickyScrollbarBubbleWidth,
        setStickyScrollbarBubbleWidth,
    ] = useState(0);
    const [tableWidth, setTableWidth] = useState<number>(0);
    const scrollableOffsetWidth = useRef<number>(0);

    const getScrollableWidth = () => {
        if (!tableContent?.current || !tableWrap?.current) {
            return null;
        }

        const offsetWidth =
            tableContent.current.offsetWidth - tableWrap.current.offsetWidth;
        scrollableOffsetWidth.current = offsetWidth;

        if (!offsetWidth) {
            return 0;
        }

        return tableWrap.current.offsetWidth - offsetWidth;
    };

    const getTableWidth = () => {
        if (!tableWrap?.current) {
            return null;
        }
        return tableWrap.current.offsetWidth;
    };

    const handleResize = () => {
        setStickyScrollbarBubbleWidth(getScrollableWidth());
        setTableWidth(getTableWidth());
    };

    useEffect(() => {
        setStickyScrollbarBubbleWidth(getScrollableWidth());
        setTableWidth(getTableWidth());
    }, []);

    useEffect(() => {
        window.addEventListener('resize', handleResize);

        return () => {
            window.removeEventListener('resize', handleResize);
        };
    });

    return {
        stickyScrollbarBubbleWidth,
        scrollableOffsetWidth: scrollableOffsetWidth.current,
        tableWidth,
    };
};

export default useTableResize;
