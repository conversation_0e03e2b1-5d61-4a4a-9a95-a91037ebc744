import React, { useState, useRef, ReactNode } from 'react';
import classNames from 'classnames';
import { useScrollNavigator } from '../../hooks/useScrollNavigator';

export type Image = {
    url: string;
    title?: string;
};

export type CarouselProps = {
    items: Image[];
    cursor?: number;
    onScroll?: any;
    dotsNavigation?: boolean;
    outsideArrow?: boolean;
    withItemSpace?: boolean;
    isStarting?: boolean;
    leftIcon?: ReactNode;
    rightIcon?: ReactNode;
    customClass?: string;
};

export const Carousel = ({
    items,
    dotsNavigation,
    leftIcon,
    rightIcon,
    outsideArrow,
    withItemSpace,
    isStarting,
    customClass,
}: CarouselProps) => {
    const sliderContent = useRef<HTMLDivElement>(null);
    const scrollNavigator = useScrollNavigator(sliderContent);
    const [cursorPosition, setCursorPosition] = useState(1);

    const arrowNavigation = e => {
        e.stopPropagation();
        if (e.key === 'ArrowLeft' || e.keyCode === 37) {
            scrollNavigator.prev();
        }
        if (e.key === 'ArrowRight' || e.keyCode === 39) {
            scrollNavigator.next();
        }
    };

    return (
        <div className="nd-carousel">
            <div
                className={classNames(
                    'nd-carouselWrapper',
                    outsideArrow && 'nd-carouselWrapper--outsideArrow',
                    withItemSpace && 'nd-carouselWrapper--withItemSpace',
                    isStarting &&
                        !scrollNavigator.leftButtonVisible &&
                        'isStarting',
                    customClass
                )}
            >
                <div className="nd-carousel__content" ref={sliderContent}>
                    {items.map((image, i) => (
                        <div className="nd-carousel__item" key={i}>
                            <img
                                src={image.url}
                                alt={image.title || `image${i}`}
                                loading="lazy"
                            />
                        </div>
                    ))}
                </div>
                {dotsNavigation && (
                    <div className="nd-carousel__dotsNavigation">
                        {items.map((_item, i) => {
                            const current = i === scrollNavigator.cursor;
                            return (
                                <span
                                    key={i}
                                    className={classNames([
                                        {
                                            'is-current': current,
                                        },
                                    ])}
                                ></span>
                            );
                        })}
                    </div>
                )}
                <div className="nd-carousel__nav">
                    <div
                        className={classNames(
                            'nd-carousel__arrow',
                            'nd-carousel__arrow--prev',
                            scrollNavigator.noPrevItem && 'is-disabled'
                        )}
                        onClick={e => {
                            e.stopPropagation();
                            setCursorPosition(
                                cursorPosition => cursorPosition - 1
                            );
                            scrollNavigator.prev();
                        }}
                        onKeyDown={arrowNavigation}
                    >
                        {leftIcon}
                    </div>
                    <div className="nd-carousel__dotsNavigation">
                        {cursorPosition} di {items.length}
                    </div>
                    <div
                        className={classNames(
                            'nd-carousel__arrow',
                            'nd-carousel__arrow--next',
                            scrollNavigator.noNextItem && 'is-disabled'
                        )}
                        onClick={e => {
                            e.stopPropagation();
                            setCursorPosition(
                                cursorPosition => cursorPosition + 1
                            );
                            scrollNavigator.next();
                        }}
                        onKeyDown={arrowNavigation}
                    >
                        {rightIcon}
                    </div>
                </div>
            </div>
        </div>
    );
};
