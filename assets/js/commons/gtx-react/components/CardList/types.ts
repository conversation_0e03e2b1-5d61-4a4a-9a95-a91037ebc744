import { ReactElement, ReactNode } from 'react';

export type CardField<T = unknown> = {
    /**
     * Field identifier.
     */
    key: string;
    /**
     * Field label.
     */
    label?: string;
    /**
     * If the a field is main then the UI will behavior differently.
     */
    main?: boolean;
    /**
     * Function that loads a card field content through an api call.
     */
    asyncContentRender?: (data: T) => ReactElement | HTMLElement | ReactNode;
    /**
     * Function that return a card field content.
     */
    renderContent?: (data: T) => ReactElement | HTMLElement | string;
    /**
     * Indicates if the field is visible or not.
     */
    visible?: boolean;
};

export type CardListLabels = {
    /**
     * Label to show when a card is selected.
     */
    singleCardSelected?: string;
    /**
     * Label to show when more cards are selected.
     */
    moreCardsSelected?: string;
    /**
     * Label to show into the card "show more" button.
     */
    showMore?: string;
    /**
     * Label to show into the card "show less" button.
     */
    showLess?: string;
};

export type CardListEmptyState = {
    /**
     * Button click handler.
     */
    buttonLabel?: string;
    /**
     * Displayed text.
     */
    text: string;
    /**
     * Button click handler.
     */
    buttonOnClick?: () => void;
    /**
     * Image path.
     */
    image: string;
};

export type ItemSelectionStatus = boolean | 'disabled';

export type ItemSelectionCallback = (id: string) => {
    status: ItemSelectionStatus;
    tooltip?: string;
};

export type CardListConfigs = {
    /**
     * Optional flag to indicate if a card can be selected.
     * If flagged as true, a checkbox will be displayed on every single card.
     */
    itemSelection?: boolean | ItemSelectionCallback;
    /**
     * Optional helper function.
     * This is useful to show or not a specific row action (quick/menu/bulk).
     */
    itemActionsHelper?: (...props) => void;
    /**
     * Optional UI labels list
     */
    labels?: CardListLabels;
    /**
     * Config object used to build the UI when the table is empty.
     */
    emptyState?: CardListEmptyState;
};

export type CardListData<T = unknown> = {
    /**
     * Cards data
     */
    items: Card<T>[];
    /**
     * Keys list used to decorate the card data
     * and used by the component to add specific behaviors to the UI.
     */
    extraItemFields?: string[];
};

export type Card<T = unknown> = {
    /**
     * Card identifier.
     */
    id: string | number;
    /**
     * Card is set/not set as favourite.
     */
    favourite?: boolean;
} & T;

export type CardItem = {
    /**
     * Card rows data object.
     */
    rows: any;
    /**
     * Extra card data object.
     */
    extra: any;
};

export type CardListOptions<T = unknown> = {
    /**
     * Card actios.
     */
    actions: CardListActions;
    /**
     * Config object.
     */
    configs: CardListConfigs;
    /**
     * Card list data. It contains info about cards and sorting.
     */
    cardsData: CardListData<T>;
    /**
     * Card fields array.
     */
    cardFields: CardField<T>[];
    /**
     * Indicates the list will be displayed on two columns.
     */
    twoColumnsView: boolean;
    /**
     * Determine if the show more button is visible
     */
    displayShowMoreButton?: boolean;
    /**
     * Determines if there is no initial items to show.
     */
    noInitialItems?: boolean;
};

export type CardAction = {
    /**
     * Action label.
     */
    label?: string;
    /**
     * Button icon .
     */
    icon?: string;
    /**
     * Action handler.
     */
    action: (_data: Card['id'] | Card['id'][]) => void;
    /**
     * Action href.
     */
    link?: any;
    /**
     * Optional values list.
     * An action may be under specific conditions determined by `CardConfig.itemActionsHelper`
     */
    validFor?: any;
    /**
     * Optional values list.
     * An action may not available because of specific conditions determined by `CardConfig.itemActionsHelper`.
     */
    notValidFor?: any;
};

export type MenuAction = Omit<CardAction, 'icon' | 'validFor'>;

export type CardListActions = {
    /**
     * Action performed when a card is touched.
     */
    main: {
        action: (id: Card['id']) => void;
        /**
         * Optional values list.
         * May not available because of specific conditions determined by `CardConfig.itemActionsHelper`.
         */
        notValidFor?: any;
    } | null;
    /**
     * Actions displayed within the card header.
     */
    quick: CardAction[];
    /**
     * Actions listed into the dropdown within the card header.
     */
    menu: MenuAction[] | null;
    /**
     * Bulk actions dispayed in the floating box
     * when one or more cards are selected.
     */
    bulk: CardAction[] | null;
};

export type CardRowItem = {
    key: string;
    content;
    visible: boolean;
    asyncContent: ReactElement | HTMLElement | ReactNode | null;
};
