import * as React from 'react';
import { useMemo } from 'react';
import { QsWithSource } from '../../../types/util_types';

type GtxContextProviderProps = {
    goBack: () => void;
    queryString?: QsWithSource<unknown>;
    initialData: unknown;
};

type GtxAppContextValues = {
    goBack: () => void;
    queryString: GtxContextProviderProps['queryString'];
    initialData: any;
};

export const GtxAppContext = React.createContext<GtxAppContextValues>(null!);

export const GtxAppContextProvider: React.FC<
    React.PropsWithChildren<GtxContextProviderProps>
> = ({ goBack, queryString, children, initialData }) => {
    const ctx = useMemo(
        () => ({
            goBack,
            queryString,
            initialData,
        }),
        [initialData]
    );

    return (
        <GtxAppContext.Provider value={ctx}>{children}</GtxAppContext.Provider>
    );
};
