import { UseMutationOptions, useMutation } from '@tanstack/react-query';
import { CreateRequestServiceArgs, createRequestService } from './api';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';

export const useRequestServiceMutation = (
    options?: UseMutationOptions<boolean, unknown, CreateRequestServiceArgs, unknown>
) => {
    return useMutation({
        mutationFn: createRequestService,
        ...options,
    });
};

export const useCreateRequestFeedbacksWithSnackbar = (
    mutate: ReturnType<typeof useRequestServiceMutation>['mutate']
) => {
    const { showNotification } = useNotifyContext();
    const mutation = (args: CreateRequestServiceArgs) =>
        mutate(args, {
            onSuccess: (isSuccess: boolean) => {
                showNotification(
                    isSuccess
                        ? {
                              message: trans('confirmEntity.success'),
                              type: 'success',
                          }
                        : {
                              message: trans('confirmEntity.error'),
                              type: 'error',
                          }
                );
            },
            onError: () => {
                showNotification({
                    message: trans('confirmEntity.error'),
                    type: 'error',
                });
            },
        });
    return mutation;
};
