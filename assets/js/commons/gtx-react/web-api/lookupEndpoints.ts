import { serialize } from '@pepita/querystring';

export const lookupEndpoints = {
    PROPERTIES_HEATINGS: `/api/lookup/properties/heatings`,
    PROPERTY_OWNERSHIP: '/api/lookup/properties/property-ownerships',
    COUNTRIES: '/api/lookup/geography/countries',
    PROPERTY_LICENSES: `/api/lookup/properties/licenses`
};

//returning null or undefined from the lookupEndpointsWithParam func will prevend the api call
export const lookupEndpointsWithParam = {
    CITY_ZONES: (args: { cityId: string }) => args.cityId && `/api/lookup/cities/${args.cityId}/zones`,
    CITIES: (args: { provinceId: string }) => args.provinceId && `/api/lookup/provinces/${args.provinceId}/cities`,
    PROVINCES_BY_COUNTRY: (args: { countryId: string }) =>
        args.countryId && `/api/lookup/geography/countries/${args.countryId}/provinces`,
    PROVINCES_BY_REGION: (args: { regionId: string }) =>
        args.regionId && `/api/lookup/geography/regions/${args.regionId}/provinces`,
    REGIONS: (args: { countryId: string }) =>
        args.countryId && `/api/lookup/geography/countries/${args.countryId}/regions`,
    PROPERTY_TIPOLOGY_BY_CATEGORY: (args: { categoryId: string }) =>
        args.categoryId && `/api/lookup/properties/categories/${args.categoryId}/typologies`,
    PROPERTY_CONTRACTS_BY_CATEGORY: (args: { categoryId: string }) =>
        args.categoryId && `/api/lookup/categories/${args.categoryId}/contracts`,
    PROPERTIES_CATEGORIES: (args: { excludedCategories?: number[] }) =>
        `/api/lookup/properties/categories?${serialize(args)}`,
};

export type LookupEndpointsKeys = keyof typeof lookupEndpoints;
export type LookupEndpointsWithParamKeys = keyof typeof lookupEndpointsWithParam;

export const isLookupWithParams = (
    value: string
): value is LookupEndpointsWithParamKeys => {
    return Object.keys(lookupEndpointsWithParam).includes(value);
};

export const isLookupWithoutParams = (value: string): value is LookupEndpointsKeys => {
    return Object.keys(lookupEndpoints).includes(value);
};
