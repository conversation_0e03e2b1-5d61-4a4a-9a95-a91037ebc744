import React, { Fragment, useState } from 'react';
import { Item, MediaQuery } from 'gtx-react/components';
import { Popover } from '@gx-design/popover';
import {
    getUserSearchDate,
    getUserSearchZones,
    getUserSearchTypologies,
    getUserSearchCity,
    getUserSearchStaticMapUrl,
    getUserSearchStaticMapUrlRetina,
    getUserSearchHeating,
    getUserSearchBathrooms,
    getUserSearchOwnership,
    getUserSearchCategory,
    getUserSearchTerraceBalcony,
    getUserSearchPrice,
    getUserSearchSurface,
    getUserSearchBox,
    getUserSearchRooms,
    getUserSearchContract,
    getUserSearchProvince,
} from '../selectors/userSearch';
import { COLUMNS_MEDIA_QUERY } from '../constants';
import { trans } from '@pepita-i18n/babelfish';
import { Badge } from '@gx-design/badge';

const maxVisibleZones = 3;
let searchesIndex = 0;

function UserSearchZones(search) {
    const [hidden, setVisible] = useState(true);

    searchesIndex++;

    return getUserSearchZones(search).map((zone, index, arr) => {
        if (index < maxVisibleZones) {
            return <Badge key={`${searchesIndex}_${index}`} text={zone} />;
        } else if (index === maxVisibleZones) {
            return (
                <Fragment key={`search_tags_${searchesIndex}`}>
                    <Badge classnames={hidden ? 'hidden' : ''} key={`${searchesIndex}_${index}`} text={zone} />
                    <Badge
                        classnames={!hidden ? 'hidden' : 'show-more'}
                        key={`${searchesIndex}_show-more-typologies`}
                        text={`+${arr.length - maxVisibleZones}`}
                        onClick={() => setVisible(false)}
                    />
                </Fragment>
            );
        } else {
            return <Badge classnames={hidden ? 'hidden' : ''} key={`${searchesIndex}_${index}`} text={zone} />;
        }
    });
}

let UserSearchTypologies = ({ search }) => {
    const typologies = getUserSearchTypologies(search);

    if (typologies.length > 1) {
        return (
            <MediaQuery queries={COLUMNS_MEDIA_QUERY}>
                {(match) =>
                    !match.MOBILE && !match.TABLET ? (
                        <Popover
                            onEdge={false}
                            title=""
                            large={false}
                            position="top"
                            content={<div>{typologies.join(', ')}</div>}
                        >
                            <span>
                                {typologies.length} {trans('label.typologies')}
                            </span>
                        </Popover>
                    ) : (
                        <span>
                            {typologies.length} {trans('label.typologies')}
                        </span>
                    )
                }
            </MediaQuery>
        );
    }

    return typologies[0];
};

export let UserSearch = ({ search }) => {
    return (
        <div className="user-search-card">
            <div className="user-search-card__head">
                <div className="user-search-card__title">
                    {trans('label.research_in_date', { DATE: getUserSearchDate(search) })}
                </div>
                {getUserSearchStaticMapUrl(search) ? (
                    <img
                        className="user-search-card__map"
                        src={getUserSearchStaticMapUrl(search)}
                        srcSet={getUserSearchStaticMapUrlRetina(search)}
                    />
                ) : null}
                {getUserSearchCity(search) ? (
                    <Item label={`${trans('label.municipality')} / ${trans('label.province')}`}>
                        {getUserSearchCity(search)} ({getUserSearchProvince(search)})
                    </Item>
                ) : null}
                <div className="user-search-card__badges">
                    {getUserSearchZones(search)
                        ? UserSearchZones(search) // eslint-disable-line new-cap
                        : null}
                </div>
            </div>
            <div className="gx-summary-list user-search-card__list">
                {getUserSearchContract(search) ? (
                    <Item label={trans('label.contract')}>{getUserSearchContract(search)}</Item>
                ) : null}
                {getUserSearchCategory(search) ? (
                    <Item label={trans('label.category')}>{getUserSearchCategory(search)}</Item>
                ) : null}
                {getUserSearchTypologies(search) ? (
                    <Item label={trans('label.typology')}>
                        <UserSearchTypologies search={search} />
                    </Item>
                ) : null}
                {getUserSearchPrice(search) ? (
                    <Item label={trans('label.price')}>{getUserSearchPrice(search)}</Item>
                ) : null}
                {getUserSearchSurface(search) ? (
                    <Item label={trans('label.surface')}>{getUserSearchSurface(search)}</Item>
                ) : null}
                {getUserSearchRooms(search) ? (
                    <Item label={trans('label.rooms')}>{getUserSearchRooms(search)}</Item>
                ) : null}
                {getUserSearchHeating(search) ? (
                    <Item label={trans('label.heating')}>{getUserSearchHeating(search)}</Item>
                ) : null}
                {getUserSearchBathrooms(search) ? (
                    <Item label={trans('label.bathrooms')}>{getUserSearchBathrooms(search)}</Item>
                ) : null}
                {getUserSearchOwnership(search) ? (
                    <Item label={trans('label.property_type')}>{getUserSearchOwnership(search)}</Item>
                ) : null}
                {getUserSearchBox(search) ? (
                    <Item label={trans('label.car_parking')}>{getUserSearchBox(search)}</Item>
                ) : null}
                {getUserSearchTerraceBalcony(search) ? (
                    <Item label={`${trans('label.terrace')}/${trans('label.balcony')}`} mobileFullWidth={true}>
                        {getUserSearchTerraceBalcony(search)}
                    </Item>
                ) : null}
            </div>
        </div>
    );
};
