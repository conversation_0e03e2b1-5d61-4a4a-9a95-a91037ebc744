import React from 'react';
import { IRealEstateObj } from './types';
import { formatAddress } from './utils/formatAddress';
import { formatPrice } from './utils/formatPrice';

interface IImmovisitaScheduleModalRefBox {
    realEstateObj: IRealEstateObj;
}

export const IvSchModalRefBox: React.FunctionComponent<IImmovisitaScheduleModalRefBox> = ({
    realEstateObj,
}) => {
    return (
        <div>
            {realEstateObj && (
                <div className="gx-scheduled-visit-modal__ref">
                    <div className="list__field__thumb">
                        <div className="property-block__photo">
                            {realEstateObj.mainImage ? (
                                <img src={realEstateObj.mainThumbUrl} />
                            ) : (
                                <img src="/bundles/base/getrix/common/img/img-placeholder.png" />
                            )}
                        </div>
                    </div>
                    <div className="gx-scheduled-visit-modal__refInfo">
                        <div>RIF. {realEstateObj?.id}</div>
                        <div>
                            {formatAddress(
                                realEstateObj.properties[0]
                                    ?.geographyInformation
                            )}
                        </div>
                        <div>{formatPrice(realEstateObj.prices[0])}</div>
                    </div>
                </div>
            )}
        </div>
    );
};
