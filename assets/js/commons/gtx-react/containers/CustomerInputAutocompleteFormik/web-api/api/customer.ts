import { http } from '@pepita/http';
import { endpoints } from '../endpoints';

export const getCustomerApi = (additionalParams) => (params) => {
    if (params && params.id) {
        return fetch(endpoints.GET_CUSTOMER.replace(':id', params.id) + `${additionalParams ? '?' + new URLSearchParams(additionalParams) : ''}`)
    } else {
        return Promise.reject();
    }
};

export const getSearchCustomerApi = (params: {
    [key: string]: string | boolean
} = {}) => (text: string) => {
    return http.get(endpoints.SEARCH_CUSTOMER, {
        searchParams: {
            q: text,
            ...params
        }
    }).json();
};
export const updateCustomerApi = (params) => {
    if (params && params.id) {
        return http
            .post(endpoints.UPDATE_CUSTOMER.replace(':id', params.id), {
                form: params,
            })
            .json();
    } else {
        return Promise.reject();
    }
};

export const createCustomerApi = (params) => {
    if (params) {
        return http
            .post(endpoints.CREATE_CUSTOMER, {
                form: params,
            })
            .json();
    } else {
        return Promise.reject();
    }
};
