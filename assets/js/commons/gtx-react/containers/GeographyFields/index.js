import React, { useEffect, useState, useReducer } from 'react';
import { useFormikContext } from 'formik';
import { trans } from '@pepita-i18n/babelfish';
import { lookupApi } from '../../web-api';
import { GxFkSelect } from 'gtx-react/components/gx-formik';

const SOURCES = {};

Object.defineProperties(SOURCES, {
    COUNTRY: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'countries',
    },
    REGION: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'regions',
    },
    PROVINCE: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'provinces',
    },
    CITY: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'cities',
    },
    ZONE: {
        enumerable: true,
        configurable: false,
        writable: false,
        value: 'zones',
    },
});

const reducer = (state = initialState, action) => {
    switch (action.type) {
        case 'SET_LOADING':
            return { ...state, ...action.payload };
        default:
            return state;
    }
};

const Wrapper = ({ wrap, children }) => {
    return <>{wrap ? <div className="filter-box__section">{children}</div> : children}</>;
};

/**
 * @typedef {Object} GeographyFieldsLabelsMap
 * @property {string} [province]
 * @property {string} [region]
 * @property {string} [city]
 * @property {string} [country]
 * @property {string} [zones]
 */

/**
 * @typedef {Object} GeographyFieldsInitialValuesMap
 * @property {Array} [countries]
 * @property {Array} [regions]
 * @property {Array} [provinces]
 * @property {Array} [cities]
 * @property {Array} [zones]
 */

/**
 * @typedef {Object} GeographyFieldsPlaceholders
 * @property {string} [country]
 * @property {string} [region]
 * @property {string} [province]
 * @property {string} [city]
 * @property {string} [zone]
 */

/**
 * @typedef {Object} GeographyFieldsEndpointsMap
 * @property {string} [countries]
 * @property {string} [regions]
 * @property {string} [provinces]
 * @property {string} [cities]
 * @property {string} [zones]
 */

/**
 * @typedef {Object} GeographyFieldsLoadersMap
 * @property {function} [countries]
 * @property {function} [regions]
 * @property {function} [provinces]
 * @property {function} [cities]
 * @property {function} [zones]
 */

/**
 * @typedef {Object} GeographyFieldsOnLoadHandlerMap
 * @property {function} [countries]
 * @property {function} [regions]
 * @property {function} [provinces]
 * @property {function} [cities]
 * @property {function} [zones]
 */

/**
 * @typedef {Object} GeographyFieldsProps
 * @property {boolean} [disabled]
 * @property {boolean} [hasCountry]
 * @property {boolean} [hasRegion]
 * @property {boolean} [hasProvince]
 * @property {boolean} [hasCity]
 * @property {boolean} [hasZone]
 * @property {boolean} [noWrapper]
 * @property {Array<'province'|'region'|'city'|'country'|'zones'>} [noPlaceholder]
 * @property {GeographyFieldsLabelsMap} [fieldLabelsMap]
 * @property {boolean|GeographyFieldsInitialValuesMap} [initialValuesMap]
 * @property {boolean|GeographyFieldsPlaceholders} [placeholders]
 * @property {boolean|GeographyFieldsEndpointsMap} [customEndpointsMap]
 * @property {boolean|GeographyFieldsLoadersMap} [customLoadersMap]
 * @property {boolean|GeographyFieldsOnLoadHandlerMap} [onLoadHandlerMap]
 */

/**
 * @param {GeographyFieldsProps} props
 */
const GeographyFields = ({
    hasCountry = true,
    hasRegion = true,
    hasProvince = true,
    hasCity = true,
    hasZone = true,
    noPlaceholder,
    noWrapper = false,
    initialValuesMap = false,
    fieldLabelsMap,
    customEndpointsMap = false,
    customLoadersMap = false,
    onLoadHandlerMap = false,
    placeholders = false,
    disabled = false,
}) => {
    const { values, setFieldValue } = useFormikContext();

    const [countries, setCountries] = useState([]);
    const [regions, setRegions] = useState([]);
    const [provinces, setProvinces] = useState([]);
    const [cities, setCities] = useState([]);
    const [zones, setZones] = useState([]);
    const [loadState, dispatch] = useReducer(reducer, {
        countries: false,
        regions: false,
        provinces: false,
        cities: false,
        zones: false,
    });
    const [init, setInit] = useState(true);

    const stateSetters = {
        setCountries,
        setRegions,
        setProvinces,
        setCities,
        setZones,
    };

    const loadData = (sourceType, param, method) => {
        if (disabled) {
            return;
        }

        const loaders = {
            loader: null,
            data: null,
        };

        if (sourceType in SOURCES) {
            const sourceKey = SOURCES[sourceType];
            const setter = stateSetters[`set${sourceKey.charAt(0).toUpperCase()}${sourceKey.slice(1)}`];
            loaders.loader = customLoadersMap[sourceKey];
            loaders.data = initialValuesMap[sourceKey];
            loaders.endpoint = customEndpointsMap[sourceKey];

            const { loader, endpoint, data } = loaders;

            const remote = () => {
                const resp = loader ? loader(param) : lookupApi(endpoint, param);

                if (resp instanceof Promise) {
                    dispatch({
                        type: 'SET_LOADING',
                        payload: { [sourceKey]: true },
                    });

                    resp.then((resp) => {
                        if (!resp || !resp.data) {
                            dispatch({
                                type: 'SET_LOADING',
                                payload: { [sourceKey]: false },
                            });
                            return;
                        }

                        setter(resp?.data);
                        dispatch({
                            type: 'SET_LOADING',
                            payload: { [sourceKey]: false },
                        });

                        onLoadHandlerMap && onLoadHandlerMap[sourceKey] ? onLoadHandlerMap[sourceKey]() : null;
                    });
                } else if (Array.isArray(resp)) {
                    setter(resp);
                }
            };

            const local = () => setter(data);

            if (method) {
                method === 'remote' && remote();
                method === 'local' && local();
            } else {
                if (loader || endpoint) {
                    remote();
                }

                if (data) {
                    setter(data);
                }
            }
        } else {
            throw new Error(`Supported source type is any of ${Object.keys(SOURCES).join(',')}`);
        }
    };

    useEffect(() => {
        if (!initialValuesMap.hasOwnProperty('countries') || !hasCountry) {
            return;
        }

        if (!initialValuesMap.countries) {
            dispatch({
                type: 'SET_LOADING',
                payload: { [SOURCES.COUNTRIES]: true },
            });
            return;
        }

        dispatch({
            type: 'SET_LOADING',
            payload: { [SOURCES.COUNTRIES]: false },
        });
        loadData('COUNTRY', initialValuesMap.countries, 'local');
    }, [initialValuesMap]);

    useEffect(() => {
        if (hasCountry) {
            const { country } = values;

            setFieldValue('region', '');
            setRegions([]);

            if (hasProvince) {
                setFieldValue('province', '');
                setProvinces([]);
            }

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
            }

            country && loadData('REGION', country, 'remote');
        }
    }, [values.country]);

    useEffect(() => {
        if (hasRegion) {
            const { region } = values;

            if (hasProvince) {
                setFieldValue('province', '');
                setProvinces([]);
            }

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            region && loadData('PROVINCE', region, 'remote');
        }
    }, [values.region]);

    useEffect(() => {
        if (hasProvince) {
            const { province } = values;

            if (hasCity) {
                setFieldValue('city', '');
                setCities([]);
            }

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
            }

            province && loadData('CITY', province, 'remote');
        }
    }, [values.province]);

    useEffect(() => {
        if (hasCity) {
            const { city } = values;

            if (hasZone) {
                setFieldValue('zones', '');
                setZones([]);
                city && loadData('ZONE', city, 'remote');
            }
        }
    }, [values.city]);

    useEffect(() => {
        if (init) {
            for (let source in SOURCES) {
                loadData(source, null);
            }
            setInit(false);
        }
    }, [init]);

    useEffect(() => {
        if (values?.region && hasRegion) {
            setFieldValue('region', values.region);
        }

        if (values?.province && hasProvince) {
            setFieldValue('province', values.province);
        }

        if (values?.city && hasCity) {
            setFieldValue('city', values.city);
        }

        if (values?.zones && hasZone) {
            setFieldValue('zones', values.zones);
        }
    }, []);

    return (
        <Wrapper wrap={!noWrapper}>
            <>
                {hasCountry && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="country"
                            name="country"
                            label={fieldLabelsMap?.countries ? fieldLabelsMap.countries : trans('label.nation')}
                            placeholder={placeholders?.country}
                            disabled={disabled || !countries || countries.length === 0}
                            options={countries ? countries : initialValuesMap[SOURCES.COUNTRY] ?? []}
                            isLoading={loadState[SOURCES.COUNTRY]}
                        />
                    </div>
                )}
                {hasRegion && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="region"
                            name="region"
                            label={fieldLabelsMap?.region ? fieldLabelsMap.region : trans('label.region')}
                            placeholder={placeholders?.region}
                            options={regions ? regions : initialValuesMap[SOURCES.REGION] ?? []}
                            disabled={!regions || regions.length === 0}
                            isLoading={loadState[SOURCES.REGION]}
                        />
                    </div>
                )}
                {hasProvince && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="province"
                            name="province"
                            label={fieldLabelsMap?.province ? fieldLabelsMap.province : trans('label.province')}
                            placeholder={placeholders?.province}
                            options={provinces ? provinces : initialValuesMap[SOURCES.PROVINCE] ?? []}
                            disabled={disabled || !provinces || provinces.length === 0}
                            isLoading={loadState[SOURCES.PROVINCE]}
                        />
                    </div>
                )}
                {hasCity && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="city"
                            name="city"
                            label={fieldLabelsMap?.city ? fieldLabelsMap.city : trans('label.municipality')}
                            placeholder={placeholders?.city}
                            options={cities ? cities : initialValuesMap[SOURCES.CITY] ?? []}
                            disabled={disabled || !cities || cities.length === 0}
                            isLoading={loadState[SOURCES.CITY]}
                        />
                    </div>
                )}
                {hasZone && (
                    <div className="filter-box__section__item">
                        <GxFkSelect
                            id="zones"
                            name="zones"
                            label={fieldLabelsMap?.zones ? fieldLabelsMap.zones : trans('label.zone')}
                            placeholder={placeholders?.zone}
                            options={zones ? zones : initialValuesMap[SOURCES.ZONES] ?? []}
                            disabled={disabled || !zones || zones.length === 0}
                            isLoading={loadState[SOURCES.ZONE]}
                        />
                    </div>
                )}
            </>
        </Wrapper>
    );
};

export default GeographyFields;
