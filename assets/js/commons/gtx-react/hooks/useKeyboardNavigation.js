import { useReducer } from 'react';
import { useListener } from './useListener';

const KEYS = {
    up: 38,
    down: 40
};

export const useKeyboardNavigation = (max, enabled) => {
    const [value, dispatch] = useReducer((p, a) => {
        if (typeof a === 'number') {
            p = a;
        } else if (a === '+') {
            p++;
        } else if (a === '-') {
            p--;
        }
        return Math.max(0, Math.min(p, max - 1));
    }, 0);

    function prev() {
        dispatch('-');
    }

    function next() {
        dispatch('+');
    }

    function set(value) {
        dispatch(value);
    }

    function handleKeyDown(evt) {
        let key = evt.which || evt.keyCode;
        switch (key) {
            case KEYS.up:
                evt.preventDefault();
                prev();
                break;
            case KEYS.down:
                evt.preventDefault();
                next();
                break;
        }
    }

    useListener(window, 'keydown', handleKeyDown, enabled);

    return {
        value,
        prev,
        next,
        set
    };
};
