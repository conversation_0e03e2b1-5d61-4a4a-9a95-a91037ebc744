import { useBeforeUnload } from 'react-router-dom';

/**
 *
 * @param hasUnsavedChanges - boolean
 *
 * This hook is used to prompt the user when they try to navigate away from a page with unsaved changes.
 * A simple usage with formik would be: useFormPrompt(formik.dirty);
 *
 */
export const useFormPrompt = (hasUnsavedChanges: boolean) => {
    useBeforeUnload((e) => {
        if (hasUnsavedChanges) {
            e.preventDefault();
        }
    });
};
