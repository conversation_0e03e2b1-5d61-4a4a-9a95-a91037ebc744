import React, { FC, Fragment, useEffect } from 'react';
import { useFormikContext } from 'formik';

const defaultQuerySelector = (errorKey: string): string =>
    `[name="${errorKey}"]`;
/**
 * Simple hook to jump and focus on field wtih error after our custom onSubmit validation
 * @param querySelector optional, useful to find the proper DOM element to scroll to; defaults to a function returning `[name="${errorKey}"], works also with array fields.`
 */
export const useFormikFocusErroredFieldOnSubmit = (
    querySelector = defaultQuerySelector
) => {
    const { errors, submitCount } = useFormikContext();

    const [erroredSubmitNumber, setErroredSubmitNumber] = React.useState<
        number | null
    >(null);

    useEffect(() => {
        const errorKeys = Object.keys(errors);
        const errorValues = Object.values(errors);
        if (
            errorKeys.length &&
            errorValues.length &&
            erroredSubmitNumber !== submitCount
        ) {
            let firstErrorKey: string = '';
            switch (typeof errorValues[0]) {
                case 'string':
                    // se il value corrispondente è una stringa, allora è un input regolare
                    firstErrorKey = errorKeys[0];
                    break;
                case 'object':
                    // altrimenti, se è un array, allora l'input è un array
                    // questo array è vuoto dove non c'è l'errore, oppure ha un oggetto
                    // dove la chiave è l'indice dell'elemento dell'array con errore, mentre
                    // il valore è un altro oggetto con chiave il campo dell'elemento dell'array con errore
                    if (Array.isArray(errorValues[0])) {
                        for (let i = 0; i < errorValues[0].length; i++) {
                            const element = errorValues[0][i];
                            if (element) {
                                firstErrorKey = `${errorKeys[0]}.${i}.${
                                    Object.keys(element)[0]
                                }`;
                                break;
                            }
                        }
                    }
            }
            const erroredInput: HTMLElement | null = document.querySelector(
                querySelector(firstErrorKey)
            );
            if (erroredInput) {
                if (typeof erroredInput.getBoundingClientRect === 'function') {
                    const headerNode = document.querySelector(
                        '.gx-navigation-header'
                    );
                    const headerHeight =
                        headerNode?.getBoundingClientRect().height ?? 0;
                    const yOffset = -headerHeight;

                    const y =
                        erroredInput.getBoundingClientRect().top +
                        window.scrollY +
                        yOffset;

                    window.scrollTo({ top: y, behavior: 'smooth' });
                }
                erroredInput.focus();
                setErroredSubmitNumber(submitCount);
            }
        }
    }, [errors, submitCount]);
};

export const FormikFocusErroredFieldOnSubmit: FC = () => {
    useFormikFocusErroredFieldOnSubmit();
    return <Fragment />;
};
