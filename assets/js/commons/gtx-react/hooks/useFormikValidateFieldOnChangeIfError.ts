import { useFormikContext } from 'formik';
import { useEffect } from 'react';

/**
 * Field is validated on submit: if error, field is validated 'on change' to make error tooltip disappear quickly. Please set also formik's validation options `validateOnChange`, `validateOnBlur` and `validateOnSubmit` to `false`. Under the hood, it uses `validateForm(values)` because it is compatible with both validate function (whole form) as well as validationSchema
 * <AUTHOR>
 * @param fieldName
 */
export const useFormikValidateFieldOnChangeIfError = (fieldName: string) => {
    const {
        getFieldMeta,
        getFieldProps,
        validateForm,
        values,
    } = useFormikContext();

    useEffect(() => {
        if (getFieldMeta(fieldName).error) {
            validateForm(values);
        }
    }, [getFieldProps(fieldName).value]);
};
