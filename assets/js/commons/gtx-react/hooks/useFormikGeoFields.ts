import { SelectProps } from '@gx-design/select';
import { trans } from '@pepita-i18n/babelfish';
import { useFormikContext } from 'formik';
import { useEffect, useState } from 'react';

import { lookupEndpoints, lookupEndpointsWithParam } from 'gtx-react/web-api/lookupEndpoints';
import { ApiResponse } from 'types/api-response';
import { LookupItem } from 'types/lookup';

/**
 * default endpoints; except for 'country', which is a string, all the others are functions returning a string
 */
const GEOENDPOINTS: Record<string, string | ((_param: string) => string)> = {
    country: lookupEndpoints.COUNTRIES,
    // eslint-disable-next-line new-cap
    region: (countryId: string) => lookupEndpointsWithParam.REGIONS({ countryId }),
    // eslint-disable-next-line new-cap
    provinceByCountry: (countryId: string) => lookupEndpointsWithParam.PROVINCES_BY_COUNTRY({ countryId }),
    // eslint-disable-next-line new-cap
    provinceByRegion: (regionId: string) => lookupEndpointsWithParam.PROVINCES_BY_REGION({ regionId }),
    // eslint-disable-next-line new-cap
    city: (provinceId: string) => lookupEndpointsWithParam.CITIES({ provinceId }),
    // eslint-disable-next-line new-cap
    zone: (cityId: string) => lookupEndpointsWithParam.CITY_ZONES({ cityId }),
};

export type GeoField = 'country' | 'region' | 'province' | 'city' | 'zone';

export type GeoFieldInputType = {
    /**
     * required, to know how to deal with all other properties
     */
    type: GeoField;
    /**
     * custom id/name property; defaults to GeoField.type as string
     */
    idKey?: string;
    /**
     * `Array<{ label: string, value: string }>` pre-filled array of options; use only FIXED options (pre-fetched)
     */
    options?: LookupItem[];
    /**
     * If `false`, placeholder will be disabled; defaults to `trans('label.choose')`
     */
    placeholder?: string | false;
    /**
     * If `false`, `isLabelVisible={false}` will be passed as a prop; different default value for every field
     */
    label?: string | false;
    /**
     * If true, resets value as an array; no side-effects on selection; use it on last field only (like zones)
     */
    isMultiSelect?: boolean; // as seen in 'Ricerche' sections for 'zone' field
    /**
     * Defaults to `true`; after a fetch occurred, if the options array is empty, this input will be disabled.
     */
    disableIfNoOptions?: boolean;
    /**
     * Custom endpoint; NOTE: normal fetch will be used instead of pepita/http to properly handle AbortController to avoid race condition. If you really need pepita/http, use `customFetch` prop (no recommended)
     *
     * Custom endpoint can be:
     * - a `string`, for endpoints with no params (useful for `country` type)
     * - a `function`, which replaces the endpoint param in the middle of the string, returning a string
     * - an `object`; `object.url` is the base url where an optional `object.searchParam` is the name of the query param to add at the end as querystring
     * @example
     * '/api/lookup/geography/countries' // string for no-param endpoints
     * '/api/lookup/provinces/${PARAM}/cities' //  function which returns a string, replacing PARAM
     * { url: '/rest/geo?type=cbp', searchParam: 'value' } // => an object, for api with query string like '/rest/geo?type=cbp&value=PARAM'
     */
    customEndpoint?: string | ((_input: string) => string) | { url: string; searchParam?: string };
    /**
     * Custom fetch function, should return a Promise with `data` property filled of items; if it uses the passed abortController, the fetch will be properly 'killed' by this hook
     */
    customFetch?: (_searchParam?: string, _abortController?: AbortController) => Promise<ApiResponse<LookupItem[]>>;
    /**
     * This will be called every time the `options` array changes (when resets too)
     */
    onResultChange?: (_result?: LookupItem[]) => any;
    /**
     * Useful to pre-filter the result of the first geoField by a fixed value
     * @example
     * // Want to use provinces, cities and zone starting with Italian provinces?
     * const getFormikGeoFieldProps = useFormikGeoFieldsInput([
     *      { type: 'province', provinceBy: 'country', fixedFetchParam: 'IT' }, 'city', 'zone'
     * ])
     */
    fixedFetchParam?: string;
    /**
     * Fetch provinces by? Defaults to 'region', applies only when `type === 'province'`
     */
    provinceBy?: 'region' | 'country';
};

/**
 * Hook helper. Throws an error if basic dependency-logic between fields is broken and there is no fixedFetchParam.
 * @param inputMap
 */
const validateHookInput = (inputMap: Record<GeoField, GeoFieldInputType>): void => {
    const entries: Array<[string | GeoField, GeoFieldInputType]> = Object.entries(inputMap);

    for (let i = 0; i < entries.length; i++) {
        const [type, fieldOption] = entries[i];
        if (fieldOption) {
            switch (type as GeoField) {
                case 'country':
                    break;

                case 'region':
                    if (!inputMap['country'] && !fieldOption?.fixedFetchParam) {
                        throw new Error(
                            'useFormikGeoField Invalid input: using "region" without "country" or hookOptions.fixedFirstValue as fixed "country" value!'
                        );
                    }
                    break;

                case 'province': {
                    if (fieldOption.provinceBy === 'region') {
                        if (!inputMap['region'] && !fieldOption?.fixedFetchParam) {
                            throw new Error(
                                'useFormikGeoField Invalid input: using "province" and provinceBy==="region" without "region" or hookOptions.fixedFirstValue as fixed "region" value!'
                            );
                        }
                    }
                    if (fieldOption.provinceBy === 'country') {
                        if (!inputMap['country'] && !fieldOption?.fixedFetchParam) {
                            throw new Error(
                                'useFormikGeoField Invalid input: using "province" and provinceBy==="country" without "country" or hookOptions.fixedFirstValue as fixed "country" value!'
                            );
                        }
                    }
                    break;
                }

                case 'city': {
                    if (!inputMap['province'] && !fieldOption?.fixedFetchParam) {
                        throw new Error(
                            'useFormikGeoField Invalid input: using "city" without "province" or hookOptions.fixedFirstValue as fixed "province" value!'
                        );
                    }
                    break;
                }

                case 'zone': {
                    if (!inputMap['city'] && !fieldOption?.fixedFetchParam) {
                        throw new Error(
                            'useFormikGeoField Invalid input: using "zone" without "city" or hookOptions.fixedFirstValue as fixed "city" value!'
                        );
                    }
                    break;
                }

                default:
                    break;
            }
        }
    }
};

const defaultLabelGenerator = (fieldType: GeoField): string => {
    switch (fieldType) {
        case 'country':
            return trans('label.nation');
        case 'region':
            return trans('label.region');
        case 'province':
            return trans('label.province');
        case 'city':
            return trans('label.municipality');
        case 'zone':
            return trans('label.zone');

        default:
            return trans('label.nation');
    }
};

type GetLookupParamInputType = {
    paramName: string;
    paramValue: string;
};
/** previous version without AbortController with pepita/http */

// const getLookup = (url: string, paramObj: GetLookupParamInputType) => {
//     return http
//         .get(
//             url,
//             paramObj?.paramName && paramObj?.paramValue
//                 ? {
//                       searchParams: {
//                           [`${paramObj.paramName}`]: paramObj.paramValue,
//                       },
//                   }
//                 : undefined
//         )
//         .json();
// };

/** new version replacing pepita/http with fetch, to handle AbortController; this breaks compatibility with old endpoints like `/rest/geo?type=cbp` */
const getLookup = async (url: string, paramObj?: GetLookupParamInputType, abortController?: AbortController) => {
    let processedUrl = new URL(url, window.origin);
    if (paramObj) {
        processedUrl.searchParams.set(paramObj.paramName, paramObj.paramValue);
    }

    const rawResult = await fetch(processedUrl.toString(), {
        signal: abortController?.signal,
    });
    const parsedResult = await rawResult.json();
    return parsedResult;
};

/**
 * useFormikGeoFields auxiliary hook. It handles all the logic about choosing where to fetch data, and fetches it.
 * @param fieldOptions
 * @param searchParam
 * @returns {Array} Object.options - array of <{ label: string, value: string }>
 * @returns {Boolean} Object.isLoading - loading boolean
 */
const useGeoLookup = (fieldOptions: GeoFieldInputType, searchParam?: string) => {
    const [options, setOptions] = useState<LookupItem[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(false);

    const fetchData = async (abortController: AbortController) => {
        if (fieldOptions.type !== 'country' && searchParam === undefined) {
            return;
        }
        try {
            setIsLoading(true);

            let dataToSave: Array<LookupItem> | undefined = undefined;

            if (searchParam === '') {
                // resetting options when upper value is reset
                dataToSave = [];
            } else if (fieldOptions?.options) {
                // plain options to load directly
                dataToSave = [...fieldOptions.options];
            } else if (fieldOptions?.customFetch) {
                const resp = await fieldOptions.customFetch(searchParam, abortController);
                if (resp.status === 'success' && resp.data) {
                    dataToSave = [...resp.data];
                }
            } else {
                const fieldType = fieldOptions.type;
                let endpoint: string | false | undefined;
                let paramObj: GetLookupParamInputType | undefined;

                switch (typeof fieldOptions?.customEndpoint) {
                    case 'string':
                        // endpoint with no params
                        endpoint = fieldOptions.customEndpoint;
                        break;

                    case 'function':
                        // endpoint with param as part of the url (function which returns a string)
                        // example: /api/lookup/geography/countries/${args.countryId}/provinces
                        if (typeof fieldOptions.customEndpoint === 'function' && searchParam) {
                            endpoint = fieldOptions.customEndpoint(searchParam);
                        }
                        break;

                    case 'object':
                        // endpoint with param to send as querystring
                        if ((fieldOptions.customEndpoint as any)?.url) {
                            endpoint = (fieldOptions.customEndpoint as any).url;
                            paramObj = searchParam
                                ? {
                                      paramName: (fieldOptions.customEndpoint as any).searchParam.trim(),
                                      paramValue: searchParam,
                                  }
                                : undefined;
                        }
                        break;

                    default:
                        // here with default endpoints
                        if (fieldType === 'province' && searchParam) {
                            endpoint =
                                fieldOptions?.provinceBy === 'country'
                                    ? typeof GEOENDPOINTS.provinceByCountry === 'function' &&
                                      GEOENDPOINTS.provinceByCountry(searchParam)
                                    : typeof GEOENDPOINTS.provinceByRegion === 'function' &&
                                      GEOENDPOINTS.provinceByRegion(searchParam);
                        } else {
                            if (fieldType === 'country') {
                                endpoint = GEOENDPOINTS.country as string;
                            } else {
                                endpoint = (GEOENDPOINTS[fieldType] as Function)(searchParam);
                            }
                        }
                        break;
                }

                if (!endpoint) {
                    throw new Error(`NO ENDPOINT FOR ${fieldType}`);
                }
                const resp = await getLookup(endpoint, paramObj, abortController);

                // per adattarsi alle vecchie api che restituiscono direttamente un array id, description
                dataToSave = resp?.data
                    ? resp.data
                    : Array.isArray(resp) &&
                      resp.length > 0 &&
                      resp[0].hasOwnProperty('id') &&
                      resp[0].hasOwnProperty('description')
                    ? resp.map((result) => ({
                          label: result.description,
                          value: result.id,
                      }))
                    : [];
            }
            if (dataToSave) {
                setOptions(dataToSave);
            }
            // custom side effect
            if (typeof fieldOptions?.onResultChange === 'function') {
                fieldOptions.onResultChange(dataToSave);
            }
        } catch (error) {
            if (!abortController.signal?.aborted) {
                console.error('useGeoLookup - Error fetching data: ', error);
            }
        } finally {
            setIsLoading(false);
        }
    };

    useEffect(() => {
        const abortController = new AbortController();
        fetchData(abortController);

        return () => {
            abortController.abort();
        };
    }, [searchParam]);

    return { options, isLoading };
};

/**
 * Geography Field's logic without tears! Must be used inside a FORMIK CONTEXT.
 *
 * It handles both options-filling, data-resetting values and aborting useless request with AbortController (normal `fetch` is used instead of `@pepita/http`)
 * @example
 * // Simpliest example, all default values for the options
 * const GeoSection = () => {
    const getFormikGeoFieldProps = useFormikGeoFields([
        'country',
        'region',
        'zone',
        'province',
        'city',
    ]);

    return (
        <>
            <GxFkSelect {...getFormikGeoFieldProps('country')} />
            <GxFkSelect {...getFormikGeoFieldProps('region')} />
            <GxFkSelect {...getFormikGeoFieldProps('province')} />
            <GxFkSelect {...getFormikGeoFieldProps('city')} />
            <GxFkSelect {...getFormikGeoFieldProps('zone')} />
        </>
    );
};
 * @example
 * // Provinces, cities, zone. No labels. Only Italian (IT) provinces. 'city' has custom endpoint with query string, and the id 'citta'
 * const GeoSection = () => {
    const getFormikGeoFieldProps = useFormikGeoFields([
        { type: 'zone', label: false },
        {
            type: 'province',
            provinceBy: 'country',
            fixedFetchParam: 'IT',
            label: false,
        },
        {
            type: 'city',
            idKey: 'citta',
            label: false,
            customEndpoint: {
                url: '/rest/geo?type=cbp',
                searchParam: 'value',
            },
        },
    ]);

    return (
        <>
            <GxFkSelect {...getFormikGeoFieldProps('province')} />
            <GxFkSelect {...getFormikGeoFieldProps('city', 'citta')} />
            <GxFkSelect {...getFormikGeoFieldProps('zone')} />
        </>
    );
};
 */
export const useFormikGeoFields = (inputArray: Array<GeoField | GeoFieldInputType>) => {
    // input map used for everything
    const inputMap: Record<GeoField, GeoFieldInputType> | {} = {};
    let tmpArr: Array<GeoField | false> = [false, false, false, false, false];
    // normalizing input and putting a results into inputMap
    inputArray.forEach((element) => {
        let type: GeoField;
        let provinceBy: GeoFieldInputType['provinceBy'] | undefined;
        let disableIfNoOptions: GeoFieldInputType['disableIfNoOptions'] = true;

        if (typeof element === 'string') {
            type = element;
            if (type === 'province') {
                provinceBy = 'region';
            }
            inputMap[element] = {
                type,
                provinceBy,
                idKey: type,
                disableIfNoOptions,
            };
        } else {
            type = element.type;
            if (type === 'province') {
                provinceBy = typeof element === 'string' ? 'region' : element?.provinceBy || 'region';
            }
            inputMap[element.type] = {
                ...element,
                idKey: element?.idKey || element.type,
                provinceBy,
                disableIfNoOptions,
            };
        }

        switch (type) {
            case 'country':
                tmpArr[0] = 'country';
                break;
            case 'region':
                tmpArr[1] = 'region';
                break;
            case 'province':
                tmpArr[2] = 'province';
                break;
            case 'city':
                tmpArr[3] = 'city';
                break;
            case 'zone':
                tmpArr[4] = 'zone';
                break;

            default:
                break;
        }
    });

    // keeping an ordered array of type fields to use
    const inputTypesArray: Array<GeoField> = tmpArr.filter((elem): elem is GeoField => typeof elem === 'string');

    validateHookInput(inputMap as Record<GeoField, GeoFieldInputType>);

    const { values, getFieldProps, setFieldValue } = useFormikContext<
        unknown & {
            country?: string;
            region?: string;
            province?: string;
            city?: string;
            zone?: string;
        }
    >();

    const lookups: Record<GeoField, { options: LookupItem[]; isLoading: boolean } | undefined> = {
        country: inputMap['country'] ? useGeoLookup({ ...inputMap['country'] }) : undefined,

        region: inputMap['region']
            ? inputMap['region']?.options
                ? { options: inputMap['region']?.options, isLoading: false }
                : useGeoLookup(
                      { ...inputMap['region'] },
                      inputMap['region']?.fixedFetchParam || values[inputMap['country'].idKey]
                  )
            : undefined,

        province: inputMap['province']
            ? inputMap['province']?.options
                ? { options: inputMap['province']?.options, isLoading: false }
                : useGeoLookup(
                      { ...inputMap['province'] },
                      inputMap['province']?.fixedFetchParam
                          ? inputMap['province'].fixedFetchParam
                          : inputMap['province'].provinceBy === 'country'
                          ? values[inputMap['country']?.idKey]
                          : values[inputMap['region']?.idKey]
                  )
            : undefined,

        city: inputMap['city']
            ? inputMap['city']?.options
                ? { options: inputMap['city']?.options, isLoading: false }
                : useGeoLookup(
                      { ...inputMap['city'] },
                      inputMap['city']?.fixedFetchParam || values[inputMap['province'].idKey]
                  )
            : undefined,

        zone: inputMap['zone']
            ? inputMap['zone']?.options
                ? { options: inputMap['zone']?.options, isLoading: false }
                : useGeoLookup(
                      { ...inputMap['zone'] },
                      inputMap['zone']?.fixedFetchParam || values[inputMap['city'].idKey]
                  )
            : undefined,
    };

    /**
     *
     * @param fieldType 'country' or 'region' or ...
     * @param fieldId custom field id, should also be included as `idKey` for the same field into hook's array input
     * @returns spreading the result of formik's getFieldProps(fieldId) plus: id, name, label, isLabelVisible, placeholder, options, isLoading, disabled, onChange; if `isMultiSelect: true` for this field, returns the original `onChange` with no side-effects
     */
    const getFormikGeoFieldProps = (
        fieldType: GeoField,
        fieldId?: string
    ): Omit<SelectProps, 'name'> & { isLoading?: boolean; name: string } => {
        const fieldName = fieldId ?? fieldType;

        const fieldIndex = inputTypesArray.indexOf(fieldType);
        const fieldOption = inputMap[fieldType];

        if (!inputMap[fieldType]) {
            throw new Error(
                `useFormikGeoFields Invalid usage: using field ${fieldType} not specified inside hoook's array input`
            );
        }
        if (fieldName !== fieldOption.idKey) {
            throw new Error(
                `useFormikGeoFields Invalid usage: using fieldId ${fieldName} not specified inside hoook's array input for field ${fieldType}`
            );
        }

        const shouldBeDisabled = (): boolean => {
            if (fieldIndex > 0) {
                const previousSiblingOption = inputMap[inputTypesArray[fieldIndex - 1]];

                const previousSiblingValue = values[previousSiblingOption.idKey];

                if (!previousSiblingValue || (fieldOption.disableIfNoOptions && !lookups[fieldType]?.options?.length)) {
                    return true;
                }
            }
            return false;
        };

        return {
            options: [],
            ...getFieldProps(fieldName),
            id: fieldName,
            name: fieldName,
            label: fieldOption?.label !== false ? fieldOption.label || defaultLabelGenerator(fieldType) : undefined,
            isLabelVisible: fieldOption?.label !== false,
            placeholder:
                fieldOption?.placeholder === false ? undefined : fieldOption?.placeholder || trans('label.choose'),
            ...lookups[fieldType],
            disabled: shouldBeDisabled(),
            onChange: (e: React.ChangeEvent<any>) => {
                if (fieldOption?.isMultiSelect) {
                    return getFieldProps(fieldName).onChange;
                }

                getFieldProps(fieldName).onChange(e);

                for (let i = fieldIndex + 1; i < inputTypesArray.length; i++) {
                    const siblingFieldType = inputTypesArray[i];
                    const siblingFieldOption =
                        inputMap[inputTypesArray[inputTypesArray.findIndex((elem) => elem === siblingFieldType)]];

                    setFieldValue(siblingFieldOption.idKey, siblingFieldOption.isMultiSelect ? [] : '');
                }
            },
        };
    };

    return getFormikGeoFieldProps;
};

export default useFormikGeoFields;
