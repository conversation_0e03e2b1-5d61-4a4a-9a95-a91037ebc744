import { useFormikContext, useField } from 'formik';

/**
 * Simple hook handling a numeric input (no formatting), into a formik form (represented as a string), preventing user to type non-digit chars, and giving also the possibility to have a maximum number of digits.
 * @param fieldName formik field's name
 * @param maximumDigits optional maximum number of digits
 */
export const useFormikSimpleNumField = (fieldName: string, maximumDigits?: number) => {
    const { setFieldValue } = useFormikContext();
    const [fieldProps, fieldMeta] = useField(fieldName);

    const onChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value;
        if (value && !Number(value)) {
            return;
        }
        if (maximumDigits && value.length > maximumDigits) {
            return;
        }
        setFieldValue(fieldName, value);
    };

    return {
        fieldProps: {
            ...fieldProps,
            onChange,
        },
        fieldMeta,
    };
};
