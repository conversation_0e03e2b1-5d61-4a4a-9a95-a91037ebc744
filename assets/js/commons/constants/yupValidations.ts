import { trans } from '@pepita-i18n/babelfish';
import gtxConstants from '@getrix/common/js/gtx-constants';
import * as Yup from 'yup';

const REGEX_EMAIL = new RegExp(gtxConstants('REGEX_EMAIL'));

export const requiredPhone = (message: string = trans('label.required_value')) => Yup.string().required(message);

export const requiredPhoneWithoutPrefix = () =>
    requiredPhone().matches(
        new RegExp(window.gtxConstants.REGEX_PHONE_STRICT_NO_PREFIX),
        trans('label.form.error.phone.no_prefix_or_separator')
    );

export const email = () =>
    Yup.string().matches(REGEX_EMAIL, trans('label.insert_valid_mail_2')).email(trans('label.insert_valid_mail_2'));

export const requiredEmail = () =>
    Yup.string()
        .required(trans('label.required_value'))
        .matches(REGEX_EMAIL, trans('label.insert_valid_mail_2'))
        .email(trans('label.insert_valid_mail_2'));
