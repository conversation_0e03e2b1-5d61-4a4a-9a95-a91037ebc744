import { validateLegacy<PERSON><PERSON><PERSON><PERSON> } from 'lib/REST/helpers/utils';
import { LookupItemList } from 'lib/REST/types/shared';

getProvinces.endpoint = '/acquisizione/annunci-privati/initial-data/provinces' as const;

export function getProvinces() {
    return validateLegacyT<PERSON><PERSON><PERSON><{ provinces: { data: LookupItemList; type: string } }>(fetch(getProvinces.endpoint));
}
