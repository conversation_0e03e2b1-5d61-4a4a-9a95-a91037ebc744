import { CustomerServiceRequestDataType } from '../../../../../../base/pages/landing-pages/types';
import { http } from '@pepita/http';
import { CUSTOMER_SERVICE_REQUEST_POST } from '../../../../../../base/pages/landing-pages/web-api/endpoints';
import { useMutation } from '@tanstack/react-query';

export const sendCustomerServiceRequest = async (data: CustomerServiceRequestDataType): Promise<void> => {
    const response = await http
        .post(CUSTOMER_SERVICE_REQUEST_POST, {
            form: data,
        })
        .raw();

    if (!response?.ok) {
        console.error('CustomerServiceRequest failed', response);
        throw new Error('CustomerServiceRequest: result is not ok');
    }
};

export const useCustomerServiceRequest = () => {
    return useMutation({
        mutationFn: sendCustomerServiceRequest,
    });
};
