import { withFormData } from 'lib/api';
import { RESTArgs } from 'lib/REST/types/shared';

type TosUpdateType = {
    updateCheck: boolean;
};

putUpdateTos.endpoint = '/api/tos' as const;

export async function putUpdateTos(
    args: RESTArgs<{
        params: TosUpdateType;
    }>
) {
    const response = await fetch(putUpdateTos.endpoint, {
        method: 'POST',
        body: withFormData({ updateCheck: args.params.updateCheck ? '1' : '0' }),
    });

    return response.json();
}
