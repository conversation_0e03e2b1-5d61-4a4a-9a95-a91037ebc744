import { validateT<PERSON><PERSON><PERSON> } from 'lib/REST/helpers/utils';
import { NotificationSettingsType, PutNotificationSettingsType } from 'lib/REST/types/notification-settings';
import { RESTArgs } from 'lib/REST/types/shared';

getNotificationSettings.endpoint = '/api/configurations/searches/notification-settings' as const;

export function getNotificationSettings() {
    return validateToJson<NotificationSettingsType>(fetch(getNotificationSettings.endpoint));
}

putNotificationSettings.endpoint = '/api/configurations/searches/notification-settings' as const;

export function putNotificationSettings(
    args: RESTArgs<{
        body: PutNotificationSettingsType;
    }>
) {
    return validateToJson<NotificationSettingsType>(
        fetch(putNotificationSettings.endpoint, {
            method: 'PUT',
            body: JSON.stringify(args.body),
        })
    );
}
