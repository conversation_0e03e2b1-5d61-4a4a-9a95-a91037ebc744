import { isApiSuccess } from 'lib/api';
import { ApiResponse } from 'types/api-response';
import { ExpectedRestError } from './errors/ExpectedRestError';
import { UnexpectedRestError } from './errors/UnexpectedRestError';

/**
 * Get the JSON from the response, or throw {@link UnexpectedRestError} an error if it's not possible
 * @param response
 * @returns
 */
const getJson = async (response: Response) => {
    try {
        return await response.json();
    } catch (err) {
        throw new UnexpectedRestError(response);
    }
};

/**
 * Get the BLOB from the response, or throw {@link UnexpectedRestError} an error if it's not possible
 * @param response
 * @returns
 */
export const getBlob = async (response: Response) => {
    try {
        return await response.blob();
    } catch (err) {
        throw new UnexpectedRestError(response);
    }
};

/**
 * Validate the response to JSON, or throw an error if the response is an error
 * @throws {ExpectedRestError} - if the response is an error
 * @throws {UnexpectedRestError} - if the response is not a JSON
 */
export const validateToJson = async <Success extends any, Error extends any = any>(
    awaitableFetch: Promise<Response>
) => {
    const response = await awaitableFetch;
    const safeResponse = (await getJson(response)) as ApiResponse<Success, Error>;

    if (isApiSuccess(safeResponse)) {
        return safeResponse.data;
    }

    throw new ExpectedRestError(safeResponse);
};

/**
 * Use this function only for legacy endpoints that doesn't return the object `{ status: 'success' | 'error', data: any }`
 * Validate the response to JSON, or throw an error if the response is an error
 * @throws {UnexpectedRestError} - if the response is not a JSON or an error
 */
export const validateLegacyToJson = async <Success extends any>(awaitableFetch: Promise<Response>) => {
    const response = await awaitableFetch;

    if (!response.ok) {
        throw new UnexpectedRestError(response);
    }

    const safeResponse = await getJson(response);

    return safeResponse as Success;
};

/**
 * Validate the response to BLOB, or throw an error if the response is an error
 * @throws {ExpectedRestError} - if the response is an error
 * @throws {UnexpectedRestError} - if the response is not a BLOB
 */
export const validateToBlob = async <Success extends any>(awaitableFetch: Promise<Response>) => {
    const response = await awaitableFetch;
    const safeResponse = await getBlob(response);

    if (!response.ok) {
        throw new UnexpectedRestError(response);
    }

    return safeResponse as Success;
};
