/**
 * ```window?.['gtxLoggedUser'];```
 */
export type GtxLoggedUser = {
    email?: string;
    getrixVersion?: number;
    hasGetrixVersion?: number;
    idAgente?: string;
    isAgencyLight?: number;
    name?: string;
    roles?: {
        ROLE_SUPER_AMMINISTRATORE: boolean;
        ROLE_AMMINISTRATORE: boolean;
        ROLE_USER_BACKOFFICE: boolean;
    };
    surname?: string;
    telephone?: string;
    hasGetrixPlus: 1 | 0 | boolean;
    language?: string;
    hasMessaging?: boolean;
    agencyId?: string;
    agentUuid?: string;
};
