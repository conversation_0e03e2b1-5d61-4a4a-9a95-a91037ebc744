'use strict';

let $ = require('jquery-extend'),
    ImmobiliareFlux = require('immobiliare-flux'),
    PinVerify = require('./pin-verify'),
    Store = require('../store/store'),
    MessageConfig = require('../message-config'),
    bootbox = require('../lib/bootbox'),
    { trans } = require('@getrix/common/js/lib/translator'),
    gtxConstants = require('@getrix/common/js/gtx-constants');

module.exports = ImmobiliareFlux.createComponent({
    initialize: function () {
        this.errorOn = false;
        this.currentState = Store.getState();
    },
    events: {
        'click [data-action=submit-phone-number]': function (evt, _this) {
            evt.preventDefault();
            _this.submitPhoneNumber();
        },
        'click [data-action=show-customer-service]': function () {
            bootbox.customerCareBox('numberSelection');
        },
        'submit [data-role=form-phone-number-selection]': function (evt, _this) {
            evt.preventDefault();
            _this.submitPhoneNumber();
        }
    },
    initializeListeners: function ($input) {
        let _this = this;

        this.listenTo(Store, 'success', function () {
            let newState = Store.getState();
            if (_this.currentState.action === Store.getActionPhoneNumberSelected()) {
                if (newState.action === Store.getActionPinValidated() && !!newState.targetUrl) {
                    location.href = newState.targetUrl;
                } else if (newState.action === Store.getActionPinValidation()) {
                    let insertPinComp = ImmobiliareFlux.render(PinVerify, {
                        randSeed: Math.floor(Math.random() * 1000),
                        nPhoneNumbers: _this.nPhoneNumbers
                    });
                    $('[data-role=form-container]').html(insertPinComp.$el);
                    _this.currentState = newState;
                }
            }
        });

        this.listenTo(Store, 'error', function () {
            let newState = Store.getState();
            if (newState.action === Store.getActionPhoneNumberSelected() && newState.action === _this.currentState.action) {
                _this.setError(_this.getErrorMsg(newState.error), $input);
                _this.changeStatusSubmit(true);
                _this.currentState = newState;
            }
        });
    },
    changeStatusSubmit: function (enable) {
        let $submit = this.$el.find('[data-action=submit-phone-number]');

        if (enable) {
            $submit.removeAttr('disabled');
            $submit.html(
                `${trans('a2f.number_selection.send_sms')}`
            )
        } else {
            $submit.attr('disabled', 'disabled');
            $submit.html(
                `<span data-role="spinner">
                    <svg class="gx-icon gx-spin">
                        <use xlink:href="${gtxConstants('ICONS_SVG_SPRITE_FILE_PATH')}#loader_icon"></use>
                    </svg>
                </span>`
            )
        }
    },
    setError: function (msg, $element) {
        let $errorContainer = null;
        if (this.errorOn === (msg === '')) {
            $errorContainer = this.$el.find('[data-role=error-message]');
            $errorContainer.find('.gx-helper__text').text(msg);
            $errorContainer.removeClass('hidden');
            if (msg === '') {
                this.errorOn = false;
                $element.removeClass('gx-input--negative');
                $errorContainer.addClass('hidden');
            } else {
                this.errorOn = true;
                $element.addClass('gx-input--negative');
            }
        }
    },
    getErrorMsg: function (errorCode) {
        let message = '';
        switch (errorCode) {
            case Store.getErrorBadRequest():
                message = MessageConfig.numberSelection.errorInvalidPhoneNumber.message;
                break;
            default:
                message = MessageConfig.numberSelection.errorGeneric.message;
        }
        return message;
    },
    isPhoneNumberValid: function (phoneNumber) {
        let oReg = new RegExp(gtxConstants('REGEX_PHONE_MOBILE'));
        return !!phoneNumber && phoneNumber !== '' && !!phoneNumber.match(oReg);
    }
});
