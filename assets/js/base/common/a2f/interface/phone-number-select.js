'use strict';

let ImmobiliareFluxInput = require('immobiliare-flux').basicComponents.Input,
    PhoneNumberSelection = require('./phone-number-selection'),
    StyledSelect = require('styled-select'),
    Action = require('../action/action');

module.exports = PhoneNumberSelection.extend({
    components: {
        'phone-number': ImmobiliareFluxInput
    },
    initialize: function () {
        this.phoneNumberComp = this.childs.get('phone-number');
        this.nPhoneNumbers = this.phoneNumberComp.$el.find('option').length;

        StyledSelect.initialize('.gx-select');
        StyledSelect.update('.gx-select');

        PhoneNumberSelection.prototype.initialize.call(this);

        PhoneNumberSelection.prototype.initializeListeners.call(this, this.phoneNumberComp.$el);
    },
    getPhoneNumber: function () {
        return $.trim(this.phoneNumberComp.$el.find('option:selected').text());
    },
    submitPhoneNumber: function () {
        let contactId = this.phoneNumberComp.get(),
            urlParts = window.location.href.split('?')[0].split('/');

        this.changeStatusSubmit(false);

        this.setError('', this.phoneNumberComp.$el);
        Action.sendPinToContact(contactId, urlParts[urlParts.length - 1]);
    }
});
