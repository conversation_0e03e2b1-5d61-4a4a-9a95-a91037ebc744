import { NotifyProvider } from '@gx-design/snackbar';
import { ActiveSearchesPage } from './pages/ActiveSearchesPage';
import { GtxApp } from 'gtx-react/components/GtxApp/GtxApp';
import { ActiveSearchesMixpanelProvider } from './contexts/mixpanel/ActiveSearchesMixpanelProvider';
import { MswDeveloperBoundary } from 'gtx-react/components/MswDeveloperBoundary';
import { QueryClientProvider } from '@tanstack/react-query';
import { queryClient } from './utils';

export const App = () => {
    return (
        <QueryClientProvider client={queryClient}>
            <ActiveSearchesMixpanelProvider>
                <NotifyProvider>
                    <MswDeveloperBoundary isDisabled={true}>
                        <GtxApp pages={[ActiveSearchesPage]} />
                    </MswDeveloperBoundary>
                </NotifyProvider>
            </ActiveSearchesMixpanelProvider>
        </QueryClientProvider>
    );
};
