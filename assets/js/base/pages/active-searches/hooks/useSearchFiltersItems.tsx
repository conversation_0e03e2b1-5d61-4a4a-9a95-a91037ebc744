import { trans } from '@pepita-i18n/babelfish';
import { ActiveSearchItem } from '../types/ActiveSearches';
import { getFromToString } from '../utils/getFromToString';
import { ListItemProps } from '@gx-design/list';
import { ReactNode } from 'react';
import { AUCTIONS_STATE_IDS } from '../constants/ActiveSearchesAuctionStates';

type SearchFilterConfig = {
    name: string;
    show: boolean;
    icon: ListItemProps['icon'];
    content: () => string | ReactNode;
};

export const useSearchFiltersItems = (search: ActiveSearchItem['search']) => {
    const surfaceString = getFromToString(search.surface, trans('label.m2'));
    const roomsString = getFromToString(search.rooms, trans('label.rooms'));

    const allSearchFilters: SearchFilterConfig[] = [
        {
            name: 'planimetry',
            show: search.surface !== null || search.rooms !== null,
            icon: 'planimetry',
            content: () =>
                `${surfaceString}${
                    roomsString && surfaceString ? ' - ' : ''
                } ${roomsString}`,
        },
        {
            name: 'bathrooms',
            show: !!search.bathrooms,
            icon: 'bath',
            content: () => getFromToString(search.bathrooms, ''),
        },
        {
            name: 'floorRanges',
            show: search.floorRanges && search.floorRanges.length > 0,
            icon: 'stairs',
            content: () =>
                `${trans('label.floor')}: ${search.floorRanges
                    .map((itm) => trans(`db_floor_category.id_${itm.id}`))
                    .join(', ')}`,
        },
        {
            name: 'propertyFeatures',
            show:
                search.propertyFeatures &&
                search.propertyFeatures.findIndex((itm) => itm.value === true) >
                    -1,
            icon: 'beach-umbrella',
            content: () =>
                `${search.propertyFeatures
                    .filter((itm) => itm.value === true)
                    .map((itm) => trans(`label.${itm.name}`))
                    .join(', ')}`,
        },
        {
            name: 'propertyCondition',
            show: !!search.propertyCondition?.id,
            icon: 'helmet',
            content: () =>
                trans(`db_status.id_${search.propertyCondition?.id}`),
        },
        {
            name: 'garage',
            show: search.garages && search.garages.length > 0,
            icon: 'garage',
            content: () =>
                `${search.garages
                    .map((itm) => trans(`ui_boxauto.id_${itm.id}`))
                    .join(', ')}`,
        },
        {
            name: 'energyClass',
            show: !!search.energyEfficiency,
            icon: 'radiator',
            content: () => (
                <>
                    <div>{trans('label.energy_class')}:</div>
                    <div>
                        {trans(
                            `db_energy_rating.id_${search.energyEfficiency?.id}`
                        )}
                    </div>
                </>
            ),
        },
        {
            name: 'gardens',
            show: search.gardens && search.gardens.length > 0,
            icon: 'tree',
            content: () =>
                `${search.gardens
                    .map((itm) => trans(`db_garden.id_${itm.id}`))
                    .join(', ')}`,
        },
        {
            name: 'auctionsExcluded',
            show: search.auctions?.id === AUCTIONS_STATE_IDS.AUCTIONS_EXCLUDED,
            icon: 'hammer',
            content: () => trans('label.exclude_auctions'),
        },
    ];

    return {
        searchFiltersItems: allSearchFilters.filter((itm) => itm.show),
    };
};
