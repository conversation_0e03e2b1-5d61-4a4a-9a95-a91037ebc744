import { CardList } from 'gtx-react/components/CardList/CardList';
import { FC } from 'react';
import { ActiveSearchItem } from '../types/ActiveSearches';
import { activeSearchesListCardsConfig } from '../constants/ActiveSearchesListTableConfig';
import { useActiveSearchesListTableActions } from '../hooks/useActiveSearchesListTableActions';
import { CardListConfigs } from 'gtx-react/components/CardList/types';

type ActiveSearchesCardListProps = {
    data: ActiveSearchItem[];
    config: CardListConfigs;
};
export const ActiveSearchesCardList: FC<ActiveSearchesCardListProps> = ({
    data,
    config,
}) => {
    const tableActions = useActiveSearchesListTableActions();

    return (
        <div className="active-searches-cardlist">
            <CardList
                actions={{
                    main: null,
                    quick: [],
                    menu: null,
                    bulk: null,
                    ...tableActions,
                }}
                configs={{
                    ...config,
                }}
                cardsData={{
                    items: data.map((itm) => ({
                        id: itm.uuid,
                        ...itm,
                    })),
                    extraItemFields: undefined,
                }}
                cardFields={activeSearchesListCardsConfig}
                twoColumnsView
            />
        </div>
    );
};
