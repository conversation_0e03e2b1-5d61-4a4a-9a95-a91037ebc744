import { captureException } from '@sentry/browser';
import { useCallback } from 'react';
import * as Yup from 'yup';

export type IFrameErrorMsg = {
    type: 'error';
    payload: {
        code: `5${number}${number}`; // String of 3 digits starting with 5
        message: string;
    };
};

/**
 * Schema for error message from iframe
 */
const errorMessageSchema = Yup.object().shape({
    type: Yup.string().oneOf(['error']).required(),
    payload: Yup.object()
        .shape({
            code: Yup.string()
                .matches(/^[5]\d{2}$/, 'The code must be a string of 3 digits starting with 5')
                .required(),
            message: Yup.string().required(),
        })
        .required(),
});

/**
 * Validate error message from iframe
 * @param message - message from iframe
 * @returns {boolean} - true if message is valid, false otherwise
 */
const validateErrorMessage = (message: IFrameErrorMsg): boolean => {
    try {
        errorMessageSchema.validateSync(message);
        return true;
    } catch (error) {
        // Emit Sentry error
        captureException(error, {
            extra: {
                errorMessage: 'Invalid message from IFrame',
                messageFromIframe: message,
            },
        });
        console.error('Invalid message from IFrame', error);

        return false;
    }
};

/**
 * Hook to handle error message from iframe
 * @returns {handleErrorMessage} - function to handle error message
 */
export const useHandleErrorMessage = () => {
    const handleErrorMessage = useCallback((data: IFrameErrorMsg) => {
        if (!validateErrorMessage(data)) {
            return;
        }

        // Emit Sentry error
        captureException(new Error(`[${data.payload.code}] ${data.payload.message}`), {
            extra: {
                errorMessage: 'Error message from IFrame',
                messageFromIframe: data,
            },
        });
    }, []);

    return { handleErrorMessage };
};
