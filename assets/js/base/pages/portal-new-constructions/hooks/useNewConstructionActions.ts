import { useDispatch, useSelector } from 'react-redux';
import { useNotifyContext } from '@gx-design/snackbar';
import { useDetailPrintContext } from './useDetailPrintContext';
import { trans } from '@pepita-i18n/babelfish';
import { CACHE_DETAIL_KEY_PREFIX } from '../constants';
import {
    addPremiumSpaceToProperty,
    deleteProperty,
    deleteProperties,
    removePremiumSpaceFromProperty,
    revertPropertyField,
    searchLoadingStart,
    searchLoadingEnd,
    setPropertyField,
    updateCounters,
    updateTotalCount,
    updateEcommerceProducts,
    updatePropertiesSpaces,
} from '../actions';
import {
    bulkDeleteNewConstructionsApi,
    bulkArchiveNewConstructionsApi,
    bulkActivateNewConstructionsApi,
    favouriteNewConstructionApi,
    deleteNewConstructionApi,
    archiveNewConstructionApi,
    activateNewConstructionApi,
    getNewConstructionsCountByStatus<PERSON><PERSON>,
    getNewConstructionDetail<PERSON>pi,
    setNewConstructionIntegrationFlagApi,
    getEcommerceProductsApi,
    togglePropertyPremiumVisibilityApi,
} from '../web-api/api';
import { getPropertiesSpacesApi } from '../../portal-properties/web-api/api';
import { endpoints } from '../web-api/endpoints';
import { useQueryClient } from '@tanstack/react-query';
import { IPortalNewConstructionsListItem } from '../types/list';
import { ucFirst } from 'lib/strings-formatter';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { IReduxRootState } from '../types/redux';
import { useAlertContext } from './useAlertContext';
import { parseActivationErrors } from '../utils/errors';

const useNewConstructionActions = () => {
    const dispatch = useDispatch();
    const { setAlert } = useAlertContext();
    const { showNotification } = useNotifyContext();
    const { setDataToPrint } = useDetailPrintContext();
    const { trackEvent } = useMixpanelContext();
    const queryClient = useQueryClient();

    const properties = useSelector<IReduxRootState, IReduxRootState['properties']>((data) => data.properties);

    const searchFilters = useSelector<IReduxRootState, { [key: string]: string | number }>(
        (data: IReduxRootState) => data.searchFilters as IReduxRootState['searchFilters']
    );

    const getNewConstructionEditLink = (id: IPortalNewConstructionsListItem['id'], step: number) =>
        endpoints.NEW_CONSTRUCTION_EDIT.replace('{step}', '' + step).replace('{id}', id);

    const handleEditNewConstruction = (id: IPortalNewConstructionsListItem['id'], step: number, track: boolean) => {
        if (track) {
            handleTrackEditNewConstruction(id);
        }

        location.href = getNewConstructionEditLink(id, step);
    };

    const handleDeleteNewConstruction = (id: IPortalNewConstructionsListItem['id']) => {
        dispatch(searchLoadingStart());
        // Track deletion before the API call, to avoid an "Entity not found exception".
        trackEvent({
            event: 'properties_delete_listing',
            extra: {
                ['property_id']: id,
                ['bulk_action']: false,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
        deleteNewConstructionApi(id)
            .then(() => {
                dispatch(searchLoadingEnd());
                dispatch(deleteProperty(id));
                showNotification({
                    type: 'success',
                    message: trans('ad.deleted_success'),
                });
            })
            .then(() => getNewConstructionsCountByStatusApi())
            .then((res: any) => {
                dispatch(updateCounters(res.data));
                dispatch(updateTotalCount(1, 'minus'));
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleBulkDeleteNewConstructions = (ids: IPortalNewConstructionsListItem['id'][]) => {
        dispatch(searchLoadingStart());
        // Track deletion before the API call, to avoid an "Entity not found exception".
        ids.map((id) => {
            trackEvent({
                event: 'properties_delete_listing',
                extra: {
                    ['property_id']: id,
                    ['bulk_action']: true,
                    ['list_tab']: ucFirst('' + searchFilters.status),
                },
            });
        });

        bulkDeleteNewConstructionsApi({ ids } as any)
            .then(() => {
                dispatch(searchLoadingEnd());
                dispatch(deleteProperties(ids));
                showNotification({
                    type: 'success',
                    message: trans('ad.deleted_success_plural'),
                });
            })
            .then(() => getNewConstructionsCountByStatusApi())
            .then((res: any) => {
                dispatch(updateCounters(res.data));
                dispatch(updateTotalCount(ids.length, 'minus'));
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleTrackEditNewConstruction = (id: IPortalNewConstructionsListItem['id']) => {
        trackEvent({
            event: 'properties_edit_listing',
            extra: {
                ['property_id']: id,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
    };

    const handlePrintDetail = (id: IPortalNewConstructionsListItem['id']) => {
        trackEvent({
            event: 'properties_print_listing',
            extra: {
                ['property_id']: id,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
        dispatch(searchLoadingStart());

        const cachedDetail = queryClient.getQueryData([`${CACHE_DETAIL_KEY_PREFIX}${id}`]);

        if (cachedDetail) {
            dispatch(searchLoadingEnd());
            setDataToPrint(cachedDetail);
            return;
        }

        getNewConstructionDetailApi(id)
            .then((res) => {
                dispatch(searchLoadingEnd());
                queryClient.setQueryData([`${CACHE_DETAIL_KEY_PREFIX}${id}`], res.data);
                setDataToPrint(res.data);
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleArchiveNewConstruction = (id: IPortalNewConstructionsListItem['id']) => {
        dispatch(searchLoadingStart());
        archiveNewConstructionApi(id)
            .then(() => {
                dispatch(searchLoadingEnd());
                dispatch(deleteProperty(id));
                showNotification({
                    type: 'success',
                    message: trans('ad.archived_success'),
                });
                trackEvent({
                    event: 'properties_archive_listing',
                    extra: {
                        ['property_id']: id,
                        ['bulk_action']: false,
                        ['list_tab']: ucFirst('' + searchFilters.status),
                    },
                });
            })
            .then(() =>
                Promise.all([
                    getNewConstructionsCountByStatusApi(),
                    getEcommerceProductsApi(),
                    getPropertiesSpacesApi(),
                ])
            )
            .then((res: any) => {
                dispatch(updateCounters(res[0].data));
                dispatch(updateEcommerceProducts(res[1].data));
                dispatch(updatePropertiesSpaces(res[2].data));
                dispatch(updateTotalCount(1, 'minus'));
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleActivationError = (extraData) => {
        if (extraData) {
            setAlert({
                style: 'error',
                type: 'activationErrors',
                content: parseActivationErrors(extraData),
            });
            return;
        }

        showNotification({
            type: 'error',
            message: trans('label.error_durin_operation'),
        });
    };

    const handleActivateNewConstruction = (id: IPortalNewConstructionsListItem['id']) => {
        setAlert(null);
        dispatch(searchLoadingStart());
        activateNewConstructionApi(id)
            .then((res) => {
                dispatch(searchLoadingEnd());

                if (res.data.code == 400) {
                    const propertyData: IPortalNewConstructionsListItem | undefined = properties.find(
                        (property) => property.id === id
                    );

                    res.data.extra[0]['code'] = propertyData ? propertyData.code : '';

                    return Promise.reject({
                        type: 'activation-error',
                        payload: res.data.extra,
                    });
                }

                dispatch(deleteProperty(id));
                showNotification({
                    type: 'success',
                    message: trans('ad.activated_success'),
                });
                trackEvent({
                    event: 'properties_unarchive_listing',
                    extra: {
                        ['property_id']: id,
                        ['bulk_action']: false,
                        ['list_tab']: ucFirst('' + searchFilters.status),
                    },
                });
            })
            .then(() =>
                Promise.all([
                    getNewConstructionsCountByStatusApi(),
                    getEcommerceProductsApi(),
                    getPropertiesSpacesApi(),
                ])
            )
            .then((res: any) => {
                dispatch(updateCounters(res[0].data));
                dispatch(updateEcommerceProducts(res[1].data));
                dispatch(updatePropertiesSpaces(res[2].data));
                dispatch(updateTotalCount(1, 'minus'));
            })
            .catch((err) => {
                dispatch(searchLoadingEnd());

                if (err && err.type === 'activation-error') {
                    handleActivationError(err.payload);
                    return;
                }

                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleBulkActivateNewConstructions = (ids: IPortalNewConstructionsListItem['id'][]) => {
        setAlert(null);
        dispatch(searchLoadingStart());
        bulkActivateNewConstructionsApi({ ids } as any)
            .then((res) => {
                dispatch(searchLoadingEnd());

                if (res.data.code == 400) {
                    const failedPropertyId = res.data.extra.map((item) => item.adId);

                    dispatch(deleteProperties(ids.filter((id) => failedPropertyId.indexOf(id) === -1)));

                    const payload = res.data.extra.map((item) => {
                        const propertyData: IPortalNewConstructionsListItem | undefined = properties.find(
                            (property) => property.id === item.adId
                        );

                        return { ...item, code: propertyData ? propertyData.code : '' };
                    });

                    return Promise.reject({
                        type: 'activation-error',
                        payload,
                    });
                }

                dispatch(deleteProperties(ids));
                showNotification({
                    type: 'success',
                    message: trans('ad.activated_success_plural'),
                });

                ids.map((id) => {
                    trackEvent({
                        event: 'properties_unarchive_listing',
                        extra: {
                            ['property_id']: id,
                            ['bulk_action']: true,
                            ['list_tab']: ucFirst('' + searchFilters.status),
                        },
                    });
                });
            })
            .then(() =>
                Promise.all([
                    getNewConstructionsCountByStatusApi(),
                    getEcommerceProductsApi(),
                    getPropertiesSpacesApi(),
                ])
            )
            .then((res: any) => {
                dispatch(updateCounters(res[0].data));
                dispatch(updateEcommerceProducts(res[1].data));
                dispatch(updatePropertiesSpaces(res[2].data));
                dispatch(updateTotalCount(ids.length, 'minus'));
            })
            .catch((err) => {
                dispatch(searchLoadingEnd());

                if (err && err.type === 'activation-error') {
                    handleActivationError(err.payload);
                    return;
                }

                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleBulkArchiveNewConstructions = (ids: IPortalNewConstructionsListItem['id'][]) => {
        dispatch(searchLoadingStart());
        bulkArchiveNewConstructionsApi({ ids } as any)
            .then(() => {
                dispatch(searchLoadingEnd());
                dispatch(deleteProperties(ids));
                showNotification({
                    type: 'success',
                    message: trans('ad.archived_success_plural'),
                });
                ids.map((id) => {
                    trackEvent({
                        event: 'properties_archive_listing',
                        extra: {
                            ['property_id']: id,
                            ['bulk_action']: true,
                            ['list_tab']: ucFirst('' + searchFilters.status),
                        },
                    });
                });
            })
            .then(() =>
                Promise.all([
                    getNewConstructionsCountByStatusApi(),
                    getEcommerceProductsApi(),
                    getPropertiesSpacesApi(),
                ])
            )
            .then((res: any) => {
                dispatch(updateCounters(res[0].data));
                dispatch(updateEcommerceProducts(res[1].data));
                dispatch(updatePropertiesSpaces(res[2].data));
                dispatch(updateTotalCount(ids.length, 'minus'));
            })
            .catch(() => {
                dispatch(searchLoadingEnd());
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleSetNewConstructionIntegrationFlag = (id: IPortalNewConstructionsListItem['id'], status: boolean) => {
        dispatch(setPropertyField(id, 'integrationFlag', status));
        setNewConstructionIntegrationFlagApi(id, status).catch(() => {
            dispatch(revertPropertyField(id, 'integrationFlag', !status));
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        });
    };

    const handleSetNewConstructionFavourite = (id: IPortalNewConstructionsListItem['id'], status: boolean) => {
        trackEvent({
            event: status ? 'properties_mark_as_favorite_listing' : 'properties_unmark_as_favorite_listing',
            extra: {
                ['property_id']: id,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
        dispatch(setPropertyField(id, 'favourite', status));
        favouriteNewConstructionApi(id, status).catch(() => {
            dispatch(revertPropertyField(id, 'favourite', !status));
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        });
    };

    const handleActivateNewConstructionPremium = (id: IPortalNewConstructionsListItem['id']) => {
        const propertyData = properties.find((property) => property.id === id);

        if (!propertyData) {
            return;
        }

        trackEvent({
            event: 'properties_activate_premium_on_listing',
            extra: {
                ['property_id']: id,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });

        dispatch(addPremiumSpaceToProperty(id));

        return togglePropertyPremiumVisibilityApi(id, !propertyData.extraVisibilities.searchable)
            .then(() => getEcommerceProductsApi())
            .then((res: any) => {
                dispatch(updateEcommerceProducts(res.data));
            })
            .catch(() => {
                dispatch(removePremiumSpaceFromProperty(id));
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    const handleDeactivateNewConstructionPremium = (id: IPortalNewConstructionsListItem['id']) => {
        const propertyData = properties.find((property) => property.id === id);

        if (!propertyData) {
            return;
        }

        trackEvent({
            event: 'properties_deactivate_premium_on_listing',
            extra: {
                ['property_id']: id,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });

        dispatch(removePremiumSpaceFromProperty(id));

        return togglePropertyPremiumVisibilityApi(id, !propertyData.extraVisibilities.searchable)
            .then(() => getEcommerceProductsApi())
            .then((res: any) => {
                dispatch(updateEcommerceProducts(res.data));
            })
            .catch(() => {
                dispatch(addPremiumSpaceToProperty(id));
                showNotification({
                    type: 'error',
                    message: trans('label.error_durin_operation'),
                });
            });
    };

    return {
        activateNewConstructionAction: handleActivateNewConstruction,
        activateNewConstructionPremiumAction: handleActivateNewConstructionPremium,
        archiveNewConstructionAction: handleArchiveNewConstruction,
        bulkActivateNewConstructionsAction: handleBulkActivateNewConstructions,
        bulkArchiveNewConstructionsAction: handleBulkArchiveNewConstructions,
        bulkDeleteNewConstructionsAction: handleBulkDeleteNewConstructions,
        deactivateNewConstructionPremiumAction: handleDeactivateNewConstructionPremium,
        deleteNewConstructionAction: handleDeleteNewConstruction,
        editNewConstructionAction: handleEditNewConstruction,
        getNewConstructionEditLinkAction: getNewConstructionEditLink,
        printNewConstructionDetailAction: handlePrintDetail,
        setNewConstructionIntegrationFlagAction: handleSetNewConstructionIntegrationFlag,
        setNewConstructionFavouriteAction: handleSetNewConstructionFavourite,
        trackEditNewConstructionAction: handleTrackEditNewConstruction,
    };
};

export default useNewConstructionActions;
