import React from 'react';
import { ImprovePropertyQualityContainer } from '../../../../commons/gtx-react/containers/ImprovePropertyQualityContainer';
import { IPropertyListItem } from '../../../../commons/types/api/property';
import { endpoints } from '../web-api/endpoints';

type ImproveNewConstructionQualityProps = {
    propertyData: IPropertyListItem;
};

export const ImprovePortalNewConstructionQuality = ({
    propertyData,
}: ImproveNewConstructionQualityProps) => {
    return (
        <ImprovePropertyQualityContainer
            propertyData={propertyData}
            configName="portalNewConstruction"
            endpoints={endpoints}
        />
    );
};
