import React, { useEffect, useRef } from 'react';
import { useAlertContext } from '../hooks/useAlertContext';
import { trans } from '@pepita-i18n/babelfish';

const ErrorItem = ({ error }) => {
    return (
        <>
            {error.code ? `${error.code} (${error.id})` : error.id}
            <br />
            {error.errors.map((errorItem, index) => (
                <span key={`error_item_${index}`}>
                    <span className="msg_erorre_attivazione">{errorItem}</span>
                    <br />
                </span>
            ))}
            <br />
            <br />
        </>
    );
};

export const PortalNewConstructionsActivationErrors = () => {
    const { alert } = useAlertContext();
    const alertContent = useRef<HTMLInputElement>(undefined);

    if (!alert) {
        return null;
    }

    useEffect(() => {
        if (!alertContent || !alertContent.current) {
            return;
        }

        alertContent.current.parentElement.parentElement.scrollIntoView();
    }, [alert]);

    return (
        <span ref={alertContent}>
            <div className="error-title">
                <strong>{trans('ads_activation_failed')}</strong>
            </div>
            <span>{trans('ads_activation_failed_codes')}</span>
            <br />
            <br />
            {alert.content.map((error) => (
                <ErrorItem key={error.id} error={error} />
            ))}
        </span>
    );
};
