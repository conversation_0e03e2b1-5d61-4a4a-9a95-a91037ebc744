import React from 'react';
import { createRoot } from 'react-dom/client';
import { Provider as SlotProvider } from '@gestionale-immobiliare/react-slot-fill';
import { SentryErrorHandler } from 'gtx-react/sentry';
import { Error } from 'gtx-react/components/Error/Error';
import App from './app';

const container = document.getElementById('content');
const root = createRoot(container);

root.render(
    <SentryErrorHandler fallback={<Error />}>
        <SlotProvider>
            <App />
        </SlotProvider>
    </SentryErrorHandler>
);
