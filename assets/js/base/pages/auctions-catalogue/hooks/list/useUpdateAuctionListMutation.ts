import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateAuction } from 'lib/REST/requests/immobili/aste/catalogo';
import { createAuctionsQueryOptions } from 'lib/REST/requests/immobili/aste/catalogo/query-factory';
import { useAppSearchParams } from '../../context/useAppSearchParams';
import { AUCTION_RECAP_PATH } from '../../web-api/endpoints';
import gtxConstants from '@getrix/common/js/gtx-constants';

export const useUpdateAuctionListMutation = (auctionId: string) => {
    const queryClient = useQueryClient();
    const searchParams = useAppSearchParams();

    const { showNotification } = useNotifyContext();

    const { queryKey } = createAuctionsQueryOptions({ query: searchParams });

    return useMutation({
        mutationFn: (auctionId: string) => updateAuction(auctionId),
        onSuccess: () => {
            queryClient.setQueryData(queryKey, (old) => {
                if (!old) {
                    return undefined;
                }

                return {
                    ...old,
                    auctions: old.auctions.map((auction) => {
                        if (auction.id === auctionId) {
                            return {
                                ...auction,
                                updated: false,
                            };
                        }

                        return auction;
                    }),
                };
            });

            window.location.assign(
                AUCTION_RECAP_PATH.replace(':id', auctionId)
                    .replace(':type', gtxConstants('CATEGORIA_ASTE'))
                    .replace(':category', gtxConstants('CATEGORIA_ASTE'))
            );
        },
        onError: () => {
            showNotification({
                type: 'error',
                message: trans('label.error_durin_operation'),
            });
        },
    });
};
