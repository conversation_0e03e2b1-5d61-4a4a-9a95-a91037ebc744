import { Suspense } from 'react';
import { Loader } from '@gx-design/loader';
import { Error } from 'gtx-react/components/Error/Error';
import { ErrorBoundary } from 'react-error-boundary';
import { DetailSection } from '../components/detail/DetailSection';
import { useSuspenseQuery } from '@tanstack/react-query';
import { createAuctionByIdQueryOptions } from 'lib/REST/requests/immobili/aste/catalogo/query-factory';
import { useAuctionIdParams } from '../hooks/detail/useAuctionIdParams';

export const AuctionsCatalogueDetailView = () => {
    return (
        <ErrorBoundary FallbackComponent={() => <Error />}>
            <Suspense fallback={<Loader variant="fixed" />}>
                <AuctionsCatalogueDetail />
            </Suspense>
        </ErrorBoundary>
    );
};

const AuctionsCatalogueDetail = () => {
    const id = useAuctionIdParams();

    const { data: auction } = useSuspenseQuery(
        createAuctionByIdQueryOptions({ params: { auctionId: id } })
    );

    return <DetailSection auction={auction} />;
};
