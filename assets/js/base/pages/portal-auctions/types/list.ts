import { IEcommerceProduct, IPropertyListItem } from 'types/api/property';

export interface IPortalAuctionsListItem extends IPropertyListItem {}
export interface IEcommerceProductItem extends IEcommerceProduct {}

export interface IPortalAuctionsListFilters {
    code?: string;
    country?: string;
    region?: string;
    province?: string;
    city?: string;
    zones?: string;
    favourite?: boolean;
    agent?: string;
    priceFrom?: string;
    priceTo?: string;
    mandateFrom?: string;
    mandateTo?: string;
    visibility?: string;
    status?: string;
    sort?: string;
}
