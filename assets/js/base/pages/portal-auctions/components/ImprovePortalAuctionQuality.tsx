import React from 'react';
import { ImprovePropertyQualityContainer } from '../../../../commons/gtx-react/containers/ImprovePropertyQualityContainer';
import { endpoints } from '../web-api/endpoints';
import { IPropertyListItem } from '../../../../commons/types/api/property';

type ImproveAuctionQualityProps = {
    propertyData: IPropertyListItem;
};

export const ImprovePortalAuctionQuality = ({
    propertyData,
}: ImproveAuctionQualityProps) => {
    return (
        <ImprovePropertyQualityContainer
            propertyData={propertyData}
            configName="portalAuction"
            endpoints={endpoints}
        />
    );
};
