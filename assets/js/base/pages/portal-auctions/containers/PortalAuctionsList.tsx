import { useState, createContext, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { trans } from '@pepita-i18n/babelfish';
import { MediaQuery } from 'gtx-react/components';
import { Toolbar } from '@gx-design/toolbar';
import {
    TABLE_LABELS,
    TOOLBAR_LABELS,
    PORTAL_AUCTIONS_VIEWPORT_MEDIA_QUERIES,
    PROPERTY_STATUS_ACTIVE_LABEL,
    LIST_QS_VALID_FILTERS_KEYS,
} from '../constants';
import { PortalAuctionsBigScreenDevicesList } from '../components/PortalAuctionsBigScreenDevicesList';
import { PortalAuctionsSmallScreenDevicesList } from '../components/PortalAuctionsSmallScreenDevicesList';
//@ts-ignore
import { Loader as SectionLoader } from 'gtx-react/containers/Loader';
import { IReduxRootState } from '../types/redux';
import { useDispatch, useSelector } from 'react-redux';
import { createSelector } from 'reselect';
import { PortalAuctionsListFooter } from '../components/PortalAuctionsListFooter';
import { listSearch } from '../actions';
import { IPagination } from 'types/pagination';
import { IPortalAuctionsListFilters } from '../types/list';
import useToolbarNavigation from '../hooks/useToolbarNavigation';
import useListActions from '../hooks/useListActions';
import useListField from '../hooks/useListField';
import { formatNumberToLocale } from 'lib/formatter';
import { SORT_ITEMS } from '../constants';
import { PortalAuctionsListFilters } from '../components/PortalAuctionsListFilters';
import {
    getSortingDirection,
    getSortingDirectionSymbol,
} from '../utils/sorting';
import { setQueryString, sanitizeObject } from 'lib/utility';
import { BASE_PATH, endpoints } from '../web-api/endpoints';
import { getValidationErrorsApi } from '../web-api/api';
import { isSearchActive } from '../utils/searchFilters';
import { SortingDirectionLabel, SortingDirectionSymbol } from 'types/sorting';
import { ENDPOINTS } from 'constants/property';
import { ucFirst } from 'lib/strings-formatter';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { TableActions, TableConfigs } from 'gtx-react/components/GxTable/types';
import { SECRET_PROPERTY_VISIBILITY_KEY } from 'constants/propertyVisibilities';
import { IEcommerceProduct } from 'types/api/property';

const QUERY_CONFIG = {
    refetchIntervalInBackground: false,
    refetchOnWindowFocus: false,
    refetchOnMount: false,
    staleTime: 600 * 1000,
};

interface IPortalAuctionsListContext {
    ecommerceProducts: IReduxRootState['ecommerceProducts'];
    listConfigs: TableConfigs;
    listActions: TableActions | undefined;
    listItems: any;
    renderMapping: {};
}

export const PortalAuctionsListContext =
    createContext<IPortalAuctionsListContext | null>(null);

const hasFilters = createSelector(
    (state: IReduxRootState) => state.searchFilters,
    (searchFilters) => isSearchActive(searchFilters)
);

export const PortalAuctionsList = () => {
    const dispatch = useDispatch();

    useQuery({
        queryKey: ['validation_errors'],
        queryFn: () => getValidationErrorsApi().then((res) => res.data),
        ...QUERY_CONFIG,
    });

    const properties = useSelector<IReduxRootState>((data) => data.properties);

    const totalPropertiesCount = useSelector<IReduxRootState, string>(
        (data: IReduxRootState) =>
            data.totalCount as IReduxRootState['totalCount']
    );

    const searchFilters = useSelector<
        IReduxRootState,
        { [key: string]: string | number }
    >(
        (data: IReduxRootState) =>
            data.searchFilters as IReduxRootState['searchFilters']
    );

    const countersByStatus = useSelector<IReduxRootState>(
        (data: IReduxRootState) => data.counters
    );

    const ecommerceProducts = useSelector<
        IReduxRootState,
        IReduxRootState['ecommerceProducts']
    >((data: IReduxRootState) =>
        data.ecommerceProducts.filter(
            (item: IEcommerceProduct) =>
                item.service !== SECRET_PROPERTY_VISIBILITY_KEY
        )
    );
    const pagination = useSelector<IReduxRootState, IPagination>(
        (data: IReduxRootState) => data.pagination
    );

    const isFiltersSearchActive = useSelector(hasFilters);

    const [isFiltersModalOpen, setIsFiltersModalOpen] = useState(false);

    const toolbarNavigationItems = useToolbarNavigation(
        searchFilters,
        countersByStatus
    );

    const { trackEvent } = useMixpanelContext();

    const handleInputSearchAction = (searchText: string) => {
        const searchParams = {
            ...(searchFilters as IReduxRootState['searchFilters']),
            code: searchText,
        };
        trackEvent({
            event: 'properties_search_selection',
            extra: {
                code: searchText,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
        setQueryString(BASE_PATH, searchParams);
        dispatch(
            listSearch(
                {
                    page: 1,
                    results: pagination.results,
                },
                searchParams
            )
        );
    };

    const handleSortingAction = (key: string, direction: string) => {
        trackEvent({
            event: 'properties_sort_list',
            extra: {
                ['sorting_column']: key,
                ['sorting_criteria']: direction,
                ['listings_number']: totalPropertiesCount,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
        const sortingValue = `${getSortingDirectionSymbol(
            direction as SortingDirectionLabel
        )}${key}`;
        const newSearchFilters = sanitizeObject({
            objectToClean: searchFilters,
            validKeys: LIST_QS_VALID_FILTERS_KEYS,
            cleanEmptyStringsFrom: ['visibility'],
        });
        setQueryString(BASE_PATH, {
            ...(newSearchFilters as IReduxRootState['searchFilters']),
            code: null,
            sort: sortingValue,
        });
        dispatch(
            listSearch(
                {
                    page: 1,
                    results: pagination.results,
                },
                {
                    ...(newSearchFilters as IReduxRootState['searchFilters']),
                    sort: sortingValue,
                    code: null,
                }
            )
        );
    };

    const handleSearchResetAction = () => {
        const searchParams = {
            ...(searchFilters as IReduxRootState['searchFilters']),
            code: null,
            status: PROPERTY_STATUS_ACTIVE_LABEL,
        };
        setQueryString(BASE_PATH, searchParams);
        dispatch(
            listSearch(
                {
                    page: 1,
                    results: pagination.results,
                },
                searchParams
            )
        );
    };

    const handleRemoveFiltersAction = () => {
        const searchParams = {
            code: null,
            status: (searchFilters as IReduxRootState['searchFilters']).status,
        };
        setQueryString(BASE_PATH, searchParams);
        dispatch(
            listSearch(
                {
                    page: 1,
                    results: pagination.results,
                },
                searchParams
            )
        );
    };

    const handleNavigateAction = (type) => {
        if (!type) {
            return null;
        }

        const navigationSearch = {
            status: type,
            code: null,
        };

        setQueryString(BASE_PATH, { status: type });

        dispatch(
            listSearch(
                {
                    page: 1,
                    results: pagination.results,
                },
                navigationSearch
            )
        );
    };

    const { actions: listActions, getAuctionActions } = useListActions({
        sortingAction: handleSortingAction,
    });
    const renderMapping = useListField();

    const listConfigs = {
        itemSelection: true,
        itemActionsHelper: getAuctionActions,
        labels: TABLE_LABELS,
        emptyState: {
            text: trans('label.no_results_found'),
            image: '/bundles/base/getrix/common/img/empty-state/empty-state.png',
            buttonLabel: trans('label.add'),
            buttonOnClick: () => (location.href = ENDPOINTS.auctionAdd),
        },
    };

    useEffect(() => {
        trackEvent({
            event: 'properties_portal_area_access',
            extra: {
                ['listings_number']: totalPropertiesCount,
                ['list_tab']: ucFirst('' + searchFilters.status),
            },
        });
    }, [searchFilters.status]);

    return (
        <>
            <SectionLoader />
            <PortalAuctionsListContext.Provider
                value={{
                    listConfigs,
                    listActions,
                    listItems: properties,
                    ecommerceProducts,
                    renderMapping,
                }}
            >
                <Toolbar
                    labels={TOOLBAR_LABELS}
                    isFiltersSearchActive={isFiltersSearchActive}
                    searchedText={
                        (searchFilters as IPortalAuctionsListFilters)?.code ??
                        ''
                    }
                    onShowFiltersAction={() => setIsFiltersModalOpen(true)}
                    onInputSearchAction={(value) =>
                        handleInputSearchAction(value)
                    }
                    onNavigateAction={(value) => handleNavigateAction(value)}
                    onResetInputSearchAction={handleSearchResetAction}
                    onRemoveFiltersAction={handleRemoveFiltersAction}
                    searchItemsCount={formatNumberToLocale(
                        parseInt(totalPropertiesCount as string)
                    )}
                >
                    <Toolbar.Navigation
                        navigationItems={toolbarNavigationItems}
                    />
                    <Toolbar.Actions
                        sortingConfig={{
                            sortItems: SORT_ITEMS,
                            currentSortKey: searchFilters.sort
                                ? (searchFilters.sort as string).substring(1)
                                : '',
                            currentSortDirection: searchFilters.sort
                                ? getSortingDirection(
                                      (searchFilters.sort as string).charAt(
                                          0
                                      ) as SortingDirectionSymbol
                                  )
                                : '',
                            onSortAction: handleSortingAction,
                        }}
                    />
                    <Toolbar.Results />
                </Toolbar>
                <PortalAuctionsListFilters
                    isOpen={isFiltersModalOpen}
                    onClose={() => setIsFiltersModalOpen(false)}
                />
                <MediaQuery queries={PORTAL_AUCTIONS_VIEWPORT_MEDIA_QUERIES}>
                    {(match) =>
                        match.isDesktop || match.isLargeDesktop ? (
                            <PortalAuctionsBigScreenDevicesList />
                        ) : (
                            <PortalAuctionsSmallScreenDevicesList />
                        )
                    }
                </MediaQuery>
                <PortalAuctionsListFooter />
                {parseInt(totalPropertiesCount) > 0 && (
                    <div className="csv-bar">
                        <a href={endpoints.DOWNLOAD_PROPERTIES_CSV}>
                            <span>{trans('label.export_ads_excel')}</span>
                        </a>
                    </div>
                )}
            </PortalAuctionsListContext.Provider>
        </>
    );
};
