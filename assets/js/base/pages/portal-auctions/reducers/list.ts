import { Reducer } from 'redux';
import { handleActions } from 'redux-actions';
//@ts-ignore
import gtxConstants from 'gtx-constants';
import { IPortalAuctionsListItem } from '../types/list';
import * as actions from '../../../../commons/gtx-react/actions/list';
import {
    deletePropertyCreator,
    deletePropertiesCreator,
    searchLoadingStart,
    searchLoadingEnd,
    setPropertyFieldCreator,
    revertPropertyFieldCreator,
    updateCountersCreator,
    updateTotalCountCreator,
    updateEcommerceProductsCreator,
    updatePropertiesSpacesCreator,
    addPremiumSpaceToPropertyCreator,
    removePremiumSpaceFromPropertyCreator,
} from '../actions';
import { IPortalAuctionsListApiResponse } from '../types/apiResponse';
import { IReduxRootState } from '../types/redux';

export const properties: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                properties,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!properties) {
                return [];
            }

            return [...properties];
        },
        [setPropertyFieldCreator](state, action) {
            let { id, field, value } = action.payload;

            if (!id || !field) {
                return state;
            }

            const properties = [...state];
            const propertyData = properties.find(
                property => property.id === id
            );
            propertyData[field] = value;

            return [...properties];
        },
        [revertPropertyFieldCreator](state, action) {
            let { id, field, value } = action.payload;

            if (!id || !field) {
                return state;
            }

            const properties = [...state];
            const propertyData = properties.find(
                property => property.id === id
            );
            propertyData[field] = value;

            return [...properties];
        },
        [deletePropertyCreator](state, action) {
            let { id } = action.payload;

            if (!id) {
                return state;
            }

            return [...state].filter(property => property.id !== id);
        },
        [deletePropertiesCreator](state, action) {
            let { ids } = action.payload;

            if (!ids || !ids.length) {
                return state;
            }

            return [...state].filter(
                property => ids.indexOf(property.id) === -1
            );
        },
        [addPremiumSpaceToPropertyCreator](state, action) {
            let { id } = action.payload;

            if (!id) {
                return state;
            }

            return [...state].map(property => {
                if (property.id === id) {
                    return {
                        ...property,
                        extraVisibilities: {
                            ...property.extraVisibilities,
                            searchable: true,
                        },
                    };
                }

                return property;
            });
        },
        [removePremiumSpaceFromPropertyCreator](state, action) {
            let { id } = action.payload;

            if (!id) {
                return state;
            }

            return [...state].map(property => {
                if (property.id === id) {
                    return {
                        ...property,
                        extraVisibilities: { searchable: false },
                    };
                }

                return property;
            });
        },
    },
    {}
);

export const pagination: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                pagination,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!pagination) {
                return state;
            }

            return pagination;
        },
    },
    {}
);

export const paginationOptions: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                paginationOptions,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!paginationOptions) {
                return state;
            }

            return paginationOptions;
        },
    },
    {}
);

export const totalCount: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                totalCount,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!totalCount) {
                return state;
            }

            return totalCount;
        },
        [updateTotalCountCreator](state, action) {
            let { quantity, operation } = action.payload;

            if (!quantity || !operation) {
                return state;
            }

            if (operation === 'minus') {
                return state - quantity;
            }

            if (operation === 'add') {
                return state + quantity;
            }
        },
    },
    {}
);

export const propertiesSpaces: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                propertiesSpaces,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!propertiesSpaces) {
                return state;
            }

            return propertiesSpaces;
        },
        [updatePropertiesSpacesCreator](state, action) {
            let { propertiesSpaces } = action.payload;

            if (!propertiesSpaces) {
                return state;
            }

            return [...propertiesSpaces];
        },
        [addPremiumSpaceToPropertyCreator](state, action) {
            let { id } = action.payload;

            if (!id) {
                return state;
            }

            const propertiesSpaces = [...state];

            return propertiesSpaces.map(item => {
                if (item.type.id === gtxConstants('CONTRATTO_VENDITA')) {
                    return {
                        ...item,
                        available: item.available - 1,
                        applied: item.applied + 1,
                    };
                }

                return item;
            });
        },
        [removePremiumSpaceFromPropertyCreator](state, action) {
            let { id } = action.payload;

            if (!id) {
                return state;
            }

            const propertiesSpaces = [...state];

            return propertiesSpaces.map(item => {
                if (item.type.id === gtxConstants('CONTRATTO_VENDITA')) {
                    return {
                        ...item,
                        available: item.available + 1,
                        applied: item.applied - 1,
                    };
                }

                return item;
            });
        },
    },
    {}
);

export const ecommerceProducts: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                ecommerceProducts,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!ecommerceProducts) {
                return state;
            }

            return ecommerceProducts;
        },
        [updateEcommerceProductsCreator](state, action) {
            let { ecommerceProducts } = action.payload;

            if (!ecommerceProducts) {
                return state;
            }

            return [...ecommerceProducts];
        },
    },
    {}
);

export const counters: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                counters,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!counters) {
                return state;
            }

            return counters;
        },
        [updateCountersCreator](state, action) {
            let {
                counters,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!counters) {
                return state;
            }

            return { ...counters };
        },
    },
    {}
);

export const agency: Reducer<IPortalAuctionsListItem[]> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let {
                agency,
            } = action.payload as IPortalAuctionsListApiResponse['data'];

            if (!agency) {
                return state;
            }

            return agency;
        },
    },
    {}
);

export const listLoader = handleActions(
    {
        [searchLoadingStart]() {
            return { isLoading: true };
        },
        [searchLoadingEnd]() {
            return { isLoading: false };
        },
    },
    {
        isLoading: false,
    }
);

export const matchesAndThreadsCounters: Reducer<IReduxRootState['matchesAndThreadsCounters']> = handleActions(
    {
        [actions.paginationEnd](state, action) {
            let { matchesAndThreadsCounters } = action.payload;

            if (!matchesAndThreadsCounters) {
                return state;
            }

            return matchesAndThreadsCounters;
        },
    },{}
)