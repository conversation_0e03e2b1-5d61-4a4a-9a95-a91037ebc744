import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { Image } from 'gtx-react/components';
import { ListField, ListItem } from 'gtx-react/components/List';
import React from 'react';
import { usePartitionList } from '../../hooks/usePartitionList';
import {
    getPropertyImageUrl,
    getVisibileColumnsByMediaQuery,
} from '../../utils';
import { usePortalsTableMediaQueryContext } from './PortalsTableMediaQueryContext';

export function PortalsTableBodyRow<T>({
    children,
    constructionDescriptions,
    imageId,
    fallbackImageId,
    list,
    renderHiddenList,
    previewUrl,
}: {
    imageId: string;
    fallbackImageId: string;
    constructionDescriptions: string[];
    list: T[];
    previewUrl: string;
    renderHiddenList: (
        child: T[],
        modal: { onClose: () => void; isOpen: boolean }
    ) => React.ReactElement;
    children: (child: T, index: number) => React.ReactElement;
}) {
    const match = usePortalsTableMediaQueryContext();
    const [visibleList, hiddenList] = usePartitionList(
        list,
        getVisibileColumnsByMediaQuery(match)
    );
    const [isModalOpen, setIsModalOpen] = React.useState(false);

    const onClose = React.useCallback(() => {
        setIsModalOpen(false);
    }, []);

    return (
        <ListItem className="multisend-item">
            <ListField className="multisend-item__field multisend-item__fieldProperty">
                <div className="property-block">
                    <Image
                        className="property-block__photo"
                        src={getPropertyImageUrl(imageId)}
                        fallbackSrc={fallbackImageId}
                    />
                    <div className="property-block__desc">
                        {constructionDescriptions.map((description) => (
                            <div
                                key={description}
                                className="property-block__desc__field"
                            >
                                {description}
                            </div>
                        ))}
                    </div>
                </div>
            </ListField>
            {visibleList && visibleList.length > 0
                ? visibleList.map(children)
                : null}
            <ListField className="multisend-item__field multisend-item__fieldActions">
                {hiddenList.length > 0
                    ? renderHiddenList(hiddenList, {
                          onClose,
                          isOpen: isModalOpen,
                      })
                    : null}
                {hiddenList.length > 0 ? (
                    <Button
                        title={trans('label.other_sites')}
                        onClick={() => setIsModalOpen(true)}
                    >
                        {trans('label.other_sites')}
                    </Button>
                ) : null}
                <Button
                    title={trans('label.preview')}
                    as="a"
                    href={previewUrl}
                    target="_blank"
                    className="preview"
                    iconOnly
                >
                    <Icon name="eye" />
                    <span className="gx-is-hidden-md-up">
                        {trans('label.preview')}
                    </span>
                </Button>
            </ListField>
        </ListItem>
    );
}
