import { Button } from '@gx-design/button';
import { trans } from '@pepita-i18n/babelfish';
import classnames from 'classnames';
import { ListField, ListItem, ViewportMediaQuery } from 'gtx-react/components';
import { StickyListHead } from 'gtx-react/components/List';
import React from 'react';
import { usePartitionList } from '../../hooks/usePartitionList';
import { getVisibileColumnsByMediaQuery } from '../../utils';

import { usePortalsTableMediaQueryContext } from './PortalsTableMediaQueryContext';

export function PortalsTableHeaderRow<T>({
    children,
    list,
    renderHiddenList,
}: {
    children: (child: T, index: number) => React.ReactElement;
    renderHiddenList: (
        child: T[],
        modal: { isOpen: boolean; onClose: () => void }
    ) => React.ReactElement;
    list: T[];
}) {
    const match = usePortalsTableMediaQueryContext();
    const [visibleList, hiddenList] = usePartitionList(
        list,
        getVisibileColumnsByMediaQuery(match)
    );
    const [isModalOpen, setIsModalOpen] = React.useState(false);

    const onClose = React.useCallback(() => {
        setIsModalOpen(false);
    }, []);

    return (
        <ViewportMediaQuery>
            {({ isDesktop }) =>
                isDesktop ? (
                    <StickyListHead className="list__head--portals">
                        <ListItem>
                            <ListField
                                key="info"
                                className="portal-publish-all-info"
                            >
                                <div>{trans('label.send_of_all_ads')}</div>
                            </ListField>
                            {visibleList.map(children)}
                            <ListField className="other-portal-action">
                                <div
                                    className={classnames([
                                        'other-portal-action__action',
                                        hiddenList &&
                                            hiddenList.length === 0 &&
                                            'hidden',
                                    ])}
                                >
                                    {hiddenList.length > 0 &&
                                        renderHiddenList(hiddenList, {
                                            isOpen: isModalOpen,
                                            onClose,
                                        })}
                                    {hiddenList.length > 0 ? (
                                        <Button
                                            title={trans('label.other_sites')}
                                            onClick={() => setIsModalOpen(true)}
                                        >
                                            {trans('label.other_sites')}
                                        </Button>
                                    ) : null}
                                </div>
                            </ListField>
                        </ListItem>
                    </StickyListHead>
                ) : null
            }
        </ViewportMediaQuery>
    );
}
