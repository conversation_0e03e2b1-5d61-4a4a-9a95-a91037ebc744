import { useMutation, useQueryClient } from '@tanstack/react-query';
import produce from 'immer';
import { UseMultiSendPortalsQueryData, getMultiSendPortalsQueryKey } from '../../../hooks/useMultiSendPortalsQuery';
import { isImmobiliareId } from '../../../utils';
import { togglePortalForNewConstruction } from '../../../web-api/real';
import { UseNewConstructionsQueryData, getNewConstructionsQueryKey } from './useNewConstructionsQuery';

export const useToggleActivationPortalMutation = () => {
    const queryClient = useQueryClient();

    return useMutation({
        mutationFn: togglePortalForNewConstruction,
        onSuccess: (response, { portalId, status, propertyId }) => {
            if (response.status === 'success') {
                queryClient.setQueriesData<UseMultiSendPortalsQueryData>(
                    { queryKey: getMultiSendPortalsQueryKey({ context: 'projects' }) },
                    produce((data) => {
                        const portalFound = data?.find((f) => f.portal.id === portalId);
                        if (portalFound) {
                            portalFound.advertisementSpaces = response.data.advertisementSpaces;
                            portalFound.propertiesPublishStatus = response.data.propertiesPublishStatus;
                        }
                    })
                );

                queryClient.setQueriesData<UseNewConstructionsQueryData>(
                    { queryKey: getNewConstructionsQueryKey() },
                    produce((data) => {
                        if (
                            data?.publicationStatus &&
                            data.publicationStatus[propertyId] &&
                            data.publicationStatus[propertyId][portalId]
                        ) {
                            const construction = data.publicationStatus[propertyId];
                            const foundPortal = construction[portalId];
                            foundPortal.linkBadge = response.data.linkBadge;
                            foundPortal.searchableBadge = { message: '', status: false };

                            if (isImmobiliareId(portalId) && status === false) {
                                foundPortal.published = status;
                            }
                        }
                    })
                );
            }
        },
    });
};
