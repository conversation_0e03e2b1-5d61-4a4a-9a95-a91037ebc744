import { usePropertyPortalContext } from '../PropertyPortalProvider';
import { useActivationMutation } from '../hooks/useActivationMutation';
import { useIsSearchableMutation } from '../hooks/useIsSearchableMutation';
import { isImmobiliareId, projectsTotalCounter, propertiesSentCounter } from '../../../utils';
import { useNotifyContext } from '@gx-design/snackbar';
import { trans } from '@pepita-i18n/babelfish';
import { usePortalIsMutating } from '../hooks/usePortalIsMutating';

export const usePortalActions = (props: { onLimitReached: () => void }) => {
    const { portal, propertyStatus, property } = usePropertyPortalContext();
    const { showNotification } = useNotifyContext();
    const isPortalLoading = usePortalIsMutating(portal.portal.id);
    const isActive = propertyStatus.published;

    const { mutate: activationMutate, isPending: isActivationLoading } = useActivationMutation();

    const { mutate: isSearchableMutate, isPending: isSearchableLoading } = useIsSearchableMutation();

    const hasLimitReached = () => {
        const diff = projectsTotalCounter(portal) - propertiesSentCounter(portal);
        if (isNaN(diff)) {
            // if diff is NaN, it means that the portal has no limit
            return false;
        }
        return diff <= 0;
    };

    const onError = () => {
        showNotification({
            message: trans('label.error_durin_operation', {
                portalName: portal.portal.name,
            }),
            type: 'error',
        });
    };

    const onSuccess = () => {
        showNotification({
            message: trans(!isActive ? 'label.request_send_to_portal' : 'portals.request_remove_executed', {
                portalName: portal.portal.name,
            }),
            type: 'success',
        });
    };

    const onToggleActivation = () => {
        if (!isActive && hasLimitReached() && !isImmobiliareId(portal.portal.id)) {
            return props.onLimitReached();
        }

        activationMutate(
            {
                portalId: portal.portal.id,
                status: !isActive,
                propertyId: property.id,
            },
            {
                onSuccess: (response, variables) => {
                    if (response.status === 'error') {
                        return onError();
                    }
                    if (isImmobiliareId(variables.portalId) && !isActive) {
                        isSearchableMutate(
                            {
                                portalId: variables.portalId,
                                propertyId: variables.propertyId,
                            },
                            { onSuccess, onError }
                        );
                    } else {
                        onSuccess();
                    }
                },
                onError,
            }
        );
    };

    return {
        isLoading: isPortalLoading || isActivationLoading || isSearchableLoading,
        onToggleActivation,
    };
};
