import { ApiResponse, MultiSendPortal } from '.';

export type NewConstructionsResponse = ApiResponse<NewConstructionData>;

export type NewConstructionData = {
    properties: Construction[];
    searchFilters: SearchFilters;
    pagination: Pagination;
    publicationStatus: PublicationStatus;
};

export type PublicationStatus = {
    [constructionId: string]: { [portalId: string]: ConstructionStatusDetail };
};

export interface ConstructionStatusDetail {
    id: number;
    published: boolean;
    zonesBadge?: any;
    linkBadge: LinkBadge[];
    errorBadge: ErrorBadge;
    searchableBadge: SearchableBadge;
}

export interface LinkBadge {
    backLink: string;
    idPortal: number;
    namePortal: string;
    visibilityLink?: string;
    backlinkLoading: boolean;
    backlinkDisabled: boolean;
    backlinkFailed: boolean;
}

interface ErrorBadge {
    error?: any;
    message?: any;
    status?: any;
}

interface SearchableBadge {
    status: boolean;
    error?: any;
    message?: any;
}

interface Pagination {
    start: number;
    results: number;
    total: number;
}

export interface SearchFilters {
    offset: number;
    limit: number;
    search: Search;
    sort: string;
}

interface Search {
    country?: any;
    region: string;
    province?: any;
    city?: any;
    zones?: any;
    code?: any;
    portals: Portals;
    tipologies?: any;
    rooms?: any;
    contract?: any;
    price?: any;
    surface?: any;
}

interface Portals {
    portal: number;
    publishStatus: number;
}

export interface Construction {
    id: number;
    code?: string;
    surface?: number;
    propertyStatus: PropertyStatus;
    prices: Prices;
    contract: PropertyStatus;
    address: string;
    addressNumber?: string;
    isAddressVisible: number;
    addressFlag?: any;
    tipology: Tipology;
    city: City;
    editDate: string;
    status: PropertyStatus;
    ranking: number;
    preferred: number;
    mainThumbId?: number;
    detailUrl: string;
}

interface City {
    id: number;
    name: string;
    province: string;
    latitude?: any;
    longitude?: any;
}

interface Tipology {
    id: number;
    name: string;
    category: PropertyStatus;
}

interface Prices {
    [key: string | number]: Price;
}

interface Price {
    price?: any;
    showPrice?: any;
    isVisible: boolean;
    contractId: number;
}

interface PropertyStatus {
    id: number;
    name: string;
}

export type IsSearchableResponse = ApiResponse<{
    status: boolean;
    message?: string;
}>;

export type PublishedPortalsByConstructionsResponse = ApiResponse<{
    [portalId: number]: {
        [constructionId: number]: Pick<
            ConstructionStatusDetail,
            | 'id'
            | 'published'
            | 'zonesBadge'
            | 'linkBadge'
            | 'errorBadge'
            | 'searchableBadge'
        >;
    };
}>;

export type PublishAllPortalResponse = ApiResponse<
    Pick<MultiSendPortal, 'advertisementSpaces'> & {
        propertiesPublishStatus: number; // is an enum
    }
>;

export type RemainingPortalsResponse = ApiResponse<
    | {
          canPublish: true;
      }
    | {
          canPublish: false;
          publishData?: {
              properties: number;
              availableSpaces: number;
              rentSpaces: number;
              saleSpaces: number | null;
          };
      }
>;

export type ToggleNewConstructionPortalResponse = ApiResponse<
    Pick<MultiSendPortal, 'propertiesPublishStatus' | 'advertisementSpaces'> &
        Pick<ConstructionStatusDetail, 'linkBadge'>
>;
