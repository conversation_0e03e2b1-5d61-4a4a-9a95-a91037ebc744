import { trans } from '@pepita-i18n/babelfish';
// @ts-ignore
import gtxConstants from 'gtx-constants';
import { COLUMNS_MEDIA_QUERY } from 'gtx-react/constants';
import { QueryClient } from '@tanstack/react-query';
import { MultiSendPortal, ConstructionStatusDetail } from './types';

export function getImageUrl(url: string | number) {
    return `${gtxConstants('VHOST_URL_MEDIA_IMAGE')}${gtxConstants('PORTAL_LOGO_ENDPOINT')}/${url}`;
}

export function getPropertyImageUrl(imageId: string | number) {
    return `${gtxConstants('VHOST_URL_MEDIA_IMAGE')}${gtxConstants('AD_IMAGE_ENDPOINT')}/${imageId}/vetrina.jpg`;
}

export const isImmobiliareId = (id: number) => id === gtxConstants('IMMOBILIARE_PORTAL_ID');

const labelByImmobiliarePortalAdvertisementSpaceType = {
    PORTAL_SALE_PROPERTIES: trans('label.sale'),
    PORTAL_RENT_PROPERTIES: trans('label.rent'),
    PORTAL_PROJECTS: trans('label.new_buildings'),
    PORTAL_IMMOBILIARE_PROPERTIES: trans('label.total_sent'),
} as const;

const labelByPortalAdvertisementSpaceType = {
    sent: trans('label.sent'),
    residuals: trans('label.residuals'),
};

const portalAdvertisementSpaceTypes = Object.keys(
    labelByImmobiliarePortalAdvertisementSpaceType
) as (keyof typeof labelByImmobiliarePortalAdvertisementSpaceType)[];

export const mapAdvertisementSpaces = ({
    advertisementSpaces,
}: {
    advertisementSpaces: MultiSendPortal['advertisementSpaces'];
}) => {
    const portalSpaces = advertisementSpaces[gtxConstants('PORTAL_ALL_PROPERTIES')];
    const sent = portalSpaces?.sent || 0;
    const total = portalSpaces?.total || 0;
    // in the response we don't have the value of the residues so we calculate it
    // in case of incorrect data residuals will not be negative
    const residuals = total >= 0 && total >= sent ? total - sent : 0;

    return [
        {
            label: labelByPortalAdvertisementSpaceType.sent,
            value: sent,
        },
        {
            label: labelByPortalAdvertisementSpaceType.residuals,
            value: residuals,
        },
    ];
};

export const mapImmobiliareAdvertisementSpaces = ({
    advertisementSpaces,
    hasProjectVisibility = false,
}: {
    advertisementSpaces: MultiSendPortal['advertisementSpaces'];
    hasProjectVisibility?: boolean;
}) => {
    return portalAdvertisementSpaceTypes
        .filter((key) => !(key === 'PORTAL_PROJECTS' && !hasProjectVisibility))
        .map((key) => {
            const advertisementSpace = advertisementSpaces[gtxConstants(key)];

            return {
                label: labelByImmobiliarePortalAdvertisementSpaceType[key],
                value:
                    key === 'PORTAL_IMMOBILIARE_PROPERTIES'
                        ? advertisementSpace?.sent
                        : `${advertisementSpace?.sent}/${advertisementSpace?.total}`,
            };
        });
};

export const defaultQueryClient = new QueryClient({
    defaultOptions: {
        queries: { refetchOnWindowFocus: false, retry: false },
        mutations: {
            retry: false,
        },
    },
});

type MediaQuery = { [key in keyof typeof COLUMNS_MEDIA_QUERY]: boolean };

/**
 * This function returns the number of columns to be displayed on the screen
 * */
export const getVisibileColumnsByMediaQuery = ({
    MOBILE,
    TABLET,
    XSMALL_DESKTOP,
    SMALL_DESKTOP,
    MEDIUM_DESKTOP,
    LARGE_DESKTOP,
    XLARGE_DESKTOP,
}: MediaQuery) =>
    MOBILE
        ? 6
        : TABLET
        ? 6
        : XSMALL_DESKTOP
        ? 3
        : SMALL_DESKTOP
        ? 4
        : MEDIUM_DESKTOP
        ? 5
        : LARGE_DESKTOP
        ? 6
        : XLARGE_DESKTOP
        ? 7
        : 8;

export const projectsTotalCounter = (portal: MultiSendPortal) =>
    portal && portal.advertisementSpaces
        ? Object.values(portal.advertisementSpaces)
              .map((spaces) => spaces.total)
              .reduce((prev, tot) => {
                  return prev + tot;
              }, 0)
        : 0;

export const hasUnlimitedSending = (portal: MultiSendPortal) =>
    portal && portal.advertisementSpaces
        ? Object.values(portal.advertisementSpaces)
              .map((spaces) => spaces.total)
              .find((spaces) => spaces === gtxConstants('PORTAL_UNLIMITED_PROPERTIES_LABEL'))
        : false;

export const projectsSentCounter = (portal: MultiSendPortal) =>
    portal && portal.advertisementSpaces
        ? Object.entries(portal.advertisementSpaces)
              .filter((allSpaces) => parseInt(allSpaces[0]) == gtxConstants('PORTAL_IMMOBILIARE_PROPERTIES'))
              .map((spaces) => spaces[1].sent)
              .reduce((prev, tot) => {
                  return prev + tot;
              }, 0)
        : 0;

export const propertiesSentCounter = (portal: MultiSendPortal) =>
    portal && portal.advertisementSpaces
        ? Object.entries(portal.advertisementSpaces)
              .filter((allSpaces) => parseInt(allSpaces[0]) !== gtxConstants('PORTAL_IMMOBILIARE_PROPERTIES'))
              .map((spaces) => spaces[1].sent)
              .reduce((prev, tot) => {
                  return prev + tot;
              }, 0)
        : 0;

export const checkExistsPortalsWithError = (portals: MultiSendPortal[], propertyPortals: ConstructionStatusDetail[]) =>
    portals
        .map(({ portal }) => portal.id)
        .some((id) =>
            propertyPortals.some(
                (propertyPortal) =>
                    id === propertyPortal.id && propertyPortal.errorBadge && propertyPortal.errorBadge.error
            )
        );

export let hasPropertyPortalError = (propertyPortal: ConstructionStatusDetail) =>
    propertyPortal && propertyPortal.errorBadge && propertyPortal.errorBadge.error;

export const hasSomeBadge = (propertyPortal: ConstructionStatusDetail) =>
    propertyPortal &&
    (propertyPortal.errorBadge.error ||
        (propertyPortal.zonesBadge && propertyPortal.zonesBadge.needToSelect) ||
        (propertyPortal.searchableBadge && propertyPortal.searchableBadge.status));

export const isPropertyPortalZoneNeeded = (propertyPortal: ConstructionStatusDetail) =>
    propertyPortal && propertyPortal.zonesBadge && propertyPortal.zonesBadge.needToSelect;

export let isPropertyPortalSearchable = (propertyPortal: ConstructionStatusDetail) =>
    propertyPortal && propertyPortal.searchableBadge && propertyPortal.searchableBadge.status;

export const getBadgeData = (
    propertyPortal: ConstructionStatusDetail,
    target: 'error' | 'zone' | 'searchable' = null
) => {
    switch (target) {
        case 'error':
            return hasPropertyPortalError(propertyPortal)
                ? { type: 'error', message: propertyPortal.errorBadge.message }
                : null;
        case 'zone':
            return isPropertyPortalZoneNeeded(propertyPortal)
                ? {
                      type: 'warning',
                      message: propertyPortal.zonesBadge.message,
                  }
                : null;
        case 'searchable':
            return isPropertyPortalSearchable(propertyPortal)
                ? {
                      type: 'warning',
                      message: propertyPortal.searchableBadge.message,
                  }
                : null;
        default:
            return hasPropertyPortalError(propertyPortal)
                ? { type: 'error', message: propertyPortal.errorBadge.message }
                : isPropertyPortalZoneNeeded(propertyPortal)
                ? {
                      type: 'warning',
                      message: propertyPortal.zonesBadge.message,
                  }
                : isPropertyPortalSearchable(propertyPortal)
                ? {
                      type: 'warning',
                      message: propertyPortal.searchableBadge.message,
                  }
                : null;
    }
};

export const getNewConstructionPreviewUrl = (id: number) => `/v2/nuove-costruzioni/dettaglio?id=${id}`;

export const hasAgencyNoProperties = (props: { filters: Record<string, string>; properties: unknown[] }) => {
    return Object.keys(props.filters).length === 0 && props.properties.length === 0;
};
