import { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { IvSchModal } from 'gtx-react/containers/ImmovisitaScheduleModal/IvSchModal';
import { initialFormValues } from 'gtx-react/containers/ImmovisitaScheduleModal/utils/initialFormValues';
import { getQueryString, setQueryString } from 'lib/utility';
import { setModalScheduledItem, toggleScheduleModal } from '../actions';
import { modalIsOpenSelector, modalScheduledVisitSelector } from '../selectors';
import { BASE_PATH } from '../web-api/endpoints';
import { GxNavigationBus } from 'lib/gx-navigation-bus';
import { IV_SCH_GX_NAVIGATION_ADD } from '../constants';

export const IvSchScheduleModal = () => {
    const dispatch = useDispatch();
    const queryParams: { [key: string]: string } = getQueryString();

    // const [realEstateId, setRealEstateId] = useState(realEstateIdIn);
    const scheduledVisitSelected = useSelector(modalScheduledVisitSelector);

    const queryRef = isNaN(parseInt(queryParams['ref']))
        ? null
        : parseInt(queryParams['ref']);

    const initRealEstateObject = scheduledVisitSelected?.realEstateObj
        ? Object.assign({}, scheduledVisitSelected.realEstateObj)
        : null;

    const initialFormState = scheduledVisitSelected || {
        propertyId: queryRef,
    };

    delete initialFormState.realEstateObj;

    const isModalOpen = useSelector(modalIsOpenSelector);

    useEffect(() => {
        if (
            Object.keys(queryParams).indexOf('ref') > -1 &&
            queryParams['ref'] != ''
        ) {
            dispatch(
                setModalScheduledItem({
                    ...initialFormValues(),
                    propertyId: queryRef,
                })
            );
            dispatch(toggleScheduleModal(true));
        }
    }, []);

    useEffect(() => {
        const unsubscribe = GxNavigationBus.addListener(
            IV_SCH_GX_NAVIGATION_ADD,
            () => {
                dispatch(toggleScheduleModal(true));
            }
        );
        return () => unsubscribe();
    }, [dispatch, toggleScheduleModal]);

    return (
        <IvSchModal
            isOpen={isModalOpen}
            onClose={() => {
                dispatch(toggleScheduleModal(false));
                dispatch(setModalScheduledItem(initialFormValues()));
                //eventually cleaning up the ref param
                setQueryString(BASE_PATH, { ref: null });
            }}
            initialRealEstateObj={initRealEstateObject}
            initialFormState={initialFormState}
        />
    );
};
