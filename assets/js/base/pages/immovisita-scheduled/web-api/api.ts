import { http } from '@pepita/http';
import {
    IRoomListItem,
    ISvListFormattedApiResponse,
} from '../types/apiResponse';
import { IScheduledVisitItem } from '../types/list';
import { endpoints, lookupEndpoints } from './endpoints';

export const getPaginationOptionsRequest = () =>
    http.get(lookupEndpoints.PAGINATION_LOOKUP).json();

export interface ISalesRequestPagination {
    results: number;
    page: number;
}

export interface IGetRoomsSearchParams {
    [key: string]: string | number;
}

export const getRoomList = (config?: {
    pagination?: ISalesRequestPagination;
    searchParams?: IGetRoomsSearchParams;
}) =>
    http
        .get(endpoints.ROOMS, {
            searchParams: {
                ...config?.pagination,
                ...config?.searchParams,
                withScheduledTime: 1,
                withoutEndTime: 1,
            },
        })
        .json() as Promise<ISvListFormattedApiResponse>;

export const deleteRoom = (roomId: number) => {
    return http(`${endpoints.ROOMS}/${roomId}`, {
        method: 'DELETE',
    }).json();
};


export const getScheduleReqStatus = (): Promise<{active: boolean}> => {
    return http.get(endpoints.HAS_SCHEDULE_REQUEST).json()
}
