import { trans } from '@pepita-i18n/babelfish';
import React from 'react';
import { Image as GxImage } from '../../../../commons/gtx-react/components';
import { IScheduledVisitItem } from '../types/list';
import { getAddressFromProperty } from '../utils/getAddressFromProperty';
import { getPriceFromProperty } from '../utils/getPriceFromProperty';
import { getSurfaceFromProperty } from '../utils/getSurfaceFromProperty';
import { IvSchActions } from './IvSchActions';
import { Button } from '@gx-design/button';
import { Popover } from '@gx-design/popover';
import { PopoverContent } from './IvSchPopoverContent';
import { format, isValid } from 'date-fns';

interface IIvSchCardProps {
    scheduledVisit: IScheduledVisitItem;
}

const IvSchCardVisitDate = (props: { scheduledVisitDate?: string | null }) => {
    if (!props.scheduledVisitDate) {
        return null;
    }

    const scheduledVisitDate = new Date(props.scheduledVisitDate);

    if (!isValid(scheduledVisitDate)) {
        return null;
    }

    return (
        <>
            {' '}
            {format(scheduledVisitDate, 'dd/MM/yyyy')} <br />
            {format(scheduledVisitDate, 'HH:mm')}{' '}
        </>
    );
};

export const IvSchCard: React.FC<IIvSchCardProps> = ({ scheduledVisit }) => {
    return (
        <div className="gx-card gx-card--col2" role="rowgroup">
            <div className="gx-card__content" role="row">
                <div className="property-item" role="cell">
                    {scheduledVisit.property && (
                        <>
                            <div className="property-item__photo">
                                <GxImage
                                    loading="lazy"
                                    src={scheduledVisit.property?.mainThumbUrl}
                                    fallbackSrc="/bundles/base/getrix/common/img/img-placeholder.png"
                                />
                            </div>
                            <div className="property-item__desc">
                                <div>RIF: {scheduledVisit.property?.id}</div>
                                <div>
                                    {getAddressFromProperty(
                                        scheduledVisit.property
                                    )}
                                </div>
                                <div>
                                    {getPriceFromProperty(
                                        scheduledVisit.property
                                    )}{' '}
                                    |{' '}
                                    {getSurfaceFromProperty(
                                        scheduledVisit.property
                                    )}
                                </div>
                            </div>
                        </>
                    )}
                </div>
                <div className="property-card__info" role="cell">
                    <div className="property-card__info__item">
                        <span className="title">{trans('label.date')}</span>
                        <div
                            className="content"
                            data-testid="scheduled-visit-formatted-date"
                        >
                            <IvSchCardVisitDate
                                scheduledVisitDate={
                                    scheduledVisit.scheduledTime
                                }
                            />
                        </div>
                    </div>
                    <div className="property-card__info__item">
                        <span className="title">{trans('label.agent')}</span>
                        <div className="content">
                            {scheduledVisit.agent?.firstname}{' '}
                            {scheduledVisit.agent?.lastname}
                        </div>
                    </div>
                    <div className="property-card__info__item">
                        <span className="title">{trans('label.guests')}</span>
                        <div className="content">
                            {/** @ts-ignore */}
                            {scheduledVisit.guests?.length > 0 ? (
                                <Popover
                                    title=""
                                    onEdge={false}
                                    large={false}
                                    position="right"
                                    content={
                                        <PopoverContent
                                            scheduledVisit={scheduledVisit}
                                        />
                                    }
                                >
                                    <Button iconOnly={true}>
                                        {scheduledVisit.guests.length}
                                    </Button>
                                </Popover>
                            ) : (
                                <Button iconOnly={true}>
                                    {scheduledVisit.guests.length}
                                </Button>
                            )}
                        </div>
                    </div>
                </div>
            </div>
            <div className="gx-card__footer">
                <IvSchActions isDesktop={false} item={scheduledVisit} />
            </div>
        </div>
    );
};
