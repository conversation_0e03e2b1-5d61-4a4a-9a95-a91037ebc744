import { render } from '#tests/react/testing-library-enhanced';
import { NotifyProvider } from '@gx-design/snackbar';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import htmlData from './dummies/users-list-html.json';
import { SettingsUsersView } from './views/SettingsUsersView';
import { PrivilegeProvider } from './components/PrivilegeProvider';
import { LoggedUserProvider } from 'gtx-react/contexts/LoggedUserProvider';

export const renderSettingsUsersView = (data?: Partial<typeof htmlData>) => {
    return render(<SettingsUsersView />, {
        wrapper(props) {
            const queryClient = createQueryClient();

            return (
                <PrivilegeProvider isAdmin={false} isSuperAdmin>
                    <LoggedUserProvider {...window.gtxLoggedUser}>
                        <NotifyProvider>
                            <QueryClientProvider client={queryClient}>
                                {props.children}
                                <script
                                    id="users-list-data"
                                    type="application/json"
                                >
                                    {JSON.stringify({ ...htmlData, ...data })}
                                </script>
                            </QueryClientProvider>
                        </NotifyProvider>
                    </LoggedUserProvider>
                </PrivilegeProvider>
            );
        },
    });
};

export const stubBianchino = {
    idAgente: 205908,
    cognome: 'Bianchino',
    nome: 'Francesco',
    email: '<EMAIL>',
    chat: true,
    fkImmagineAgente: **********,
    titolare: true,
    emailVerificata: true,
    status: 2,
    ruolo: { idRuoloAgente: 1, ruolo: 'Amministratore' }, // Add missing property
    uuid: '', // Add missing property
    fkAgenzia: -1, // Add missing property
    mostraInPubblicita: false, // Add missing property
    urlImmagineAgenteThumb: '', // Add missing property
    profilo: {
        fkAgente: 205908,
        dataNascita: '1985-07-11',
        sesso: 1,
        codiceFiscale: '12wedfr56ytghju8',
        comune: {
            nome: 'Foggia',
            // eslint-disable-next-line camelcase
            sigla_provincia: 'FG',
            idComune: 10285,
        },
        indirizzo: 'Via valentino banal 891',
        cap: '00174',
        codiceREA: 'kaaaabvd',
        partitaIVA: '**********',
        associazioneDiCategoria: {
            idAssociazioneDiCategoria: 2,
            nome: 'Fiaip',
        },
        contatti: [
            {
                fkAgente: 205908,
                numero: '+************',
                tipo: '2',
                preferito: true,
                idContattoAgente: 719727,
                pubblico: false,
                fNumero: '+39 333 3333 333',
                prefisso: '+39',
                fNumeroSenzaPrefisso: '3333333333',
                shortCode: 'it',
            },
        ],
        biografia: [
            {
                fkAgente: 205908,
                lingua: 'it',
                testo: "Cecco Angiolieri (Siena, 1257 circa – Siena, 1313 circa) è stato uno scrittore e poeta italiano, contemrporaneo di Dante Alighieri e appartenente alla storica casata degli Angiolieri.\nIl celebre sonetto S'i' fosse foco, arderei 'l mondo appartiene a unsecolare tradizione letteraria goliardica improntata all'improperio e alla dissacrazione dell",
                idBiografiaAgente: 1206771,
            },
            // Rest of the biografia objects...
        ],
    },
};
