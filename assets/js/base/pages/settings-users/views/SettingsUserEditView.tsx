import { trans } from '@pepita-i18n/babelfish';
import { parse } from 'date-fns';
import { formatDate } from 'gtx-react/components/DatePicker';
import {
    EMPTY_CONTACT,
    EditProfileForm,
    PROFILE_FORM_KEYS,
    ProfileFormValues,
    RowSingleWrapper,
} from 'gtx-react/components/EditProfileForm';
import { ProfileImage } from 'gtx-react/components/ProfileImage';
import { GxFkSelect } from 'gtx-react/components/gx-formik';
import { ProfileData } from 'gtx-react/utils/extractProfileData';
import {
    selectFormQuery,
    useUserProfileQuery,
    userPictureQueryFn,
} from '../hooks/queries';

const generateInitialValues = ({
    languagesData,
    user,
}: Pick<ProfileData, 'user' | 'languagesData'>): ProfileFormValues => {
    const initialValues = {
        [PROFILE_FORM_KEYS.AGENT_ID]: user.idAgente,
        [PROFILE_FORM_KEYS.STATUS]:
            typeof user?.status === 'number' ? `${user.status}` : '',
        [PROFILE_FORM_KEYS.ROLE]:
            typeof user?.ruolo?.idRuoloAgente === 'number'
                ? `${user.ruolo.idRuoloAgente}`
                : '',
        [PROFILE_FORM_KEYS.ADDRESS]: user?.profilo?.indirizzo || '',
        [PROFILE_FORM_KEYS.BIRTH_DATE]: user?.profilo?.dataNascita
            ? formatDate(parse(user.profilo.dataNascita, 'yyyy-MM-dd', 0))
            : '',
        [PROFILE_FORM_KEYS.EMAIL]: user?.email || '',
        [PROFILE_FORM_KEYS.FIRST_NAME]: user?.nome || '',
        [PROFILE_FORM_KEYS.FISCAL_CODE]: user?.profilo?.codiceFiscale || '',
        [PROFILE_FORM_KEYS.LAST_NAME]: user?.cognome || '',
        [PROFILE_FORM_KEYS.POST_CODE]: user?.profilo?.cap || '',
        [PROFILE_FORM_KEYS.REA_CODE]: user?.profilo?.codiceREA || '',
        [PROFILE_FORM_KEYS.SEX]: user?.profilo?.sesso?.toString() || null,
        [PROFILE_FORM_KEYS.VAT]: user?.profilo?.partitaIVA || '',
        [PROFILE_FORM_KEYS.ASSOCIATION_ID]:
            user?.profilo?.associazioneDiCategoria?.idAssociazioneDiCategoria ||
            '',
        [PROFILE_FORM_KEYS.CITY_OBJ]: {
            id: user?.profilo?.comune?.idComune || '',
            name: user?.profilo?.comune?.nome || '',
        },
        [PROFILE_FORM_KEYS.CONTACTS]: user?.profilo?.contatti?.length
            ? user.profilo.contatti
            : [EMPTY_CONTACT],
    };

    Object.keys(languagesData).forEach((lang) => {
        initialValues[`${PROFILE_FORM_KEYS.BIO_PREFIX}${lang}`] =
            user?.profilo?.biografia?.find((bio) => bio.lingua === lang)?.testo;
    });
    // @ts-ignore:next-line
    return initialValues;
};

export default function SettingsUserEditView() {
    const { data, isError, isLoading, isSuccess } = useUserProfileQuery();

    if (isError) {
        throw new Error('Error while fetching user profile data');
    }

    if (isLoading || !isSuccess) {
        return null;
    }

    const { role: roleOptions } = selectFormQuery(data.usersFormData);

    return (
        <div className="gx-container gx-container--sideBySide">
            <ProfileImage
                userId={data.user.idAgente}
                queryFn={userPictureQueryFn}
            />

            <div className="user-profile-wrapper">
                <EditProfileForm
                    changeRoleSelectElement={
                        data.isChangeRoleDisabled
                            ? undefined
                            : ({ name }) => (
                                  <RowSingleWrapper>
                                      <GxFkSelect
                                          id="form-edit-user-role"
                                          name={name}
                                          label={trans('label.role')}
                                          options={roleOptions}
                                      />
                                  </RowSingleWrapper>
                              )
                    }
                    userId={data.user.idAgente}
                    associations={data.associations}
                    countryCallingCodes={data.countryCallingCodes}
                    isMainAgent={data.user.titolare}
                    storedNewEmail={data.user.nuovaEmail}
                    languages={Object.keys(data.languagesData)}
                    initialValues={generateInitialValues({
                        languagesData: data.languagesData,
                        user: data.user,
                    })}
                    hideDeleteButton
                    voxNumber={data.estensioni?.numeroVox?.numeroDestinazione}
                />
            </div>
        </div>
    );
}
