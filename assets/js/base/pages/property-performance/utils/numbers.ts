import { formatNumberToLocale } from 'lib/formatter';

/** Used inside cart's tooltip */
export const incrementFormatter = (value: number) => {
    let increment = formatNumberToLocale(value).toString();

    return `${increment.indexOf('-') === -1 ? `+${increment}` : increment}%`;
};

export const diffRankingPositionFormatter = (value: number) =>
    value.toString().indexOf('-') === -1 ? `+${value}` : `${value}`;
