import gtxConstants from 'gtx-constants';

type RawExtravisibility = {
    searchable: boolean;
    star: boolean;
    starPremium: boolean;
    starExpiration: unknown;
    showcase: boolean;
    showcasePremium: boolean;
    showcaseExpiration: unknown;
    top: boolean;
    topPremium: boolean;
    topExpiration: unknown;
};

type Extravisibilities = {
    searchable: boolean;
    star: boolean;
    showcase: boolean;
    top: boolean;
    sky: boolean;
};

export const normalizeExtravisibility = (extravisibility: RawExtravisibility): Extravisibilities => {
    const { showcaseExpiration, topExpiration, starExpiration, ...usefulExtravisibility } = extravisibility;

    const baseExtravisibilities: Extravisibilities = {
        searchable: false,
        showcase: false,
        star: false,
        top: false,
        sky: false,
    };

    let extravisibilities: Extravisibilities = { ...baseExtravisibilities };

    Object.keys(usefulExtravisibility).forEach((key) => {
        const typedKey = key as keyof typeof usefulExtravisibility;
        const normalizedKey = typedKey.replace('Premium', '') as keyof Extravisibilities;

        extravisibilities[normalizedKey] = extravisibilities[normalizedKey] || usefulExtravisibility[key];
    });

    if (extravisibilities.top && extravisibilities.showcase && extravisibilities.star) {
        extravisibilities = {
            ...baseExtravisibilities,
            sky: true,
        };
    }

    return extravisibilities;
};

export const getTheMostUsedExtravisibility = (extravisibilities: RawExtravisibility[]): keyof Extravisibilities => {
    const arr = extravisibilities.map(normalizeExtravisibility);
    // Count the occurrences of each extravisibility
    const counts = arr.reduce(
        (acc, curr) => {
            acc.searchable += curr.searchable ? 1 : 0;
            acc.showcase += curr.showcase ? 1 : 0;
            acc.star += curr.star ? 1 : 0;
            acc.top += curr.top ? 1 : 0;
            acc.sky += curr.sky ? 1 : 0;
            return acc;
        },
        {
            searchable: 0,
            showcase: 0,
            star: 0,
            top: 0,
            sky: 0,
        } as Record<keyof Extravisibilities, number>
    );

    // Find the most used extravisibility
    const mostUsedExtravisibility = Object.keys(counts).reduce((acc, curr) => {
        if (counts[curr as keyof Extravisibilities] > counts[acc as keyof Extravisibilities]) {
            return curr;
        }
        return acc;
    });

    return mostUsedExtravisibility as keyof Extravisibilities;
};

export const getExtravisibilityLabelKey = (extravisibility: keyof Extravisibilities): string => {
    switch (extravisibility) {
        case 'searchable':
            return gtxConstants('PREMIUM_VISIBILITY_NAME');
        case 'showcase':
            return gtxConstants('SHOWCASE_VISIBILITY_NAME');
        case 'star':
            return gtxConstants('STAR_VISIBILITY_NAME');
        case 'top':
            return gtxConstants('TOP_VISIBILITY_NAME');
        case 'sky':
            return gtxConstants('SKY_VISIBILITY_NAME');
        default:
            return '';
    }
};
