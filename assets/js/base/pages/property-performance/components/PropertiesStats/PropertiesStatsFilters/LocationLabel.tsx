import { trans } from '@pepita-i18n/babelfish';
import { useQueries } from '@tanstack/react-query';
import { isApiSuccess } from 'lib/api';
import { ApiResponse } from 'types/api-response';
import { LookupItem } from 'types/lookup';
import {
    cityByProvinceOptions,
    countyOptions,
    provinceByRegionOptions,
    regionOptions,
    zoneByCityOptions,
} from '../../../utils/queries';

type LocationLabelProps = Partial<{
    country: string;
    region: string;
    province: string;
    city: string;
    zone: string;
}>;

const findLabelByValue =
    (value?: string) => (response: ApiResponse<LookupItem[]>) => {
        if (!value || !isApiSuccess(response)) {
            return null;
        }

        return response.data.find((item) => item.value === value)?.label;
    };

function useLocationLabel(props: LocationLabelProps) {
    const [countyQuery, regionQuery, provinceQuery, cityQuery, zoneQuery] =
        useQueries({
            queries: [
                {
                    ...countyOptions(),
                    enabled: Boolean(props.country),
                    select: findLabelByValue(props.country),
                },
                {
                    ...regionOptions(props.country),
                    enabled: Boolean(props.region),
                    select: findLabelByValue(props.region),
                },
                {
                    ...provinceByRegionOptions(props.region),
                    enabled: Boolean(props.province),
                    select: findLabelByValue(props.province),
                },
                {
                    ...cityByProvinceOptions(props.province),
                    enabled: Boolean(props.city),
                    select: findLabelByValue(props.city),
                },
                {
                    ...zoneByCityOptions(props.city),
                    enabled: Boolean(props.zone),
                    select: findLabelByValue(props.zone),
                },
            ],
        });

    // cascading order: zone > city > province > region > country
    return (
        zoneQuery.data ||
        cityQuery.data ||
        provinceQuery.data ||
        regionQuery.data ||
        countyQuery.data ||
        trans('label.place')
    );
}

export function LocationLabel(props: LocationLabelProps) {
    const label = useLocationLabel(props);

    return <div>{label}</div>;
}
