import { Badge } from '@gx-design/badge';
import { trans, transChoice } from '@pepita-i18n/babelfish';
import { TooltipProps } from 'recharts';
import {
    NameType,
    ValueType,
} from 'recharts/types/component/DefaultTooltipContent';

import { formatNumberToLocale } from 'lib/formatter';
import { SIX_MONTHS_DAYS } from '../constants';
import { dateFormatter } from '../utils/dates';
import { incrementFormatter } from '../utils/numbers';

type KpiSingleEventChartTooltipProps<
    MyValue extends ValueType,
    MyName extends NameType,
> = TooltipProps<MyValue, MyName> & {
    rangeDaysDiff: number;
    chartEvent: any;
};
/**
 * Custom tooltip, used by `<ContactsByDayChart />` and `<EventsChart />`
 * (the second one right now is dismissed); responsible to show formatted date as
 * header, value and the percentage increment from previous day.
 */
export const KpiSingleEventChartTooltip: React.FC<
    KpiSingleEventChartTooltipProps<ValueType, NameType>
> = ({ payload, rangeDaysDiff, chartEvent }) => {
    if (!payload || !payload.length) {
        return null;
    }

    return (
        <div className="eventsGraph__popover">
            <div className="eventsGraph__popoverData">
                <span>
                    {dateFormatter(payload[0].payload?.date, rangeDaysDiff)}
                </span>
            </div>
            <div className="eventsGraph__popoverContent">
                <div>
                    <Badge
                        text={
                            formatNumberToLocale(
                                payload[0].value
                            )?.toString() || ''
                        }
                    />
                    <span>
                        {chartEvent?.pluralLabelKey && payload[0].value !== 1
                            ? trans(chartEvent?.pluralLabelKey).toLowerCase()
                            : transChoice(
                                  chartEvent.labelKey,
                                  parseInt(payload[0].value.toString())
                              ).toLowerCase()}
                    </span>
                </div>
                {payload[0].payload?.increment &&
                payload[0].payload?.increment !== 0 ? (
                    <div>
                        <Badge
                            text={
                                incrementFormatter(
                                    payload[0].payload.increment
                                ) || ''
                            }
                        />
                        <span>
                            {rangeDaysDiff >= SIX_MONTHS_DAYS
                                ? trans(
                                      'label.compared_to_previous_month'
                                  ).toLowerCase()
                                : trans(
                                      'label.compared_to_previous_day'
                                  ).toLowerCase()}
                        </span>
                    </div>
                ) : null}
            </div>
        </div>
    );
};
