import gtxConstants from '@getrix/common/js/gtx-constants';
import { trans } from '@pepita-i18n/babelfish';
import { format, subDays } from 'date-fns';
import { useContext, useEffect } from 'react';

import {
    SHOW_ABSOLUTE_DATA,
    SIMILAR_ADS_DEFAULT_FILTERS,
    SIMILAR_ADS_DEFAULT_PAGINATION,
    STATS_KEYS,
} from '../constants';
import { useProperty } from '../hooks/useProperty';
import { usePropertyPerformanceContext } from '../hooks/usePropertyPerformanceContext';
import { PresetRangeInterval } from '../types/filters';
import { KpiContactsChart } from './KpiContactsChart';
import { KpiEventsCharts } from './KpiEventsCharts';
import { KpiRankingChart } from './KpiRankingChart';
import { Performances } from './Performances';
import { PrintContext } from './PrintProvider';
import { PropertyPerformanceProvider } from './PropertyPerformanceProvider';
import { SimilarPropertiesPrintable } from './SimilarPropertiesPrintable';
import { useSimilarProperties } from '../hooks/queries';
import { usePropertyStatsQuery } from '../hooks/usePropertyStatsQuery';

type PropertyPerformanceProps = {
    propertyId: string;
};

export const PropertyPerformancePrint: React.FC<PropertyPerformanceProps> = ({
    propertyId,
}) => {
    const { setAreChartsDataLoaded } = useContext(PrintContext);
    const { mainPerformanceData } = usePropertyPerformanceContext();

    const presetDaysRange = PresetRangeInterval.Week;
    const dateFrom = subDays(new Date(), presetDaysRange);
    const dateTo = subDays(new Date(), 1);

    const { isLoading, data } = usePropertyStatsQuery(
        {
            dateFrom: format(dateFrom, 'yyyy-MM-dd'),
            dateTo: format(dateTo, 'yyyy-MM-dd'),
            propertyId,
            statsType: STATS_KEYS,
            details: 1,
        },
        { enabled: dateFrom !== null && dateTo !== null }
    );

    const { data: similarAds, isLoading: similarAdsLoading } =
        useSimilarProperties(
            propertyId,
            SIMILAR_ADS_DEFAULT_PAGINATION,
            SIMILAR_ADS_DEFAULT_FILTERS
        );

    const {
        data: propertyData,
        isLoading: isLoadingPropertyData,
        description,
    } = useProperty(propertyId);

    useEffect(() => {
        if (
            !isLoading &&
            !isLoadingPropertyData &&
            !similarAdsLoading &&
            typeof setAreChartsDataLoaded === 'function'
        ) {
            setAreChartsDataLoaded(true);
        }
    }, [
        isLoading,
        isLoadingPropertyData,
        similarAdsLoading,
        setAreChartsDataLoaded,
    ]);

    return propertyData ? (
        <PropertyPerformanceProvider
            data={{
                presetDaysRange,
                dateRange: [dateFrom, dateTo],
                customRange: null,
                isLoading,
                data,
                mainPerformanceData,
                propertyData,
                similarAds,
            }}
        >
            <style>
                {`
                @page {
                    size: A4;
                    margin-top: 1cm;
                    margin-bottom: 1cm;
                    counter-increment: page
                }
                `}
            </style>
            <div
                className={`performance-detail-page app-domain-${gtxConstants(
                    'BODY_CSS_CLASS_SUFFIX'
                ).toLowerCase()}`}
            >
                <div className="performance-detail-page__head"></div>

                <div className="performance-detail-page__content">
                    <div className="performance-detail-page__propertyId">
                        <div className="performance-detail-page__propertyIdImg">
                            <img src={propertyData?.mainImageThumbUrl} />
                        </div>
                        <div>
                            <div className="gx-title-2">
                                {trans('label.property_performance')}
                            </div>
                            <span>{description}</span>
                        </div>
                    </div>
                    <Performances propertyId={propertyId} printable />
                    {SHOW_ABSOLUTE_DATA ? (
                        <KpiEventsCharts printable />
                    ) : (
                        <KpiRankingChart printable />
                    )}
                    <KpiContactsChart printable />
                    <SimilarPropertiesPrintable />
                </div>
            </div>
        </PropertyPerformanceProvider>
    ) : null;
};
