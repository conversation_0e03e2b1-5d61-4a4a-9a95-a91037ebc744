import { ErrorBoundary } from 'react-error-boundary';
import React, { Suspense } from 'react';
import { SidebarContent } from './SidebarContent';
import { Error } from 'gtx-react/components/Error/Error';
import { PropertyListSkeleton } from './PropertyListSkeleton';

interface SidebarContentProps {
    propertyId: string;
}

export const SidebarContentWithSuspense = (props: SidebarContentProps) => {
    return (
        <ErrorBoundary FallbackComponent={() => <Error />}>
            <Suspense fallback={<PropertyListSkeleton showFullPage />}>
                <SidebarContent {...props} />
            </Suspense>
        </ErrorBoundary>
    );
};
