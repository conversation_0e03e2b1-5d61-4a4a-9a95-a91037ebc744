//https://github.com/redux-saga/redux-saga/issues/280#issuecomment-291133023
import 'regenerator-runtime/runtime';
import { http } from '@pepita/http';
import {
    CUSTOMER_SERVICE_REQUEST_POST,
    RECHAR<PERSON>_WALLET_POST,
} from './endpoints';
import {
    CustomerServiceRequestDataType,
    RechargeWalletRequestDataType,
    RechargeWalletResponseDataType,
} from '../types';

export const sendRechargeWalletRequest = (
    data: RechargeWalletRequestDataType
): Promise<RechargeWalletResponseDataType> =>
    http.post(RECHARGE_WALLET_POST, { form: { ...data } }).json();

export const sendCustomerServiceRequest = async (
    data: CustomerServiceRequestDataType
): Promise<void> => {
    try {
        const promised = http.post(CUSTOMER_SERVICE_REQUEST_POST, {
            form: { ...data },
        });
        // result is empty, need to check `ok` flag
        const raw = await promised.raw();
        if (raw?.ok) {
            return;
        } else {
            throw new Error('CustomerServiceRequest: result is not ok');
        }
    } catch (error) {
        console.error('ERROR: ', error);
        throw new Error(error);
    }
};
