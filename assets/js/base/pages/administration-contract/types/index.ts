import type { IEcommerceProduct } from 'types/api/property';
import type { Visibility } from 'types/propertyVisibilities';

export type RechargeWalletRequestDataType = {
    amount: Number;
    message?: string;
};

export type RechargeWalletResponseDataType = {
    message?: string;
    code?: Number;
};

export type CustomerServiceRequestDataType = {
    servizio: string;
    sezione: string;
    tipo: string;
    oggetto: string;
    nome: string;
    telefono: string;
    email: string;
    messaggio?: string;
    extraInfo?: string;
};

export interface IEcommerceVisibilityProduct extends IEcommerceProduct {
    service: Visibility['key'];
}
