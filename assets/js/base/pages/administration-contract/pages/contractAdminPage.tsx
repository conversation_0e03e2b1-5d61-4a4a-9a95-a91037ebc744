import { CONTRACT_ADMIN_PATH } from '../web-api/endpoints';
import { GtxPageWithStore } from 'gtx-react/components/GtxApp/GtxApp';
import { AdministrationContractView } from '../views/AdministrationContractView';
import { contractConfigStore } from '../redux/store';
import { setContractConfig } from '../redux/contract-admin/slices/contractConfig';
import { HeaderActions } from '../components/HeaderActions';
import { extractData, extractFeaturesConfig } from '../utils/utils';
import { setFeaturesConfig } from '../redux/contract-admin/slices/featuresConfig';

export const contractAdminPage: GtxPageWithStore = {
    store: contractConfigStore,
    container: AdministrationContractView,
    path: CONTRACT_ADMIN_PATH,
    initFunc: async (dispatch) => {
        const [data, featuresConfig] = await Promise.all([
            extractData(),
            extractFeaturesConfig(),
        ]);
        dispatch(setContractConfig(data));
        dispatch(setFeaturesConfig(featuresConfig));
    },
    header: {
        actions: HeaderActions,
    },
};
