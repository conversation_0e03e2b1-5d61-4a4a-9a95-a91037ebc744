import { Error } from 'gtx-react/components/Error/Error';
import { ErrorBoundary } from 'react-error-boundary';
import { Suspense } from 'react';
import { Loader } from '@gx-design/loader';
import { DetailHeader } from '../components/detail/DetailHeader';
import { DetailSection } from '../components/detail/DetailSection';

export const AcquisitionSalesDetailView = () => {
    return (
        <ErrorBoundary FallbackComponent={() => <Error />}>
            <Suspense fallback={<Loader variant="fixed" />}>
                <AcquisitionSalesDetail />
            </Suspense>
        </ErrorBoundary>
    );
};

const AcquisitionSalesDetail = () => {
    return (
        <>
            <DetailHeader />
            <DetailSection />
        </>
    );
};
