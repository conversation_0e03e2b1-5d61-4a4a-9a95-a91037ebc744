import { configureStore } from '@reduxjs/toolkit';
import basicSlices from 'gtx-react/rtk/slices';
import remoteVisitConfig from './slices/remoteVisitConfig';

export const remoteVisitConfigStore = configureStore({
    reducer: {
        ...basicSlices,
        remoteVisitConfig,
    },
});

// Infer the `RootState` and `AppDispatch` types from the store itself
export type RemoteVisitConfigState = ReturnType<
    typeof remoteVisitConfigStore.getState
>;
// Inferred type: {posts: PostsState, comments: CommentsState, users: UsersState}
export type RemoteVisitConfigDispatch = typeof remoteVisitConfigStore.dispatch;
