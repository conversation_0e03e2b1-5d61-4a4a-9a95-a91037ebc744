import gtxConstants from '@getrix/common/js/gtx-constants';
import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { Radio, RadioGroup } from '@gx-design/radio';
import { trans } from '@pepita-i18n/babelfish';
import {
    useQueryClient,
    useSuspenseQuery,
    useIsFetching,
} from '@tanstack/react-query';
import { fapiQuery } from 'lib/fapi/client';
import { PropsWithChildren, Suspense, useState } from 'react';
import { ErrorBoundary } from 'react-error-boundary';
import { Loader } from '@gx-design/loader';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { Error } from 'gtx-react/components/Error/Error';
import { useNotifyContext } from '@gx-design/snackbar';

const allFlagsQueryOptions = fapiQuery.queryOptions(
    'get' as const,
    '/agency/flags' as const
);

function RemoteVisitErrorBoundary(props: PropsWithChildren) {
    return (
        <ErrorBoundary onError={console.error} fallback={<Error />}>
            {props.children}
        </ErrorBoundary>
    );
}

type SwitchButtonProps = {
    onChange: (visitRequestFlag: boolean) => void;
    enabled?: boolean;
};

function RemoteVisit(props: Omit<SwitchButtonProps, 'enabled'>) {
    const queryFlags = useSuspenseQuery(allFlagsQueryOptions);

    return (
        <SwitchButton
            enabled={queryFlags.data.remoteVisits}
            onChange={props.onChange}
        />
    );
}

function SwitchButton(props: SwitchButtonProps) {
    return (
        <div className="gx-col-md-4 gx-col-sm-4 gx-col-xs-12 is-vertical-centered">
            <RadioGroup
                variant="button"
                isLabelVisible={false}
                label=""
                isSmall
            >
                <Radio
                    id="remote-visit-yes"
                    label={trans('label.yes')}
                    checked={props.enabled === true}
                    onChange={() => props.onChange(true)}
                ></Radio>
                <Radio
                    id="remote-visit-no"
                    label={trans('label.no')}
                    checked={props.enabled === false}
                    onChange={() => props.onChange(false)}
                ></Radio>
            </RadioGroup>
        </div>
    );
}

export const RemoteVisitSettingsView = () => {
    const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
    const queryClient = useQueryClient();
    const flagsMutation = fapiQuery.useMutation(
        'put' as const,
        '/agency/flags' as const
    );
    const fetchingFlags = useIsFetching({
        queryKey: allFlagsQueryOptions.queryKey,
    });

    const { trackEvent } = useMixpanelContext();
    const { showNotification } = useNotifyContext();

    const onRadioClick = (remoteVisits: boolean) => {
        flagsMutation.mutate(
            { body: { remoteVisits } },
            {
                onSuccess: (data) => {
                    trackEvent({
                        event: 'settings_remote_visit_request_activation',
                        extra: {
                            ['activation_state']: data.remoteVisits,
                        },
                    });

                    showNotification({
                        type: 'success',
                        message: trans('generic.editSuccess'),
                    });

                    queryClient.setQueryData(
                        allFlagsQueryOptions.queryKey,
                        data
                    );
                },
                onError: () => {
                    showNotification({
                        type: 'error',
                        message: trans('generic.editError'),
                    });
                },
            }
        );
    };

    const toggleModal = (value: boolean) => () => setIsModalOpen(value);

    if (flagsMutation.isPending || fetchingFlags > 0) {
        return (
            <div data-testid="ui-pending">
                <Loader />
            </div>
        );
    }
    return (
        <RemoteVisitErrorBoundary>
            <div className="gx-container">
                <div className="gx-section gx-section--paddingBottom gx-row">
                    <div className="gx-col-md-4 gx-col-sm-6 gx-col-xs-12">
                        <h5>
                            <strong>
                                {trans(
                                    'label.scheduled_visit_settings.subtitle'
                                )}
                            </strong>
                        </h5>
                        <p>
                            {trans(
                                'label.scheduled_visit_settings.description',
                                {
                                    AD_PORTAL: gtxConstants('AD_PORTAL'),
                                }
                            )}
                            &nbsp;
                            <a onClick={toggleModal(true)}>
                                {trans('label.learn_more')}
                            </a>
                        </p>
                    </div>
                    <Suspense fallback={<Loader />}>
                        <RemoteVisit onChange={onRadioClick} />
                    </Suspense>
                </div>
                <div className="gx-section" />
                <Modal
                    isOpen={isModalOpen}
                    size="large"
                    title={trans('label.immovisita_settings.modal_title')}
                    footer={
                        <Button variant="accent" onClick={toggleModal(false)}>
                            {trans('label.ok')}
                        </Button>
                    }
                    onClose={toggleModal(false)}
                >
                    {
                        <div className="settings-remote-request-info-modal__body gtx-nw-modal-body">
                            <img src="/bundles/base/img/immovisita-settings/<EMAIL>"></img>
                            <span
                                dangerouslySetInnerHTML={{
                                    __html: trans(
                                        'label.immovisita_settings.modal_body'
                                    ),
                                }}
                            />
                            <span>
                                ,&nbsp;
                                <a
                                    href="/pdf/Richiesta_di_Visita-Guida_per_le_Agenzie.pdf"
                                    target="_blank"
                                >
                                    {trans('label.click_here')}
                                </a>
                            </span>
                        </div>
                    }
                </Modal>
            </div>
        </RemoteVisitErrorBoundary>
    );
};
