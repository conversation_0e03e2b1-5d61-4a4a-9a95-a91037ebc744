import { IPagination } from 'types/pagination';
import {
    ICountersApiResponse,
    IEcommerceProduct,
    IPropertiesSpaces,
    IPropertyImproveQualityApiResponse,
    IPropertyListItem,
} from 'types/api/property';
import { ILookupItem } from 'types/api/lookup';

export interface IPaginationOptionsResponse {
    paginationOptions: ILookupItem[];
}

export interface IAgencyResponse {
    isVirtualTour360ModuleOn: boolean;
    externalPropertyUpdates: boolean;
    isPerformancesModuleOn: boolean;
};

interface PerformanceKpiDetail {
    position: number;
    rangeId: number;
};

export interface IPropertiesPerformanceDetails {
    adId: string;
    performance: {
        total: number;
        generals?: PerformanceKpiDetail;
        contacts?: PerformanceKpiDetail;
        detailViews?: PerformanceKpiDetail;
        impressions?: PerformanceKpiDetail;
    };
}

export interface IPerformanceResponseData {
    label: string;
    value: string;
    extra?: string;
}

export interface IPortalPropertiesListApiResponse {
    data: {
        properties: IPropertyListItem[];
        pagination: IPagination;
        paginationOptions: ILookupItem[];
        agency: IAgencyResponse;
        counters: ICountersApiResponse['data'];
        ecommerceProducts: IEcommerceProduct;
        propertiesSpaces: IPropertiesSpaces;
        totalCount: number;
        performances: Record<string, IPerformanceResponseData>;
    };
}

export interface IPortalPropertiesImproveQualityApiResponse
    extends IPropertyImproveQualityApiResponse { }
