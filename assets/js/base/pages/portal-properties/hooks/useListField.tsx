import clsx from 'clsx';
import { trans } from '@pepita-i18n/babelfish';
import { List, ListItem } from '@gx-design/list';
import { Popover } from '@gx-design/popover';
import { Icon } from '@gx-design/icon';
import { IPortalPropertiesImproveQualityApiResponse } from '../types/apiResponse';
import CockadeFirstPage from '../components/CockadeFirstPage';
import { TYPOLOGIES } from 'constants/property';
import { isTuristicProperty } from 'lib/propertyCategories';
import gtxConstants from '@getrix/common/js/gtx-constants';
import { useSelector } from 'react-redux';
import { IReduxRootState } from '../types/redux';
import { PropertyActiveVisibilities } from 'gtx-react/containers/PropertyActiveVisibilities';
import { useModalContext } from './useModalContext';
import { useConfirmDialogContext } from './useConfirmDialogContext';
import { canSeePerformance } from 'lib/propertyPerformances';
import usePropertyActions from './usePropertyActions';
import {
    IPropertyListItem,
    IPortalPropertiesListItem,
} from 'types/api/property';
import { ListCounter } from 'gtx-react/components/ListCounter/ListCounter';
import { Counter } from 'types/counters';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { ucFirst } from 'lib/strings-formatter';
import { PerformanceRankingIndicator } from 'gtx-react/components/PerformanceRankingIndicator';
import { shouldShowCockade, shouldShowFirstPageTag } from 'lib/property';

const isValidKeyForProperty = (key: string) => {
    // Skip energy class check as is not counted anymore, should be removed when backend stops sending it
    if (key === 'energyClass') {
        return false;
    }

    return true;
};

const areAllOutcomesChecked = (
    property: IPortalPropertiesListItem,
    apiData: IPortalPropertiesImproveQualityApiResponse
) => {
    let result = true;
    const qualityKeys = Object.keys(apiData);

    qualityKeys
        .filter((key) => isValidKeyForProperty(key))
        .forEach((key) => {
            if (!apiData[key]['outcome']) {
                result = false;
            }
        });

    return result;
};

const useListField = () => {
    const { showModal } = useModalContext();
    const { showConfirmDialog } = useConfirmDialogContext();
    const {
        deletePropertyAction,
        editPropertyAction,
        printPropertyDetailAction,
    } = usePropertyActions();
    const performances = useSelector(
        (data: IReduxRootState) => data.performances
    );

    const counters = useSelector(
        (data: IReduxRootState) => data.matchesAndThreadsCounters
    );

    const searchFilters = useSelector(
        (data: IReduxRootState) => data.searchFilters
    );
    const { trackEvent } = useMixpanelContext();

    const renderMapping = {
        place: (data: IPortalPropertiesListItem) => {
            if (!data?.properties || !data?.properties[0].city) {
                return null;
            }

            return (
                <List>
                    <Popover
                        title={''}
                        onEdge={false}
                        large={false}
                        content={
                            <>
                                <div>{`${data.properties[0].city} (${data.properties[0].province})`}</div>
                                <div>{data.properties[0].zone}</div>
                                <div>{data.properties[0].address}</div>
                            </>
                        }
                    >
                        <div>
                            <ListItem
                                content={`${data.properties[0].city} (${data.properties[0].province})`}
                            />
                            <ListItem
                                content={data.properties[0]?.zone ?? ''}
                            />
                            <ListItem content={data.properties[0].address} />
                        </div>
                    </Popover>
                </List>
            );
        },
        price: (data: IPortalPropertiesListItem) => {
            if (
                !data?.price &&
                !(!data?.properties || !data.properties[0].surface)
            ) {
                return null;
            }

            return (
                <List>
                    <>
                        <ListItem content={data.price} />
                        {data.priceByRequest && (
                            <ListItem content={trans('label.on_request')} />
                        )}
                        <ListItem content={data.properties[0].surface} />
                    </>
                </List>
            );
        },
        reference: (data: IPortalPropertiesListItem) => {
            if (!data) {
                return null;
            }

            const property = data.properties[0] as IPropertyListItem;

            const reference = property?.reference
                ? property.reference
                : data.id;

            const typology = property.subTypology
                ? property.subTypology
                : property.typology;

            return (
                <div
                    className={clsx([
                        'gx-property-item',
                        {
                            'gx-property-item--clickable':
                                !isTuristicProperty(property),
                        },
                    ])}
                    onClick={() => {
                        if (isTuristicProperty(property)) {
                            return null;
                        }
                        trackEvent({
                            event: 'properties_open_details_listing',
                            extra: {
                                ['property_id']: data.id,
                                ['list_tab']: ucFirst(
                                    `${searchFilters.status}`
                                ),
                            },
                        });
                        showModal({
                            id: data.id,
                            isOpen: true,
                            title: trans('label.ad_details'),
                            type: 'realEstate-detail',
                            footerActions: {
                                delete: (onDelete) =>
                                    showConfirmDialog({
                                        isOpen: true,
                                        title: trans('ad.delete'),
                                        type: 'deleteProperty',
                                        hasSubmitControl: true,
                                        submitControlLabel:
                                            trans('ad.delete_confirm'),
                                        submitLabel: trans('label.remove'),
                                        onSubmit: () => (
                                            deletePropertyAction(data.id),
                                            onDelete()
                                        ),
                                    }),
                                edit: () =>
                                    editPropertyAction(data.id, 5, true),
                                print: () => printPropertyDetailAction(data.id),
                            },
                        });
                    }}
                >
                    <div className={'gx-property-item__pic'}>
                        <img src={data.mainImageThumbUrl} loading="lazy" />
                    </div>
                    <div className={'gx-property-item__desc'}>
                        <List>
                            <ListItem content={reference} />
                            <ListItem
                                content={typology ? typology : data.typology}
                            />
                            <ListItem content={data.contract} />
                        </List>
                    </div>
                </div>
            );
        },
        date: (data: IPortalPropertiesListItem) => {
            if (!data || !data.modified) {
                return;
            }

            return data.modified;
        },
        visibility: (data: IPortalPropertiesListItem) => {
            if (
                isTuristicProperty(data) ||
                ![
                    gtxConstants('PROPERTY_ACTIVE_PORTAL_STATUS'),
                    gtxConstants('PROPERTY_SECRET_PORTAL_STATUS'),
                ].includes(data.statusId)
            ) {
                return null;
            }

            return (
                <div className="gx-table-new__contentFixedHeight">
                    <PropertyActiveVisibilities property={data} />
                </div>
            );
        },
        quality: (
            data: IPortalPropertiesListItem,
            openRankingDetailModal: () => void,
            qualityDetails: IPortalPropertiesImproveQualityApiResponse,
            isFromBackoffice: boolean
        ) => {
            if (!data.ranking) {
                return (
                    <span className="gx-table-new__contentFixedHeight">
                        {'0%'}
                    </span>
                );
            }

            const qualityOutcomesStatus = areAllOutcomesChecked(
                data,
                qualityDetails
            )
                ? 'positive'
                : 'warning';

            return (
                <span
                    onClick={
                        isFromBackoffice ? openRankingDetailModal : undefined
                    }
                    className="gx-table-new__quality gx-table-new__contentFixedHeight"
                >
                    <span>{data.ranking}%</span>
                    <Icon
                        className={`gx-icon gx-text-${qualityOutcomesStatus}`}
                        name={
                            qualityOutcomesStatus === 'positive'
                                ? 'check-circle--active'
                                : 'exclamation-mark-circle--active'
                        }
                    />
                </span>
            );
        },
        match: (data: IPortalPropertiesListItem) => {
            if (counters && Array.isArray(counters)) {
                const found = counters.find(
                    (counter: Counter) => counter.propertyId === data.id
                );
                return (
                    <ListCounter
                        data={found}
                        type="matches"
                        href={`/clienti/match?search=${found?.propertyId}`}
                    />
                );
            }
            return <ListCounter disabled />;
        },
        threads: (data: IPortalPropertiesListItem) => {
            if (counters && Array.isArray(counters)) {
                const found = counters.find(
                    (counter: Counter) => counter.propertyId === data.id
                );
                return (
                    <ListCounter
                        data={found}
                        type="threads"
                        href={`/messaggi/lista?code=${found?.propertyId}`}
                    />
                );
            }
            return <ListCounter disabled />;
        },
        position: (data: IPortalPropertiesListItem) => {
            if (
                !data.searchPosition ||
                data.statusId === gtxConstants('PROPERTY_SECRET_PORTAL_STATUS')
            ) {
                return '---';
            }

            const canShowFirstPageTag = shouldShowFirstPageTag(data);
            const canShowCockade = shouldShowCockade(data);

            return (
                <div>
                    {canShowFirstPageTag ? (
                        <div className="cockade-first-page-wrapper">
                            <div className="gx-table-new__index gx-table-new__index--firstPage">
                                {canShowCockade ? <CockadeFirstPage /> : null}
                                {data.searchPosition}
                            </div>
                            <Popover
                                title={''}
                                onEdge={false}
                                large={false}
                                content={
                                    <span>
                                        {trans('label.first_page_popover', {
                                            AD_PORTAL:
                                                gtxConstants('AD_PORTAL'),
                                        })}
                                    </span>
                                }
                            >
                                <div className="first-page-badge gx-mt-sm">
                                    {trans('label.first_page')}
                                </div>
                            </Popover>
                        </div>
                    ) : (
                        <div className="gx-table-new__index gx-table-new__contentFixedHeight">
                            {data.searchPosition}
                        </div>
                    )}
                </div>
            );
        },
        statistics: (data: IPortalPropertiesListItem) => {
            if (!data?.usersStats) {
                return;
            }
            return (
                <div>
                    <List>
                        <ListItem
                            content={`${data.usersStats.views} ${trans(
                                'label.visit_plural'
                            )} ${trans('label.detail')}`}
                        />
                        <ListItem
                            content={`${data.usersStats.saves} ${trans(
                                'label.saved_plural'
                            )}`}
                        />
                        <ListItem
                            content={`${data.usersStats.hidden} ${trans(
                                'label.hidden_plural'
                            )}`}
                        />
                    </List>
                </div>
            );
        },
        performance: (data: IPortalPropertiesListItem) => {
            if (!performances) {
                return trans('label.property_performance.unavailable');
            }

            return (
                <PerformanceRankingIndicator
                    data={data}
                    {...canSeePerformance(data)}
                />
            );
        },
    };

    return renderMapping;
};

export default useListField;
