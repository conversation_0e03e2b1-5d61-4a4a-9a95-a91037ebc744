import { LIST_VALID_FILTERS_ACTIVE_KEYS } from '../constants';
import { IPortalPropertiesListFilters } from 'types/api/property';

export const isSearchActive = (currentFilters: IPortalPropertiesListFilters) => {
    let result = false;

    LIST_VALID_FILTERS_ACTIVE_KEYS.forEach((key) => {
        if (currentFilters[key] && currentFilters[key] !== '') {
            result = true;
        }
    });

    return result;
};
