import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import { Icon } from '@gx-design/icon';

export function PropertyConvertToRentConfirmModal({
    isOpen,
    isLoading,
    onClose,
    onConfirm,
}: {
    isLoading?: boolean;
    isOpen: boolean;
    onClose: () => void;
    onConfirm: () => void;
}) {
    return (
        <Modal
            footer={
                <>
                    <Button variant="ghost" onClick={onClose}>
                        {trans('label.cancel')}
                    </Button>
                    <Button
                        disabled={isLoading}
                        variant="accent"
                        onClick={onConfirm}
                    >
                        {isLoading && (
                            <Icon name="loader" className="gx-spin" />
                        )}
                        <span>{trans('label.confirm')}</span>
                    </Button>
                </>
            }
            title={trans('label.confirm_convert')}
            isOpen={isOpen}
            onClose={onClose}
            onConfirm={onConfirm}
        >
            {trans('label.convert_ad_to_rented')}
        </Modal>
    );
}
