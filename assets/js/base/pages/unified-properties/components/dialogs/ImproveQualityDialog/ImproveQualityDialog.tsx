import { Button } from '@gx-design/button';
import { Modal } from '@gx-design/modal';
import { trans } from '@pepita-i18n/babelfish';
import {
    createContext,
    PropsWithChildren,
    Suspense,
    useCallback,
    useContext,
    useMemo,
    useState,
} from 'react';
import { ImproveQualityContent } from './ImproveQualityContent';
import { Loader } from '@gx-design/loader';
import { ErrorBoundary } from 'react-error-boundary';

type ImproveQualityDialogValues = {
    type: 'auction' | 'property' | 'new_construction';
    propertyId: number;
} | null;

const ImproveQualityDialogReaderContext = createContext<
    ImproveQualityDialogValues | undefined
>(undefined);

export function useImproveQualityDialogReader() {
    const context = useContext(ImproveQualityDialogReaderContext);
    if (context === undefined) {
        throw new Error(
            'useImproveQualityDialogReader must be used within a ImproveQualityDialogProvider'
        );
    }
    return context;
}

const ImproveQualityDialogWriterContext = createContext<
    ((args: ImproveQualityDialogValues) => void) | undefined
>(undefined);

export function useImproveQualityDialogWriter() {
    const context = useContext(ImproveQualityDialogWriterContext);
    if (context === undefined) {
        throw new Error(
            'useImproveQualityDialogWriter must be used within a ImproveQualityDialogProvider'
        );
    }
    return context;
}

export function ImproveQualityDialogProvider(props: PropsWithChildren) {
    const [ids, setIds] = useState<ImproveQualityDialogValues>(null);

    /**
     * Callback to set the ids in the context
     * @param newIds
     */
    const setIdsCallback = useCallback((newIds: ImproveQualityDialogValues) => {
        setIds(newIds);
    }, []);

    return (
        <ImproveQualityDialogWriterContext.Provider value={setIdsCallback}>
            <ImproveQualityDialogReaderContext.Provider value={ids}>
                {props.children}
            </ImproveQualityDialogReaderContext.Provider>
        </ImproveQualityDialogWriterContext.Provider>
    );
}

export function useOpenImproveQualityDialog() {
    const setIds = useImproveQualityDialogWriter();

    return useMemo(
        () => ({
            open: (args: ImproveQualityDialogValues) => {
                setIds(args);
            },
            close: () => {
                setIds(null);
            },
        }),
        [setIds]
    );
}

export function ImproveQualityDialog() {
    const info = useImproveQualityDialogReader();
    const { close } = useOpenImproveQualityDialog();
    const isOpen = Boolean(info);

    if (!info) {
        return null;
    }

    return (
        <Modal
            footer={
                <Button variant="default" onClick={close}>
                    {trans('label.close')}
                </Button>
            }
            size="large"
            closeAction
            isOpen={isOpen}
            title={trans('label.improve_ad_quality')}
            onClose={close}
        >
            <ErrorBoundary
                fallback={<>{trans('label.exception.GENERIC_ERROR')}</>}
            >
                <Suspense fallback={<Loader variant="inline" />}>
                    <ImproveQualityContent
                        propertyId={info.propertyId}
                        type={info.type}
                    />
                </Suspense>
            </ErrorBoundary>
        </Modal>
    );
}
