import { SidebarHeader } from './SidebarHeader';
import { MemoryRouter, NavLink } from 'react-router-dom';
import { describe, expect, it, vi } from 'vitest';
import { SidebarLeft } from 'gtx-react/components/Sidebar/Sidebar';
import { render, screen, within } from '#tests/react/testing-library-enhanced';
import { matchMediaMock } from '#tests/vitest/globals';

// Mock the Mixpanel hook to provide a spy function
const mockTrackEvent = vi.fn();
vi.mock('gtx-react/hooks/useMixpanelContext', () => ({
    useMixpanelContext: () => ({
        trackEvent: mockTrackEvent,
    }),
}));

vi.mock('./navItems', () => ({
    propertyNavItems: [
        { path: '/foo', icon: 'home', label: 'Foo' },
        { path: '/bar', icon: 'star', label: 'Bar' },
    ],
}));

describe('SidebarHeader', () => {
    it('renders DesktopHeader on largeDesktop with add button', async () => {
        render(
            <MemoryRouter initialEntries={['/']}>
                <SidebarLeft>
                    <SidebarHeader />
                </SidebarLeft>
            </MemoryRouter>
        );
        expect(screen.queryByRole('combobox')).toBeNull();
        expect(screen.getByText('label.add')).toBeInTheDocument();
    });

    it('should show the correct title based on the current path', async () => {
        const TestRouteSwitchComponent = () => {
            return (
                <MemoryRouter initialEntries={['/foo']}>
                    <NavLink to="/bar">bar</NavLink>
                    <SidebarLeft>
                        <SidebarHeader />
                    </SidebarLeft>
                </MemoryRouter>
            );
        };
        render(<TestRouteSwitchComponent />);

        expect(screen.getByText('Foo')).toBeInTheDocument();
        expect(screen.queryByText('Bar')).not.toBeInTheDocument();

        const switchRouteButton = screen.getByRole('link', {
            name: 'bar',
        });
        switchRouteButton.click();
        expect(await screen.findByText('Bar')).toBeInTheDocument();
        expect(screen.queryByText('Foo')).not.toBeInTheDocument();
    });

    it('renders MobileHeader when not largeDesktop with combobox navigation', () => {
        matchMediaMock.mockImplementation((query) => ({
            matches: false,
            media: query,
            onchange: null,
            addListener: vi.fn(),
            removeListener: vi.fn(),
            addEventListener: vi.fn(),
            removeEventListener: vi.fn(),
            dispatchEvent: vi.fn(),
        }));

        render(
            <MemoryRouter initialEntries={['/bar']}>
                <SidebarHeader />
            </MemoryRouter>
        );
        const combobox = screen.getByRole('combobox');
        expect(combobox).toBeInTheDocument();
        expect(within(combobox).getByText('Bar')).toBeInTheDocument();
        expect(within(combobox).getByText('Foo')).toBeInTheDocument();
    });
});
