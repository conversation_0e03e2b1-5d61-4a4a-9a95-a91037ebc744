import { SidebarLeft } from 'gtx-react/components/Sidebar/Sidebar';
import { FC, PropsWithChildren, useCallback, useRef, useState } from 'react';
import { SidebarActions } from './SidebarActions';
import { SidebarHeader } from './SidebarHeader';
import clsx from 'clsx';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';
import { DropdownProvider } from '@gx-design/dropdown';

const SectionContent = (props: PropsWithChildren) => {
    const dropdownRef = useRef<HTMLDivElement | null>(null);
    const [scrollState, setScrollState] = useState<
        'no-scroll' | 'start-scroll' | 'end-scroll' | undefined
    >(undefined);

    const handleScroll = useCallback(
        (event: React.UIEvent<HTMLDivElement> | { target: HTMLDivElement }) => {
            const target = event.target as HTMLDivElement;
            const { scrollLeft, scrollWidth, clientWidth } = target;

            if (scrollWidth <= clientWidth) {
                setScrollState('no-scroll');
            } else if (scrollLeft === 0 && scrollWidth > clientWidth) {
                setScrollState('start-scroll');
            } else if (scrollLeft + clientWidth >= scrollWidth - 1) {
                setScrollState('end-scroll');
            } else {
                setScrollState(undefined);
            }
        },
        []
    );

    const contentRef = useCallback(
        (node: HTMLDivElement | null) => {
            if (node) {
                dropdownRef.current = node;
                handleScroll({ target: node });
            }
        },
        [handleScroll]
    );

    return (
        <div
            ref={contentRef}
            onScroll={handleScroll}
            className={clsx('crm-section__content', scrollState)}
        >
            <DropdownProvider dropdownAnchorElement={dropdownRef}>
                <>{props.children}</>
            </DropdownProvider>
        </div>
    );
};

export const SidebarViewWrapper: FC<PropsWithChildren> = ({
    children: viewComponent,
}) => {
    const { trackEvent } = useMixpanelContext();
    return (
        <SidebarLeft
            initialValue={true}
            section="property-list"
            onSidebarToggleClick={(open) => {
                trackEvent({
                    event: 'properties_sidebar_visibility_changed',
                    extra: { ['visibility_state']: open ? 'Show' : 'Hide' },
                });
            }}
        >
            <div className="crm-section">
                <SidebarLeft.Bar>
                    <SidebarActions />
                </SidebarLeft.Bar>
                <SectionContent>
                    <div className="crm-section__contentHeader">
                        <SidebarHeader />
                    </div>
                    {viewComponent}
                </SectionContent>
            </div>
        </SidebarLeft>
    );
};
