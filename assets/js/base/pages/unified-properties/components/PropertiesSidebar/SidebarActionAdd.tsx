import { ActionList, ActionListItem } from '@gx-design/action-list';
import { Dropdown } from '@gx-design/dropdown';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import { ENDPOINTS } from 'constants/property';
import clsx from 'clsx';
import { useMixpanelContext } from 'gtx-react/hooks/useMixpanelContext';

const LIST_ACTION_ADD: {
    text: string;
    url: string;
    key: string;
}[] = [
    {
        text: 'label.estates',
        url: ENDPOINTS.propertyAdd,
        key: 'properties',
    },
    {
        text: 'label.new_buildings',
        url: ENDPOINTS.newConstructionAdd,
        key: 'new-constructions',
    },
    {
        text: 'label.auctions',
        url: ENDPOINTS.auctionAdd,
        key: 'auctions',
    },
    {
        text: 'label.sold_rented',
        url: ENDPOINTS.saleRentPropertyAdd,
        key: 'sold-rented',
    },
];

export const SidebarActionAdd = ({
    position,
    variant,
    dropdownClassName = '',
    dropdownButtonClassName = '',
}: {
    position: 'bottomRight' | 'topLeft' | 'topRight' | 'bottomLeft';
    variant?: 'default' | 'accent' | 'ghost' | 'chip';
    dropdownClassName?: string;
    dropdownButtonClassName?: string;
}) => {
    const { trackEvent } = useMixpanelContext();

    return (
        <Dropdown
            className={dropdownClassName}
            buttonIsIconOnly={false}
            showCaret={true}
            buttonVariant={variant}
            buttonContent={
                <span>
                    <Icon name="plus" />
                    <span>{trans('label.add')}</span>
                </span>
            }
            position={position}
            buttonClassName={clsx('gx-button--small', dropdownButtonClassName)}
        >
            <ActionList>
                {LIST_ACTION_ADD.map((action) => (
                    <ActionListItem
                        key={action.key}
                        target="_blank"
                        href={action.url}
                        text={trans(action.text)}
                        onClick={() => {
                            trackEvent({
                                event: 'properties_insertion_access',
                                extra: {
                                    ['listing_macrocategory']: trans(
                                        action.text
                                    ),
                                },
                            });
                        }}
                    />
                ))}
            </ActionList>
        </Dropdown>
    );
};
