import PropertyListFiltersSkeleton from './PropertyListFiltersSkeleton';
import { ActivePropertyListSkeleton } from './PropertyListSkeleton';
import PropertyListVisibilitySummarySkeleton from './PropertyListVisibilitySummarySkeleton';
import { PropertyStatus } from '../../types';

export const PropertyListViewSkeleton = (props: { status: PropertyStatus }) => {
    return (
        <>
            {props.status !== 'sold' ? (
                <PropertyListVisibilitySummarySkeleton />
            ) : null}
            <PropertyListFiltersSkeleton />
            <div className="crm-section__contentList">
                <ActivePropertyListSkeleton {...props} />
            </div>
        </>
    );
};
