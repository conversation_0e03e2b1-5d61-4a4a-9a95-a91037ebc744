import { render, screen, waitFor } from '#tests/react/testing-library-enhanced';
import { QueryClientProvider } from '@tanstack/react-query';
import { createQueryClient } from 'lib/queryClient';
import { describe, expect, it, vi } from 'vitest';
import { PropertyListAutocomplete } from './PropertyListAutoComplete';
import { useState } from 'react';

const mockTrackEvent = vi.fn();
vi.mock('gtx-react/hooks/useMixpanelContext', () => ({
    useMixpanelContext: () => ({
        trackEvent: mockTrackEvent,
    }),
}));

const ClearSelectedValueOnCleanupCase = ({ term }: { term: string }) => {
    const [state, setState] = useState({
        term,
        cityId: '1212',
        macroZoneId: '12122',
    }) as any;

    return (
        <>
            <button onClick={() => setState({})}>cleanup</button>
            <PropertyListAutocomplete
                filters={state}
                setFilters={setState}
                status="active"
            />
        </>
    );
};

describe('PropertyListAutocomplete', () => {
    it('should render the autocomplete component', () => {
        const onSetFilters = vi.fn();
        render(
            <PropertyListAutocomplete
                filters={{ term: '' } as any}
                setFilters={onSetFilters}
                status="active"
            />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        expect(screen.getByRole('combobox')).toBeInTheDocument();
        expect(screen.getByRole('combobox')).toBeEnabled();
    });

    it('should call onSelectItem when option is clicked', async () => {
        const onSelectItem = vi.fn();
        const { user } = render(
            <PropertyListAutocomplete
                filters={{ term: '' } as any}
                setFilters={onSelectItem}
                status="active"
            />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        const cb = screen.getByRole('combobox');
        await user.click(cb);
        await user.type(cb, 'roma');

        const listbox = await screen.findByRole('listbox');

        await waitFor(() => expect(listbox.innerHTML).not.toBe(''));
        const text = screen.getAllByRole('option')[0]?.textContent?.trim();
        expect(text).toBe('label.search_by_referencelabel.enter');
        // const listElement = await screen.findAllByRole('listbox');
        await user.click(screen.getAllByRole('option')[0]!);
        // expect(onSelectItem).toHaveBeenCalled();
        expect(cb).toHaveValue('roma');
        expect(cb).toBeDisabled();
        // screen.debug();

        await waitFor(() => expect(onSelectItem).toHaveBeenCalled());
        expect(onSelectItem).toHaveBeenCalledWith(
            expect.objectContaining({
                term: expect.any(String),
                city: expect.any(String),
                zones: expect.any(String),
            })
        );
    });

    it('should display the reset button when input has value', async () => {
        const onSelectItem = vi.fn();
        const { user } = render(
            <PropertyListAutocomplete
                filters={{ term: 'selected term' } as any}
                setFilters={onSelectItem}
                status="active"
            />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        const resetBtn = screen.getByRole('button', { name: 'label.reset' });
        expect(resetBtn).not.toHaveClass('is-hidden');

        expect(resetBtn).toBeInTheDocument();
        await user.click(resetBtn);
        expect(screen.getByRole('combobox')).toHaveValue('');
        expect(resetBtn).toHaveClass('is-hidden');
    });

    it('should disable input when item is already selected', async () => {
        const onSelectItem = vi.fn();
        render(
            <PropertyListAutocomplete
                filters={{ term: 'aready selected item' } as any}
                setFilters={onSelectItem}
                status="active"
            />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        expect(screen.getByRole('combobox')).toBeDisabled();
    });

    it('should clean filter on external filters change', async () => {
        const { user } = render(
            <ClearSelectedValueOnCleanupCase term="default" />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        expect(screen.getByRole('combobox')).toHaveValue('default');
        await user.click(screen.getByRole('button', { name: 'cleanup' }));

        expect(screen.getByRole('combobox')).toHaveValue('');
    });

    it('should reset filters', async () => {
        const onSelectItem = vi.fn();

        const { user } = render(
            <PropertyListAutocomplete
                filters={{ term: 'aready selected item' } as any}
                setFilters={onSelectItem}
                status="active"
            />,
            {
                wrapper: ({ children }) => {
                    return (
                        <QueryClientProvider client={createQueryClient()}>
                            {children}
                        </QueryClientProvider>
                    );
                },
            }
        );

        expect(screen.getByRole('combobox')).toHaveValue(
            'aready selected item'
        );
        await user.click(screen.getByRole('button', { name: 'label.reset' }));

        expect(screen.getByRole('combobox')).toHaveValue('');
        expect(onSelectItem).toHaveBeenCalledWith(
            expect.objectContaining({
                term: '',
                city: '',
                zones: '',
                ref: '',
                code: '',
            })
        );
    });
});
