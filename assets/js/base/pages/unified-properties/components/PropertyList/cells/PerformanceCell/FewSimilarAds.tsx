import { Icon } from '@gx-design/icon';
import { Popover } from '@gx-design/popover';
import { trans } from '@pepita-i18n/babelfish';
import {
    APARTAMENTS_TYPOLOGIES,
    CITY_WITH_MACROZONES,
    HOUSES_TYPOLOGIES,
} from 'constants/property';

export const groupResidentialTypologies = (typology?: number) => {
    if (!typology) {
        return null;
    }

    if (HOUSES_TYPOLOGIES.indexOf(typology) !== -1) {
        return trans('label.houses');
    }

    if (APARTAMENTS_TYPOLOGIES.indexOf(typology) !== -1) {
        return trans('label.apartaments');
    }

    return null;
};

type RequiredPropertyFeatures = {
    typologyId?: number;
    typology?: string;
    subTypology?: string;
    subTypologyId?: number;
    cityMacroZoneTypeId?: number;
    contract?: string;
    zone?: string;
    city?: string;
};

const getFormattedTypology = (args: RequiredPropertyFeatures): string => {
    const typologyName = args?.subTypology ? args.subTypology : args?.typology;
    const typologyId = args?.subTypologyId
        ? args.subTypologyId
        : args?.typologyId;

    const groupedTypology = groupResidentialTypologies(typologyId);
    return groupedTypology ? groupedTypology : typologyName || '';
};

const getFormattedContract = (contract?: string): string => {
    return contract
        ? ` ${trans('label.in')} ${contract.toLocaleLowerCase()}`
        : '';
};

const getFormattedZone = (
    zone?: string,
    cityMacroZoneTypeId?: number
): string => {
    const cityWithMacrozones = cityMacroZoneTypeId === CITY_WITH_MACROZONES;
    return zone && cityWithMacrozones
        ? ` ${trans('label.within_zone')} ${zone}`
        : '';
};

const getFormattedCity = (city?: string): string => {
    return city ? ` ${trans('label.to')} ${city}` : '';
};

const usePropertyFeatures = (args: RequiredPropertyFeatures): string => {
    const typology = getFormattedTypology(args);
    const contractInfo = getFormattedContract(args?.contract);
    const zoneInfo = getFormattedZone(args?.zone, args?.cityMacroZoneTypeId);
    const cityInfo = getFormattedCity(args?.city);

    return `${typology}${contractInfo}${zoneInfo}${cityInfo}`;
};

export function FewSimilarAds({
    adPortal,
    ...rest
}: { adPortal: string } & RequiredPropertyFeatures) {
    const features = usePropertyFeatures(rest);

    return (
        <div
            className="performance-text-maxWidth"
            style={{ whiteSpace: 'normal' }}
        >
            <span>{trans('label.property_performance.unavailable')}</span>{' '}
            <Popover
                title={''}
                onEdge={false}
                large
                content={
                    <>
                        <span>
                            {trans(
                                'label.performances.unique_property_with_following_features',
                                {
                                    PORTAL: adPortal,
                                }
                            )}
                        </span>
                        <br />
                        <strong>{features}</strong>
                    </>
                }
            >
                <Icon className="gx-icon--info" name="info-circle--active" />
            </Popover>
        </div>
    );
}
