import { beforeEach, describe, expect, it, vi } from 'vitest';
import { render, screen } from '#tests/react/testing-library-enhanced';
import { QueryClientProvider } from '@tanstack/react-query';
import { CellContext } from '@tanstack/react-table';
import { createQueryClient } from 'lib/queryClient';
import { ComponentProps, Suspense } from 'react';
import { Property } from '../types';
import { ExtravisibilityCell } from './ExtravisibilityCell';
import { ConfigProvider } from '../../ConfigProvider';

const defaultConfig: ComponentProps<typeof ConfigProvider>['value'] = {
    adPortal: 'test',
    allExtraVisibilityEnabled: true,
    appLocale: 'it',
    cockadeIconThreshold: 0,
    firstPageTagThreshold: 0,
    hasConvertibleAd: true,
    isGetrix: true,
    secretPropertyEnabled: true,
    shouldShowFirstPageTag: true,
    performanceFeatureEnabled: true,
};

// Mock the Mixpanel hook to provide a spy function
const mockTrackEvent = vi.fn();
vi.mock('gtx-react/hooks/useMixpanelContext', () => ({
    useMixpanelContext: () => ({
        trackEvent: mockTrackEvent,
    }),
}));

// Mock child components to simplify testing and focus on the cell's logic
vi.mock('../../Extravisibility', () => ({
    ExtravisibilityWithPopover: ({
        name,
        short,
    }: {
        name: string;
        short: boolean;
    }) => (
        <div data-testid={`extravisibility-${name}`} data-short={short}>
            {name}
        </div>
    ),
}));

vi.mock('@gx-design/badge', () => ({
    Badge: ({ text }: { text: string }) => (
        <span data-testid="badge">{text}</span>
    ),
}));

vi.mock('@gx-design/tooltip', () => ({
    Tooltip: ({ children, text }: any) => (
        <div data-testid="tooltip" data-text={text}>
            {children}
        </div>
    ),
}));

// --- Test Suite ---

// Helper to create the complex props object required by the cell
const createMockCellContext = (
    value: Property['extraVisibilities'],
    type: Property['type'] = 'property',
    id: number = 123
): CellContext<Property, Property['extraVisibilities']> =>
    ({
        cell: {
            getValue: () => value,
            row: { original: { id, type, filterStatus: 'active' } },
        },
        row: {
            original: { id, type, filterStatus: 'active' },
        },
    }) as any; // Using 'as any' to avoid mocking the entire table instance

const renderExtravisibilityCell = (
    props: ComponentProps<typeof ExtravisibilityCell>
) => {
    return render(<ExtravisibilityCell {...props} />, {
        wrapper({ children }) {
            return (
                <QueryClientProvider client={createQueryClient()}>
                    <ConfigProvider value={defaultConfig}>
                        <Suspense>{children}</Suspense>
                    </ConfigProvider>
                </QueryClientProvider>
            );
        },
    });
};

describe('ExtravisibilityCell', () => {
    beforeEach(() => {
        // Reset the mock function's call history before each test
        vi.clearAllMocks();
    });

    describe('Promote Button', () => {
        it('should render the promote button with the correct link for standard properties', async () => {
            const { user } = renderExtravisibilityCell({
                ...createMockCellContext(
                    {
                        type: 'AGENCY_PAGE',
                        payload: {
                            searchable: false,
                            showcase: false,
                            sky: false,
                            top: false,
                            star: false,
                        },
                    },
                    'property',
                    456
                ),
            });

            const button = await screen.findByRole('link', {
                name: 'label.promote',
            });
            expect(button).toBeInTheDocument();
            expect(button).toHaveAttribute(
                'href',
                '/inserimento_annuncio.php?step=4&idAnnuncio=456'
            );

            await user.click(button);
            expect(mockTrackEvent).toHaveBeenCalledWith({
                event: 'properties_promote_listing',
                extra: {
                    ['property_id']: 456,
                    ['list_view']: expect.any(String),
                },
            });
        });

        it('should render the promote button with the correct link for new constructions', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext(
                    {
                        type: 'AGENCY_PAGE',
                        payload: {
                            searchable: false,
                            showcase: false,
                            sky: false,
                            top: false,
                            star: false,
                        },
                    },
                    'new_construction',
                    789
                ),
            });

            const button = await screen.findByRole('link', {
                name: 'label.promote',
            });
            expect(button).toBeInTheDocument();
            expect(button).toHaveAttribute(
                'href',
                '/v2/nuove-costruzioni?idAnnuncio=789&step=5'
            );
        });
    });

    describe('Visibility States', () => {
        it('should render agency page text when value is undefined', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AGENCY_PAGE',
                    payload: {
                        searchable: false,
                        showcase: false,
                        sky: false,
                        top: false,
                        star: false,
                    },
                }),
            });
            expect(
                await screen.findByText('label.agency_page_only')
            ).toBeInTheDocument();
        });

        it('should render the secret badge and  when type is SECRET', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'SECRET',
                    payload: {
                        searchable: false,
                        showcase: false,
                        sky: false,
                        top: false,
                        star: false,
                    },
                }),
            });
            expect(await screen.findByTestId('badge')).toHaveTextContent(
                'label.secret'
            );
        });

        it('should render agency page text if all payload values are false', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: false,
                        top: false,
                        showcase: false,
                        sky: false,
                        star: false,
                    },
                }),
            });

            expect(
                await screen.findByText('label.agency_page_only')
            ).toBeInTheDocument();

            expect(
                screen.queryByTestId(/extravisibility-/)
            ).not.toBeInTheDocument();
        });

        it('should render a single Extravisibility component with short=false if one value is true', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: false,
                        top: true,
                        showcase: false,
                        sky: false,
                        star: false,
                    },
                }),
            });

            const evComponent = await screen.findByTestId(
                'extravisibility-top'
            );
            expect(evComponent).toBeInTheDocument();
            expect(evComponent).toHaveAttribute('data-short', 'false');
            expect(
                screen.queryByTestId('extravisibility-searchable')
            ).not.toBeInTheDocument();
        });

        it('should render multiple Extravisibility components with short=true if multiple values are true', async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: false,
                        top: true,
                        showcase: true,
                        sky: false,
                        star: false,
                    },
                }),
            });

            const evShowcase = await screen.findByTestId(
                'extravisibility-showcase'
            );
            const evTop = screen.getByTestId('extravisibility-top');

            expect(evShowcase).toBeInTheDocument();
            expect(evShowcase).toHaveAttribute('data-short', 'true');
            expect(evTop).toBeInTheDocument();
            expect(evTop).toHaveAttribute('data-short', 'true');
        });

        it("should hide 'searchable' if other visibilities are also active", async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: false,
                        top: true,
                        showcase: true,
                        sky: false,
                        star: false,
                    },
                }),
            });
            const evTop = await screen.findByTestId('extravisibility-top');

            expect(
                screen.queryByTestId('extravisibility-searchable')
            ).not.toBeInTheDocument();
            expect(evTop).toBeInTheDocument();
            expect(evTop).toHaveAttribute('data-short', 'true');
        });

        it("should show 'searchable' if it is the ONLY active visibility", async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: true,
                        top: false,
                        showcase: false,
                        sky: false,
                        star: false,
                    },
                }),
            });

            const evSearchable = await screen.findByTestId(
                'extravisibility-searchable'
            );
            expect(evSearchable).toBeInTheDocument();
            expect(evSearchable).toHaveAttribute('data-short', 'false');
            expect(
                screen.queryByTestId('extravisibility-top')
            ).not.toBeInTheDocument();
        });

        it("should not show 'searchable' if there are more active visibilities", async () => {
            renderExtravisibilityCell({
                ...createMockCellContext({
                    type: 'AVAILABLE',
                    payload: {
                        searchable: true,
                        top: true,
                        showcase: true,
                        sky: false,
                        star: false,
                    },
                }),
            });

            expect(
                await screen.findByTestId('extravisibility-top')
            ).toBeInTheDocument();
            const evSearchable = screen.queryByTestId(
                'extravisibility-searchable'
            );
            expect(evSearchable).not.toBeInTheDocument();

            expect(
                screen.getByTestId('extravisibility-showcase')
            ).toBeInTheDocument();
        });
    });
});
