import 'regenerator-runtime/runtime';
import { ApiResponse } from 'types/api-response';
import { LookupItem } from 'types/lookup';
import { AgencyInvoicingDataResponse } from '../types/apiResponse';

export const extractAgencyData = (): {
    agency: AgencyInvoicingDataResponse;
} => {
    try {
        const agencyInfoInnerHTML = document.getElementById('agency-info')?.innerHTML;

        const agency: AgencyInvoicingDataResponse | null = agencyInfoInnerHTML ? JSON.parse(agencyInfoInnerHTML) : null;

        if (agency) {
            return { agency };
        } else {
            throw new Error('No data agency found');
        }
    } catch (error) {
        throw new Error('Error parsing JSON to extract agency data');
    }
};

export const extractCountriesData = (): { countries: Array<LookupItem> } => {
    try {
        const geographyCountriesInnerHTML = document.getElementById('geography-countries')?.innerHTML;
        const content: { data: LookupItem[] } | null = geographyCountriesInnerHTML
            ? JSON.parse(geographyCountriesInnerHTML)
            : null;
        if (content?.data) {
            return { countries: content.data };
        } else {
            throw new Error('No geography countries found');
        }
    } catch (error) {
        throw new Error('Error parsing JSON to extract geography countries');
    }
};
