import React, { FC } from 'react';
import { trans } from '@pepita-i18n/babelfish';
import { RadioGroup } from '@gx-design/radio';
import useFormikGeoFields from 'gtx-react/hooks/useFormikGeoFields';
import {
    GxFkAddonNumberInput,
    GxFkInput,
    GxFkRadio,
    GxFkSelect,
} from 'gtx-react/components/gx-formik';
import { useFormikFocusErroredFieldOnSubmit } from 'gtx-react/hooks/useFormikFocusErroredFieldOnSubmit';

import { useAgencySelector } from '../redux/invoicing-data-administration/hooks';
import { getCountries } from '../redux/invoicing-data-administration/slices/countries';
import { InfoModal } from '../components/InfoModal';
import { SHOULD_SHOW_INFO_MODAL } from '../constants';

const SectionCompanyData = () => {
    return (
        <div className="gx-section">
            <div className="gx-title-1">
                <span>{trans('label.company_data')}</span>
                {SHOULD_SHOW_INFO_MODAL && <InfoModal />}
            </div>
            <div className="gx-row">
                <div className="gx-col-xs-12 gx-col-sm-10">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.ragionesociale
                            type="text"
                            label={trans('label.business_name')}
                            placeholder={trans('label.insert_business_name')}
                            maxLength={100}
                            required
                            name="ragionesociale"
                        />
                    </div>
                </div>

                <div className="gx-col-xs-12 gx-col-sm-2">
                    <div className="gx-box-row">
                        <RadioGroup
                            // data.agency.sociounico
                            variant="button"
                            label={trans('label.single_member')}
                        >
                            <GxFkRadio
                                name="socioUnico"
                                label={trans('label.yes')}
                                value="true"
                            />
                            <GxFkRadio
                                name="socioUnico"
                                label={trans('label.no')}
                                value="false"
                            />
                        </RadioGroup>
                    </div>
                </div>

                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.piva
                            type="text"
                            label={trans('label.vat')}
                            placeholder={trans('label.insert_vat_number_2')}
                            maxLength={12}
                            required
                            name="piva"
                        />
                    </div>
                </div>

                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.codice_fiscale
                            type="text"
                            label={trans('label.fiscal_code')}
                            placeholder={trans('label.insert_fiscal_code_2')}
                            maxLength={16}
                            id="codice_fiscale"
                            tooltipHelper={trans('label.different_vat')}
                            name="codice_fiscale"
                        />
                    </div>
                </div>

                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.emailPec
                            type="email"
                            label={trans('label.email_pec')}
                            placeholder={trans('label.insert_email_pec')}
                            name="emailPec"
                        />
                    </div>
                </div>

                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.codiceSdi
                            type="text"
                            label={trans('label.sdi_code')}
                            placeholder={trans('label.insert_sdi_code')}
                            maxLength={7}
                            name="codiceSdi"
                            tooltipHelper={trans(
                                'label.e_invoicing_recipient_code'
                            )}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

const SectionRegisteredOffice = () => {
    const { countries: options } = useAgencySelector(getCountries);

    const getFormikGeoFieldProps = useFormikGeoFields([
        { type: 'country', idKey: 'idNazioneSedeLegale', options },
        {
            type: 'province',
            provinceBy: 'country',
            idKey: 'idProvinciaSedeLegale',
        },
        {
            type: 'city',
            idKey: 'idComuneSedeLegale',
        },
    ]);

    return (
        <div className="gx-section">
            <div className="gx-title-1">{trans('label.registered_office')}</div>
            <div className="gx-row">
                <div className="gx-col-xs-12 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'country',
                                'idNazioneSedeLegale'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'province',
                                'idProvinciaSedeLegale'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'city',
                                'idComuneSedeLegale'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-9 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.indirizzoSedeLegale
                            type="text"
                            label={trans('label.address')}
                            placeholder={trans('label.insert_address')}
                            minLength={3}
                            maxLength={100}
                            name="indirizzoSedeLegale"
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-3">
                    <div className="gx-box-row">
                        <GxFkInput
                            // data.agency.capSedeLegale
                            type="text"
                            label={trans('label.postal_code')}
                            placeholder={trans('label.insert_postal_code_2')}
                            maxLength={5}
                            name="capSedeLegale"
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

// Iscrizione al Repertorio economico e amministrativo
const SectionREA: FC = () => {
    const { countries: options } = useAgencySelector(getCountries);

    const getFormikGeoFieldProps = useFormikGeoFields([
        { type: 'country', idKey: 'idNazioneREA', options },
        {
            type: 'province',
            provinceBy: 'country',
            idKey: 'idProvinciaREA',
        },
        {
            type: 'city',
            idKey: 'idComuneREA',
        },
    ]);
    return (
        <div className="gx-section">
            <div className="gx-title-1">
                {trans('label.registration_economic_administrative_repertory')}
            </div>
            <div className="gx-row">
                <div className="gx-col-xs-12 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'country',
                                'idNazioneREA'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'province',
                                'idProvinciaREA'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps('city', 'idComuneREA')}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkInput
                            type="text"
                            label={trans('label.rea_number')}
                            placeholder={trans('label.insert_rea_code_2')}
                            maxLength={100}
                            name="numeroREA"
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkAddonNumberInput
                            addon={
                                {
                                    position: 'left',
                                    type: 'icon',
                                    value: 'euro-circle',
                                } as any
                            }
                            label={trans('label.share_capital')}
                            name="capitaleSociale"
                            placeholder={trans('label.enter_social_capital')}
                            maximumFractionDigits={2}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export const SectionRBR = () => {
    const { countries: options } = useAgencySelector(getCountries);

    const getFormikGeoFieldProps = useFormikGeoFields([
        { type: 'country', idKey: 'idNazioneRegistroImprese', options },
        {
            type: 'province',
            provinceBy: 'country',
            idKey: 'idProvinciaRegistroImprese',
        },
        {
            type: 'city',
            idKey: 'idComuneRegistroImprese',
        },
    ]);
    return (
        <div className="gx-section">
            <div className="gx-title-1">
                {trans('label.registration_business_register')}
            </div>
            <div className="gx-row">
                <div className="gx-col-xs-12 gtx-error-container">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'country',
                                'idNazioneRegistroImprese'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'province',
                                'idProvinciaRegistroImprese'
                            )}
                        />
                    </div>
                </div>
                <div className="gx-col-xs-12 gx-col-sm-6">
                    <div className="gx-box-row">
                        <GxFkSelect
                            {...getFormikGeoFieldProps(
                                'city',
                                'idComuneRegistroImprese'
                            )}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
};

export const InvoicingDataFormSections: FC = () => {
    useFormikFocusErroredFieldOnSubmit();

    return (
        <>
            <SectionCompanyData />
            <SectionRegisteredOffice />
            <SectionREA />
            <SectionRBR />
        </>
    );
};
