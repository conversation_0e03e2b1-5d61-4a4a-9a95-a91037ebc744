import { QueryClientProvider } from '@tanstack/react-query';
import { NotificationSettingsView } from '../views/NotificationSettingsView';
import { GtxPageWithoutStore } from 'gtx-react/components/GtxApp/GtxApp';
import { createQueryClient } from 'lib/queryClient';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { NotifyProvider } from '@gx-design/snackbar';

export const notificationSettingsPage: GtxPageWithoutStore = {
    container: NotificationSettingsView,
    header: {
        backUrl: document?.referrer,
    },
    path: '/clienti/imposta-notifiche',
    wrapper(props) {
        const queryClient = createQueryClient();

        return (
            <NotifyProvider>
                <QueryClientProvider client={queryClient}>
                    <ReactQueryDevtools />
                    {props.children}
                </QueryClientProvider>
            </NotifyProvider>
        );
    },
};
