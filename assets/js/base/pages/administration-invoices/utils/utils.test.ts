import { describe, it, expect } from 'vitest';
import type { BadgeProps } from '@gx-design/badge';
import { Invoice } from '../types/apiResponse';
import { renderStatusTagStyle } from './utils';

describe('renderStatusTagStyle', () => {
    const testCases: { status: Invoice['status']; expected: BadgeProps['style'] }[] = [
        { status: 'paid', expected: 'success' },
        { status: 'notPaid', expected: 'error' },
        { status: 'expired', expected: 'error' },
        { status: 'issued', expected: 'warning' },
        { status: 'canceled', expected: 'warning' },
    ];

    testCases.forEach(({ status, expected }) => {
        it(`returns "${expected}" for status "${status}"`, () => {
            const utils = renderStatusTagStyle(status);
            expect(utils).toBe(expected);
        });
    });
});
