import { Middleware } from "redux"
import { archiveSuccess, restoreSuccess } from "../actions/thread"
import { ThreadState } from "../types"


export const threadUpdateNotify: Middleware<any, ThreadState> = (store) => next => action => {

    const dispatch = store.dispatch

    const prevState = store.getState()

    let nextReturn = next(action)

    const nextState = store.getState()

    if (prevState.thread?.status !== nextState.thread?.status) {
        switch (nextState.thread.status) {
            case 'active':
                dispatch(restoreSuccess())
                break;
            case 'archived':
                dispatch(archiveSuccess())
        }

    }

    return nextReturn

}
