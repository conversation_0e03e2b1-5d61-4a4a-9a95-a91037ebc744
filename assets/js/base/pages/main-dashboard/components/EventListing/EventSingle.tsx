import { Button } from '@gx-design/button';
import { Icon } from '@gx-design/icon';
import { trans } from '@pepita-i18n/babelfish';
import clsx from 'clsx';
import { PROPERTY_IMAGE_PLACEHOLDER } from 'constants/property';
import type { PropsWithChildren } from 'react';
import { EventSingleSkeleton } from './EventSingleSkeleton';
import { EventItem } from '../../types';

type EventSingleProps = {
    label: string;
    showAllLabel: string;
    showAllLink: string;
    emptyMessage: string;
    className: string;
    isLoading: boolean;
    items: EventItem[];
};

export function EventSingle(props: EventSingleProps) {
    return (
        <div className={clsx('gtx-panel', props.className)}>
            <div className="gtx-panel__head clearfix">
                <div className="gx-title-2">{props.label}</div>
                <a className="gtx-panel__head__action" href={props.showAllLink}>
                    {props.showAllLabel}
                </a>
            </div>
            <div className="gtx-panel__body clearfix">
                {props.isLoading ? <EventSingleSkeleton /> : null}

                {!props.isLoading ? (
                    props.items.length > 0 ? (
                        <ul className="lista-annunci-panel" data-role="list">
                            {props.items.map((item) => (
                                <EventSingleItem key={item.id} {...item} />
                            ))}
                        </ul>
                    ) : (
                        <EventSingleEmpty emptyMessage={props.emptyMessage} />
                    )
                ) : null}
            </div>
        </div>
    );
}

type EventSingleEmptyProps = {
    emptyMessage: string;
};

function EventSingleEmpty(props: EventSingleEmptyProps) {
    return (
        <p className="gtx-panel__empty hidden" data-role="empty-result">
            {props.emptyMessage}
        </p>
    );
}

type EventSingleItemProps = PropsWithChildren<EventItem>;

function EventSingleItem(props: EventSingleItemProps) {
    return (
        <li data-role="listitem">
            {props.imageUrl === undefined ? null : (
                <img
                    className="lista-annunci-panel__pic"
                    src={props.imageUrl || PROPERTY_IMAGE_PLACEHOLDER}
                    alt=""
                />
            )}
            <div className="lista-annunci-panel__content gx-body-small">
                <span className="data">{props.date}</span>
                <span>{props.title}</span>
                <span>{props.location}</span>
                <span>{props.price}</span>
            </div>
            <Button
                className="lista-annunci-panel__action"
                as="a"
                href={props.link}
                target="_blank"
                iconOnly
                aria-label={trans('label.see_details')}
            >
                <Icon name="eye" />
            </Button>
        </li>
    );
}
