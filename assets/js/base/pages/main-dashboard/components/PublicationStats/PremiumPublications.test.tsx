import { render, screen } from '#tests/react/testing-library-enhanced';
import { gtxConstants } from '#tests/mock/window';
import { describe, expect, it, vi } from 'vitest';
import { PremiumPublications } from './PremiumPublications';

const onIncreaseClick = vi.fn();
const onActiveNewBuildingsClick = vi.fn();

describe('PremiumPublications', () => {
    it('should render correctly with new buildings info', () => {
        render(
            <PremiumPublications
                isNewBuildingsEnabled={true}
                isNewBuildingsActive={true}
                newBuildingsSecondHand={0}
                newBuildingsTotal={0}
                onIncreaseClick={onIncreaseClick}
                onActiveNewBuildingsClick={onActiveNewBuildingsClick}
                salesSecondHand={0}
                salesTotal={0}
                rentalSecondHand={0}
                rentalTotal={0}
            />
        );

        expect(
            screen.getByText(gtxConstants.PREMIUM_VISIBILITY_NAME)
        ).toBeInTheDocument();
        expect(screen.getByText('label.sale')).toBeInTheDocument();
        expect(screen.getByText('label.new_buildings')).toBeInTheDocument();
        expect(
            screen.getByText('label.request_activation')
        ).toBeInTheDocument();
    });

    it('should render correctly without new buildings info', () => {
        render(
            <PremiumPublications
                isNewBuildingsEnabled={false}
                isNewBuildingsActive={false}
                newBuildingsSecondHand={0}
                newBuildingsTotal={0}
                onIncreaseClick={onIncreaseClick}
                onActiveNewBuildingsClick={onActiveNewBuildingsClick}
                salesSecondHand={0}
                salesTotal={0}
                rentalSecondHand={0}
                rentalTotal={0}
            />
        );

        expect(
            screen.getByText(gtxConstants.PREMIUM_VISIBILITY_NAME)
        ).toBeInTheDocument();
        expect(screen.getByText('label.sale')).toBeInTheDocument();
        expect(
            screen.queryByText('label.new_buildings')
        ).not.toBeInTheDocument();
        expect(
            screen.queryByRole('button', { name: 'label.request_activation' })
        ).not.toBeInTheDocument();
    });

    it('should show the "label.new_buildings" feature', () => {
        render(
            <PremiumPublications
                isNewBuildingsEnabled={true}
                isNewBuildingsActive={false}
                newBuildingsSecondHand={0}
                newBuildingsTotal={12}
                onIncreaseClick={onIncreaseClick}
                onActiveNewBuildingsClick={onActiveNewBuildingsClick}
                salesSecondHand={0}
                salesTotal={0}
                rentalSecondHand={0}
                rentalTotal={0}
            />
        );

        expect(
            screen.getByText(gtxConstants.PREMIUM_VISIBILITY_NAME)
        ).toBeInTheDocument();
        expect(screen.getByText('label.sale')).toBeInTheDocument();
        expect(screen.getByText('label.new_buildings')).toBeInTheDocument();
        expect(
            screen.queryByRole('button', { name: 'label.request_activation' })
        ).not.toBeInTheDocument();
        expect(screen.getByText(`label.out_of ${12}`)).toBeInTheDocument();
    });

    it('should show "label.request_activation" button', () => {
        render(
            <PremiumPublications
                isNewBuildingsEnabled={true}
                isNewBuildingsActive={true}
                newBuildingsSecondHand={0}
                newBuildingsTotal={0}
                onIncreaseClick={onIncreaseClick}
                onActiveNewBuildingsClick={onActiveNewBuildingsClick}
                salesSecondHand={0}
                salesTotal={0}
                rentalSecondHand={0}
                rentalTotal={0}
            />
        );

        expect(
            screen.getByText(gtxConstants.PREMIUM_VISIBILITY_NAME)
        ).toBeInTheDocument();
        expect(screen.getByText('label.sale')).toBeInTheDocument();
        expect(
            screen.getByRole('button', { name: 'label.request_activation' })
        ).toBeInTheDocument();
    });
});
