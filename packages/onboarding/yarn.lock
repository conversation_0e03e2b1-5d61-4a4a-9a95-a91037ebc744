# This file is generated by running "yarn install" inside your project.
# Manual changes might be lost - proceed with caution!

__metadata:
  version: 6
  cacheKey: 8

"@discoveryjs/json-ext@npm:^0.5.0":
  version: 0.5.7
  resolution: "@discoveryjs/json-ext@npm:0.5.7"
  checksum: 2176d301cc258ea5c2324402997cf8134ebb212469c0d397591636cea8d3c02f2b3cf9fd58dcb748c7a0dade77ebdc1b10284fa63e608c033a1db52fddc69918
  languageName: node
  linkType: hard

"@gx-design/button@npm:^2.1.4":
  version: 2.1.4
  resolution: "@gx-design/button@npm:2.1.4"
  dependencies:
    "@gx-design/icon": ^2.0.10
    clsx: ^1.1.1
  peerDependencies:
    react: "*"
  checksum: d789119193ced91d42f89221605a3b51c087b79e74c40002be190ad4825fc44ee39c23ef5f36c6790e3c0ef22b87ed148c837ea69ce6b724552354346dec4215
  languageName: node
  linkType: hard

"@gx-design/icon-set@npm:^2.3.1":
  version: 2.3.1
  resolution: "@gx-design/icon-set@npm:2.3.1"
  peerDependencies:
    react: "*"
  checksum: dc73f9fda820644bae075a6b5a0703b495b5ae46a38c8e41cc875911e6905736d6dfdb07fb97f7c62536e66de662846f41d0497e014e4ac0abc11674bc3f4bd2
  languageName: node
  linkType: hard

"@gx-design/icon@npm:^2.0.10":
  version: 2.0.10
  resolution: "@gx-design/icon@npm:2.0.10"
  dependencies:
    "@gx-design/icon-set": ^2.3.1
    clsx: ^1.1.1
  peerDependencies:
    react: "*"
  checksum: 44e9c1a51741534ead021a37bbb778ba2d1f12fc554a9b155f82607c673d24a677e6e30ae3048684940f4da8091368abf63b7ea438c5668adbda6dd4d6fe3eb7
  languageName: node
  linkType: hard

"@gx-design/onboarding@npm:^2.1.6":
  version: 2.1.7
  resolution: "@gx-design/onboarding@npm:2.1.7"
  dependencies:
    "@gx-design/button": ^2.1.4
    "@gx-design/icon": ^2.0.10
    clsx: ^1.1.1
  peerDependencies:
    react: "*"
  checksum: 94771e04620e1ca9b786121d6ccd1fd48603f576d7270b4f97edbc8375a25c30e318e08f9eaa03d61ef3d393a7602c1fb4bfc0936e563e81a8521b186a2093d1
  languageName: node
  linkType: hard

"@jridgewell/gen-mapping@npm:^0.3.0":
  version: 0.3.2
  resolution: "@jridgewell/gen-mapping@npm:0.3.2"
  dependencies:
    "@jridgewell/set-array": ^1.0.1
    "@jridgewell/sourcemap-codec": ^1.4.10
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 1832707a1c476afebe4d0fbbd4b9434fdb51a4c3e009ab1e9938648e21b7a97049fa6009393bdf05cab7504108413441df26d8a3c12193996e65493a4efb6882
  languageName: node
  linkType: hard

"@jridgewell/resolve-uri@npm:3.1.0":
  version: 3.1.0
  resolution: "@jridgewell/resolve-uri@npm:3.1.0"
  checksum: b5ceaaf9a110fcb2780d1d8f8d4a0bfd216702f31c988d8042e5f8fbe353c55d9b0f55a1733afdc64806f8e79c485d2464680ac48a0d9fcadb9548ee6b81d267
  languageName: node
  linkType: hard

"@jridgewell/set-array@npm:^1.0.1":
  version: 1.1.2
  resolution: "@jridgewell/set-array@npm:1.1.2"
  checksum: 69a84d5980385f396ff60a175f7177af0b8da4ddb81824cb7016a9ef914eee9806c72b6b65942003c63f7983d4f39a5c6c27185bbca88eb4690b62075602e28e
  languageName: node
  linkType: hard

"@jridgewell/source-map@npm:^0.3.2":
  version: 0.3.2
  resolution: "@jridgewell/source-map@npm:0.3.2"
  dependencies:
    "@jridgewell/gen-mapping": ^0.3.0
    "@jridgewell/trace-mapping": ^0.3.9
  checksum: 1b83f0eb944e77b70559a394d5d3b3f98a81fcc186946aceb3ef42d036762b52ef71493c6c0a3b7c1d2f08785f53ba2df1277fe629a06e6109588ff4cdcf7482
  languageName: node
  linkType: hard

"@jridgewell/sourcemap-codec@npm:1.4.14, @jridgewell/sourcemap-codec@npm:^1.4.10":
  version: 1.4.14
  resolution: "@jridgewell/sourcemap-codec@npm:1.4.14"
  checksum: 61100637b6d173d3ba786a5dff019e1a74b1f394f323c1fee337ff390239f053b87266c7a948777f4b1ee68c01a8ad0ab61e5ff4abb5a012a0b091bec391ab97
  languageName: node
  linkType: hard

"@jridgewell/trace-mapping@npm:^0.3.14, @jridgewell/trace-mapping@npm:^0.3.9":
  version: 0.3.17
  resolution: "@jridgewell/trace-mapping@npm:0.3.17"
  dependencies:
    "@jridgewell/resolve-uri": 3.1.0
    "@jridgewell/sourcemap-codec": 1.4.14
  checksum: 9d703b859cff5cd83b7308fd457a431387db5db96bd781a63bf48e183418dd9d3d44e76b9e4ae13237f6abeeb25d739ec9215c1d5bfdd08f66f750a50074a339
  languageName: node
  linkType: hard

"@pepita/babelfish@npm:^1.8.5":
  version: 1.8.5
  resolution: "@pepita/babelfish@npm:1.8.5"
  dependencies:
    "@pepita/sprintf": ^1.0.2
  checksum: c4efb84057875a2c0baab37ec611cdde10a3b796f849afd1a32916b546f10553200e14c5763c8649248c27a1b64c564afca8c2ecd7a3be99d95c1c48ec95e0b8
  languageName: node
  linkType: hard

"@pepita/sprintf@npm:^1.0.2":
  version: 1.0.2
  resolution: "@pepita/sprintf@npm:1.0.2"
  checksum: 8e99d21f125afb87a8e1d690750000e3671504dcf8ce00dd3cbd4491c35cfbdb9b3485ff6ffa438537cb4e2f05c781d51218005bf25baa6e604c2c2015660600
  languageName: node
  linkType: hard

"@types/eslint-scope@npm:^3.7.3":
  version: 3.7.4
  resolution: "@types/eslint-scope@npm:3.7.4"
  dependencies:
    "@types/eslint": "*"
    "@types/estree": "*"
  checksum: ea6a9363e92f301cd3888194469f9ec9d0021fe0a397a97a6dd689e7545c75de0bd2153dfb13d3ab532853a278b6572c6f678ce846980669e41029d205653460
  languageName: node
  linkType: hard

"@types/eslint@npm:*":
  version: 8.21.1
  resolution: "@types/eslint@npm:8.21.1"
  dependencies:
    "@types/estree": "*"
    "@types/json-schema": "*"
  checksum: 584068441e4000c7b41c8928274fdcc737bc62f564928c30eb64ec41bbdbac31612f9fedaf490bceab31ec8305e99615166428188ea345d58878394683086fae
  languageName: node
  linkType: hard

"@types/estree@npm:*":
  version: 1.0.0
  resolution: "@types/estree@npm:1.0.0"
  checksum: 910d97fb7092c6738d30a7430ae4786a38542023c6302b95d46f49420b797f21619cdde11fa92b338366268795884111c2eb10356e4bd2c8ad5b92941e9e6443
  languageName: node
  linkType: hard

"@types/estree@npm:^0.0.51":
  version: 0.0.51
  resolution: "@types/estree@npm:0.0.51"
  checksum: e56a3bcf759fd9185e992e7fdb3c6a5f81e8ff120e871641607581fb3728d16c811702a7d40fa5f869b7f7b4437ab6a87eb8d98ffafeee51e85bbe955932a189
  languageName: node
  linkType: hard

"@types/json-schema@npm:*, @types/json-schema@npm:^7.0.8":
  version: 7.0.11
  resolution: "@types/json-schema@npm:7.0.11"
  checksum: 527bddfe62db9012fccd7627794bd4c71beb77601861055d87e3ee464f2217c85fca7a4b56ae677478367bbd248dbde13553312b7d4dbc702a2f2bbf60c4018d
  languageName: node
  linkType: hard

"@types/node@npm:*":
  version: 18.14.2
  resolution: "@types/node@npm:18.14.2"
  checksum: 53c07e721f6ae33de71306f6a0b75dae6066a4f55bd5484c93bd59ff25f0c5f004ceafeef509a4d0cb9e24a247efc34d50489bcc1b05a53ecc68e2fc088e65cb
  languageName: node
  linkType: hard

"@types/node@npm:^17.0.2":
  version: 17.0.45
  resolution: "@types/node@npm:17.0.45"
  checksum: aa04366b9103b7d6cfd6b2ef64182e0eaa7d4462c3f817618486ea0422984c51fc69fd0d436eae6c9e696ddfdbec9ccaa27a917f7c2e8c75c5d57827fe3d95e8
  languageName: node
  linkType: hard

"@types/prop-types@npm:*":
  version: 15.7.5
  resolution: "@types/prop-types@npm:15.7.5"
  checksum: 5b43b8b15415e1f298243165f1d44390403bb2bd42e662bca3b5b5633fdd39c938e91b7fce3a9483699db0f7a715d08cef220c121f723a634972fdf596aec980
  languageName: node
  linkType: hard

"@types/react-dom@npm:^17.0.2":
  version: 17.0.19
  resolution: "@types/react-dom@npm:17.0.19"
  dependencies:
    "@types/react": ^17
  checksum: 875a472d868b235435c905ded16cf92297bd2afb20a5a78f5dccd54312f6f038ccf452ea92bb41c0b39150c2f16f3ddff0265a2de756c6f63b0971dd5719578b
  languageName: node
  linkType: hard

"@types/react@npm:^17, @types/react@npm:^17.0.2":
  version: 17.0.53
  resolution: "@types/react@npm:17.0.53"
  dependencies:
    "@types/prop-types": "*"
    "@types/scheduler": "*"
    csstype: ^3.0.2
  checksum: dacfde02c260fd98bed2eb775ed0c7ce1397be4c0844f907a50763b081a4008f81f57071889a16eb1350ddcf0927f3cf1a6541702c8ad03de3c70383ef931e3f
  languageName: node
  linkType: hard

"@types/scheduler@npm:*":
  version: 0.16.2
  resolution: "@types/scheduler@npm:0.16.2"
  checksum: b6b4dcfeae6deba2e06a70941860fb1435730576d3689225a421280b7742318d1548b3d22c1f66ab68e414f346a9542f29240bc955b6332c5b11e561077583bc
  languageName: node
  linkType: hard

"@webassemblyjs/ast@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ast@npm:1.11.1"
  dependencies:
    "@webassemblyjs/helper-numbers": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
  checksum: 1eee1534adebeece635362f8e834ae03e389281972611408d64be7895fc49f48f98fddbbb5339bf8a72cb101bcb066e8bca3ca1bf1ef47dadf89def0395a8d87
  languageName: node
  linkType: hard

"@webassemblyjs/floating-point-hex-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/floating-point-hex-parser@npm:1.11.1"
  checksum: b8efc6fa08e4787b7f8e682182d84dfdf8da9d9c77cae5d293818bc4a55c1f419a87fa265ab85252b3e6c1fd323d799efea68d825d341a7c365c64bc14750e97
  languageName: node
  linkType: hard

"@webassemblyjs/helper-api-error@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-api-error@npm:1.11.1"
  checksum: 0792813f0ed4a0e5ee0750e8b5d0c631f08e927f4bdfdd9fe9105dc410c786850b8c61bff7f9f515fdfb149903bec3c976a1310573a4c6866a94d49bc7271959
  languageName: node
  linkType: hard

"@webassemblyjs/helper-buffer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-buffer@npm:1.11.1"
  checksum: a337ee44b45590c3a30db5a8b7b68a717526cf967ada9f10253995294dbd70a58b2da2165222e0b9830cd4fc6e4c833bf441a721128d1fe2e9a7ab26b36003ce
  languageName: node
  linkType: hard

"@webassemblyjs/helper-numbers@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-numbers@npm:1.11.1"
  dependencies:
    "@webassemblyjs/floating-point-hex-parser": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: 44d2905dac2f14d1e9b5765cf1063a0fa3d57295c6d8930f6c59a36462afecc6e763e8a110b97b342a0f13376166c5d41aa928e6ced92e2f06b071fd0db59d3a
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-bytecode@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-bytecode@npm:1.11.1"
  checksum: eac400113127832c88f5826bcc3ad1c0db9b3dbd4c51a723cfdb16af6bfcbceb608170fdaac0ab7731a7e18b291be7af68a47fcdb41cfe0260c10857e7413d97
  languageName: node
  linkType: hard

"@webassemblyjs/helper-wasm-section@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/helper-wasm-section@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
  checksum: 617696cfe8ecaf0532763162aaf748eb69096fb27950219bb87686c6b2e66e11cd0614d95d319d0ab1904bc14ebe4e29068b12c3e7c5e020281379741fe4bedf
  languageName: node
  linkType: hard

"@webassemblyjs/ieee754@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/ieee754@npm:1.11.1"
  dependencies:
    "@xtuc/ieee754": ^1.2.0
  checksum: 23a0ac02a50f244471631802798a816524df17e56b1ef929f0c73e3cde70eaf105a24130105c60aff9d64a24ce3b640dad443d6f86e5967f922943a7115022ec
  languageName: node
  linkType: hard

"@webassemblyjs/leb128@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/leb128@npm:1.11.1"
  dependencies:
    "@xtuc/long": 4.2.2
  checksum: 33ccc4ade2f24de07bf31690844d0b1ad224304ee2062b0e464a610b0209c79e0b3009ac190efe0e6bd568b0d1578d7c3047fc1f9d0197c92fc061f56224ff4a
  languageName: node
  linkType: hard

"@webassemblyjs/utf8@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/utf8@npm:1.11.1"
  checksum: 972c5cfc769d7af79313a6bfb96517253a270a4bf0c33ba486aa43cac43917184fb35e51dfc9e6b5601548cd5931479a42e42c89a13bb591ffabebf30c8a6a0b
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-edit@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-edit@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/helper-wasm-section": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-opt": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    "@webassemblyjs/wast-printer": 1.11.1
  checksum: 6d7d9efaec1227e7ef7585a5d7ff0be5f329f7c1c6b6c0e906b18ed2e9a28792a5635e450aca2d136770d0207225f204eff70a4b8fd879d3ac79e1dcc26dbeb9
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-gen@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-gen@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 1f6921e640293bf99fb16b21e09acb59b340a79f986c8f979853a0ae9f0b58557534b81e02ea2b4ef11e929d946708533fd0693c7f3712924128fdafd6465f5b
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-opt@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-opt@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-buffer": 1.11.1
    "@webassemblyjs/wasm-gen": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
  checksum: 21586883a20009e2b20feb67bdc451bbc6942252e038aae4c3a08e6f67b6bae0f5f88f20bfc7bd0452db5000bacaf5ab42b98cf9aa034a6c70e9fc616142e1db
  languageName: node
  linkType: hard

"@webassemblyjs/wasm-parser@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wasm-parser@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/helper-api-error": 1.11.1
    "@webassemblyjs/helper-wasm-bytecode": 1.11.1
    "@webassemblyjs/ieee754": 1.11.1
    "@webassemblyjs/leb128": 1.11.1
    "@webassemblyjs/utf8": 1.11.1
  checksum: 1521644065c360e7b27fad9f4bb2df1802d134dd62937fa1f601a1975cde56bc31a57b6e26408b9ee0228626ff3ba1131ae6f74ffb7d718415b6528c5a6dbfc2
  languageName: node
  linkType: hard

"@webassemblyjs/wast-printer@npm:1.11.1":
  version: 1.11.1
  resolution: "@webassemblyjs/wast-printer@npm:1.11.1"
  dependencies:
    "@webassemblyjs/ast": 1.11.1
    "@xtuc/long": 4.2.2
  checksum: f15ae4c2441b979a3b4fce78f3d83472fb22350c6dc3fd34bfe7c3da108e0b2360718734d961bba20e7716cb8578e964b870da55b035e209e50ec9db0378a3f7
  languageName: node
  linkType: hard

"@webpack-cli/configtest@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/configtest@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: 15d0ca835f2e16ec99e9f295f07b676435b9e706d7700df0ad088692fea065e34772fc44b96a4f6a86178b9ca8cf1ff941fbce15269587cf0925d70b18928cea
  languageName: node
  linkType: hard

"@webpack-cli/info@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/info@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  checksum: b8fba49fee10d297c2affb0b064c9a81e9038d75517c6728fb85f9fb254cae634e5d33e696dac5171e6944ae329d85fddac72f781c7d833f7e9dfe43151ce60d
  languageName: node
  linkType: hard

"@webpack-cli/serve@npm:^2.0.1":
  version: 2.0.1
  resolution: "@webpack-cli/serve@npm:2.0.1"
  peerDependencies:
    webpack: 5.x.x
    webpack-cli: 5.x.x
  peerDependenciesMeta:
    webpack-dev-server:
      optional: true
  checksum: 75c55f8398dd60e4821f81bec6e96287cebb3ab1837ef016779bc2f0c76a1d29c45b99e53daa99ba1fa156b5e2b61c19abf58098de20c2b58391b1f496ecc145
  languageName: node
  linkType: hard

"@xtuc/ieee754@npm:^1.2.0":
  version: 1.2.0
  resolution: "@xtuc/ieee754@npm:1.2.0"
  checksum: ac56d4ca6e17790f1b1677f978c0c6808b1900a5b138885d3da21732f62e30e8f0d9120fcf8f6edfff5100ca902b46f8dd7c1e3f903728634523981e80e2885a
  languageName: node
  linkType: hard

"@xtuc/long@npm:4.2.2":
  version: 4.2.2
  resolution: "@xtuc/long@npm:4.2.2"
  checksum: 8ed0d477ce3bc9c6fe2bf6a6a2cc316bb9c4127c5a7827bae947fa8ec34c7092395c5a283cc300c05b5fa01cbbfa1f938f410a7bf75db7c7846fea41949989ec
  languageName: node
  linkType: hard

"acorn-import-assertions@npm:^1.7.6":
  version: 1.8.0
  resolution: "acorn-import-assertions@npm:1.8.0"
  peerDependencies:
    acorn: ^8
  checksum: 5c4cf7c850102ba7ae0eeae0deb40fb3158c8ca5ff15c0bca43b5c47e307a1de3d8ef761788f881343680ea374631ae9e9615ba8876fee5268dbe068c98bcba6
  languageName: node
  linkType: hard

"acorn@npm:^8.5.0, acorn@npm:^8.7.1":
  version: 8.8.2
  resolution: "acorn@npm:8.8.2"
  bin:
    acorn: bin/acorn
  checksum: f790b99a1bf63ef160c967e23c46feea7787e531292bb827126334612c234ed489a0dc2c7ba33156416f0ffa8d25bf2b0fdb7f35c2ba60eb3e960572bece4001
  languageName: node
  linkType: hard

"ajv-keywords@npm:^3.5.2":
  version: 3.5.2
  resolution: "ajv-keywords@npm:3.5.2"
  peerDependencies:
    ajv: ^6.9.1
  checksum: 7dc5e5931677a680589050f79dcbe1fefbb8fea38a955af03724229139175b433c63c68f7ae5f86cf8f65d55eb7c25f75a046723e2e58296707617ca690feae9
  languageName: node
  linkType: hard

"ajv@npm:^6.12.5":
  version: 6.12.6
  resolution: "ajv@npm:6.12.6"
  dependencies:
    fast-deep-equal: ^3.1.1
    fast-json-stable-stringify: ^2.0.0
    json-schema-traverse: ^0.4.1
    uri-js: ^4.2.2
  checksum: 874972efe5c4202ab0a68379481fbd3d1b5d0a7bd6d3cc21d40d3536ebff3352a2a1fabb632d4fd2cc7fe4cbdcd5ed6782084c9bbf7f32a1536d18f9da5007d4
  languageName: node
  linkType: hard

"ansi-styles@npm:^4.1.0":
  version: 4.3.0
  resolution: "ansi-styles@npm:4.3.0"
  dependencies:
    color-convert: ^2.0.1
  checksum: 513b44c3b2105dd14cc42a19271e80f386466c4be574bccf60b627432f9198571ebf4ab1e4c3ba17347658f4ee1711c163d574248c0c1cdc2d5917a0ad582ec4
  languageName: node
  linkType: hard

"braces@npm:^3.0.2":
  version: 3.0.2
  resolution: "braces@npm:3.0.2"
  dependencies:
    fill-range: ^7.0.1
  checksum: e2a8e769a863f3d4ee887b5fe21f63193a891c68b612ddb4b68d82d1b5f3ff9073af066c343e9867a393fe4c2555dcb33e89b937195feb9c1613d259edfcd459
  languageName: node
  linkType: hard

"browserslist@npm:^4.14.5":
  version: 4.21.5
  resolution: "browserslist@npm:4.21.5"
  dependencies:
    caniuse-lite: ^1.0.30001449
    electron-to-chromium: ^1.4.284
    node-releases: ^2.0.8
    update-browserslist-db: ^1.0.10
  bin:
    browserslist: cli.js
  checksum: 9755986b22e73a6a1497fd8797aedd88e04270be33ce66ed5d85a1c8a798292a65e222b0f251bafa1c2522261e237d73b08b58689d4920a607e5a53d56dc4706
  languageName: node
  linkType: hard

"buffer-from@npm:^1.0.0":
  version: 1.1.2
  resolution: "buffer-from@npm:1.1.2"
  checksum: 0448524a562b37d4d7ed9efd91685a5b77a50672c556ea254ac9a6d30e3403a517d8981f10e565db24e8339413b43c97ca2951f10e399c6125a0d8911f5679bb
  languageName: node
  linkType: hard

"caniuse-lite@npm:^1.0.30001449":
  version: 1.0.30001458
  resolution: "caniuse-lite@npm:1.0.30001458"
  checksum: 258cc5a25babbbfe483bf788c6f321a19400ff80b2bf156b360bac09a6f9f4da44516350d187a30395667cb142c682d9ea96577ea0df236d35f76234b07ccb41
  languageName: node
  linkType: hard

"chalk@npm:^4.1.0":
  version: 4.1.2
  resolution: "chalk@npm:4.1.2"
  dependencies:
    ansi-styles: ^4.1.0
    supports-color: ^7.1.0
  checksum: fe75c9d5c76a7a98d45495b91b2172fa3b7a09e0cc9370e5c8feb1c567b85c4288e2b3fded7cfdd7359ac28d6b3844feb8b82b8686842e93d23c827c417e83fc
  languageName: node
  linkType: hard

"chrome-trace-event@npm:^1.0.2":
  version: 1.0.3
  resolution: "chrome-trace-event@npm:1.0.3"
  checksum: cb8b1fc7e881aaef973bd0c4a43cd353c2ad8323fb471a041e64f7c2dd849cde4aad15f8b753331a32dda45c973f032c8a03b8177fc85d60eaa75e91e08bfb97
  languageName: node
  linkType: hard

"clone-deep@npm:^4.0.1":
  version: 4.0.1
  resolution: "clone-deep@npm:4.0.1"
  dependencies:
    is-plain-object: ^2.0.4
    kind-of: ^6.0.2
    shallow-clone: ^3.0.0
  checksum: 770f912fe4e6f21873c8e8fbb1e99134db3b93da32df271d00589ea4a29dbe83a9808a322c93f3bcaf8584b8b4fa6fc269fc8032efbaa6728e0c9886c74467d2
  languageName: node
  linkType: hard

"clsx@npm:^1.1.1":
  version: 1.2.1
  resolution: "clsx@npm:1.2.1"
  checksum: 30befca8019b2eb7dbad38cff6266cf543091dae2825c856a62a8ccf2c3ab9c2907c4d12b288b73101196767f66812365400a227581484a05f968b0307cfaf12
  languageName: node
  linkType: hard

"color-convert@npm:^2.0.1":
  version: 2.0.1
  resolution: "color-convert@npm:2.0.1"
  dependencies:
    color-name: ~1.1.4
  checksum: 79e6bdb9fd479a205c71d89574fccfb22bd9053bd98c6c4d870d65c132e5e904e6034978e55b43d69fcaa7433af2016ee203ce76eeba9cfa554b373e7f7db336
  languageName: node
  linkType: hard

"color-name@npm:~1.1.4":
  version: 1.1.4
  resolution: "color-name@npm:1.1.4"
  checksum: b0445859521eb4021cd0fb0cc1a75cecf67fceecae89b63f62b201cca8d345baf8b952c966862a9d9a2632987d4f6581f0ec8d957dfacece86f0a7919316f610
  languageName: node
  linkType: hard

"colorette@npm:^2.0.14":
  version: 2.0.19
  resolution: "colorette@npm:2.0.19"
  checksum: 888cf5493f781e5fcf54ce4d49e9d7d698f96ea2b2ef67906834bb319a392c667f9ec69f4a10e268d2946d13a9503d2d19b3abaaaf174e3451bfe91fb9d82427
  languageName: node
  linkType: hard

"commander@npm:^2.20.0":
  version: 2.20.3
  resolution: "commander@npm:2.20.3"
  checksum: ab8c07884e42c3a8dbc5dd9592c606176c7eb5c1ca5ff274bcf907039b2c41de3626f684ea75ccf4d361ba004bbaff1f577d5384c155f3871e456bdf27becf9e
  languageName: node
  linkType: hard

"commander@npm:^9.4.1":
  version: 9.5.0
  resolution: "commander@npm:9.5.0"
  checksum: c7a3e27aa59e913b54a1bafd366b88650bc41d6651f0cbe258d4ff09d43d6a7394232a4dadd0bf518b3e696fdf595db1028a0d82c785b88bd61f8a440cecfade
  languageName: node
  linkType: hard

"cross-spawn@npm:^7.0.3":
  version: 7.0.3
  resolution: "cross-spawn@npm:7.0.3"
  dependencies:
    path-key: ^3.1.0
    shebang-command: ^2.0.0
    which: ^2.0.1
  checksum: 671cc7c7288c3a8406f3c69a3ae2fc85555c04169e9d611def9a675635472614f1c0ed0ef80955d5b6d4e724f6ced67f0ad1bb006c2ea643488fcfef994d7f52
  languageName: node
  linkType: hard

"csstype@npm:^3.0.2":
  version: 3.1.1
  resolution: "csstype@npm:3.1.1"
  checksum: 1f7b4f5fdd955b7444b18ebdddf3f5c699159f13e9cf8ac9027ae4a60ae226aef9bbb14a6e12ca7dba3358b007cee6354b116e720262867c398de6c955ea451d
  languageName: node
  linkType: hard

"electron-to-chromium@npm:^1.4.284":
  version: 1.4.313
  resolution: "electron-to-chromium@npm:1.4.313"
  checksum: 694c379e0800e55e3e2966f73557bfc3bad66e1eb120928d33e8f0619b59ec057eb94ecb27a06e6f7ea1892565f6b8a87b03129a8243baa0d1ccea9579040a83
  languageName: node
  linkType: hard

"enhanced-resolve@npm:^5.0.0, enhanced-resolve@npm:^5.10.0":
  version: 5.12.0
  resolution: "enhanced-resolve@npm:5.12.0"
  dependencies:
    graceful-fs: ^4.2.4
    tapable: ^2.2.0
  checksum: bf3f787facaf4ce3439bef59d148646344e372bef5557f0d37ea8aa02c51f50a925cd1f07b8d338f18992c29f544ec235a8c64bcdb56030196c48832a5494174
  languageName: node
  linkType: hard

"envinfo@npm:^7.7.3":
  version: 7.8.1
  resolution: "envinfo@npm:7.8.1"
  bin:
    envinfo: dist/cli.js
  checksum: de736c98d6311c78523628ff127af138451b162e57af5293c1b984ca821d0aeb9c849537d2fde0434011bed33f6bca5310ca2aab8a51a3f28fc719e89045d648
  languageName: node
  linkType: hard

"es-module-lexer@npm:^0.9.0":
  version: 0.9.3
  resolution: "es-module-lexer@npm:0.9.3"
  checksum: 84bbab23c396281db2c906c766af58b1ae2a1a2599844a504df10b9e8dc77ec800b3211fdaa133ff700f5703d791198807bba25d9667392d27a5e9feda344da8
  languageName: node
  linkType: hard

"escalade@npm:^3.1.1":
  version: 3.1.1
  resolution: "escalade@npm:3.1.1"
  checksum: a3e2a99f07acb74b3ad4989c48ca0c3140f69f923e56d0cba0526240ee470b91010f9d39001f2a4a313841d237ede70a729e92125191ba5d21e74b106800b133
  languageName: node
  linkType: hard

"eslint-scope@npm:5.1.1":
  version: 5.1.1
  resolution: "eslint-scope@npm:5.1.1"
  dependencies:
    esrecurse: ^4.3.0
    estraverse: ^4.1.1
  checksum: 47e4b6a3f0cc29c7feedee6c67b225a2da7e155802c6ea13bbef4ac6b9e10c66cd2dcb987867ef176292bf4e64eccc680a49e35e9e9c669f4a02bac17e86abdb
  languageName: node
  linkType: hard

"esrecurse@npm:^4.3.0":
  version: 4.3.0
  resolution: "esrecurse@npm:4.3.0"
  dependencies:
    estraverse: ^5.2.0
  checksum: ebc17b1a33c51cef46fdc28b958994b1dc43cd2e86237515cbc3b4e5d2be6a811b2315d0a1a4d9d340b6d2308b15322f5c8291059521cc5f4802f65e7ec32837
  languageName: node
  linkType: hard

"estraverse@npm:^4.1.1":
  version: 4.3.0
  resolution: "estraverse@npm:4.3.0"
  checksum: a6299491f9940bb246124a8d44b7b7a413a8336f5436f9837aaa9330209bd9ee8af7e91a654a3545aee9c54b3308e78ee360cef1d777d37cfef77d2fa33b5827
  languageName: node
  linkType: hard

"estraverse@npm:^5.2.0":
  version: 5.3.0
  resolution: "estraverse@npm:5.3.0"
  checksum: 072780882dc8416ad144f8fe199628d2b3e7bbc9989d9ed43795d2c90309a2047e6bc5979d7e2322a341163d22cfad9e21f4110597fe487519697389497e4e2b
  languageName: node
  linkType: hard

"events@npm:^3.2.0":
  version: 3.3.0
  resolution: "events@npm:3.3.0"
  checksum: f6f487ad2198aa41d878fa31452f1a3c00958f46e9019286ff4787c84aac329332ab45c9cdc8c445928fc6d7ded294b9e005a7fce9426488518017831b272780
  languageName: node
  linkType: hard

"fast-deep-equal@npm:^3.1.1":
  version: 3.1.3
  resolution: "fast-deep-equal@npm:3.1.3"
  checksum: e21a9d8d84f53493b6aa15efc9cfd53dd5b714a1f23f67fb5dc8f574af80df889b3bce25dc081887c6d25457cce704e636395333abad896ccdec03abaf1f3f9d
  languageName: node
  linkType: hard

"fast-json-stable-stringify@npm:^2.0.0":
  version: 2.1.0
  resolution: "fast-json-stable-stringify@npm:2.1.0"
  checksum: b191531e36c607977e5b1c47811158733c34ccb3bfde92c44798929e9b4154884378536d26ad90dfecd32e1ffc09c545d23535ad91b3161a27ddbb8ebe0cbecb
  languageName: node
  linkType: hard

"fastest-levenshtein@npm:^1.0.12":
  version: 1.0.16
  resolution: "fastest-levenshtein@npm:1.0.16"
  checksum: a78d44285c9e2ae2c25f3ef0f8a73f332c1247b7ea7fb4a191e6bb51aa6ee1ef0dfb3ed113616dcdc7023e18e35a8db41f61c8d88988e877cf510df8edafbc71
  languageName: node
  linkType: hard

"fill-range@npm:^7.0.1":
  version: 7.0.1
  resolution: "fill-range@npm:7.0.1"
  dependencies:
    to-regex-range: ^5.0.1
  checksum: cc283f4e65b504259e64fd969bcf4def4eb08d85565e906b7d36516e87819db52029a76b6363d0f02d0d532f0033c9603b9e2d943d56ee3b0d4f7ad3328ff917
  languageName: node
  linkType: hard

"find-up@npm:^4.0.0":
  version: 4.1.0
  resolution: "find-up@npm:4.1.0"
  dependencies:
    locate-path: ^5.0.0
    path-exists: ^4.0.0
  checksum: 4c172680e8f8c1f78839486e14a43ef82e9decd0e74145f40707cc42e7420506d5ec92d9a11c22bd2c48fb0c384ea05dd30e10dd152fefeec6f2f75282a8b844
  languageName: node
  linkType: hard

"function-bind@npm:^1.1.1":
  version: 1.1.1
  resolution: "function-bind@npm:1.1.1"
  checksum: b32fbaebb3f8ec4969f033073b43f5c8befbb58f1a79e12f1d7490358150359ebd92f49e72ff0144f65f2c48ea2a605bff2d07965f548f6474fd8efd95bf361a
  languageName: node
  linkType: hard

"glob-to-regexp@npm:^0.4.1":
  version: 0.4.1
  resolution: "glob-to-regexp@npm:0.4.1"
  checksum: e795f4e8f06d2a15e86f76e4d92751cf8bbfcf0157cea5c2f0f35678a8195a750b34096b1256e436f0cebc1883b5ff0888c47348443e69546a5a87f9e1eb1167
  languageName: node
  linkType: hard

"graceful-fs@npm:^4.1.2, graceful-fs@npm:^4.2.4, graceful-fs@npm:^4.2.9":
  version: 4.2.10
  resolution: "graceful-fs@npm:4.2.10"
  checksum: 3f109d70ae123951905d85032ebeae3c2a5a7a997430df00ea30df0e3a6c60cf6689b109654d6fdacd28810a053348c4d14642da1d075049e6be1ba5216218da
  languageName: node
  linkType: hard

"has-flag@npm:^4.0.0":
  version: 4.0.0
  resolution: "has-flag@npm:4.0.0"
  checksum: 261a1357037ead75e338156b1f9452c016a37dcd3283a972a30d9e4a87441ba372c8b81f818cd0fbcd9c0354b4ae7e18b9e1afa1971164aef6d18c2b6095a8ad
  languageName: node
  linkType: hard

"has@npm:^1.0.3":
  version: 1.0.3
  resolution: "has@npm:1.0.3"
  dependencies:
    function-bind: ^1.1.1
  checksum: b9ad53d53be4af90ce5d1c38331e712522417d017d5ef1ebd0507e07c2fbad8686fffb8e12ddecd4c39ca9b9b47431afbb975b8abf7f3c3b82c98e9aad052792
  languageName: node
  linkType: hard

"import-local@npm:^3.0.2":
  version: 3.1.0
  resolution: "import-local@npm:3.1.0"
  dependencies:
    pkg-dir: ^4.2.0
    resolve-cwd: ^3.0.0
  bin:
    import-local-fixture: fixtures/cli.js
  checksum: bfcdb63b5e3c0e245e347f3107564035b128a414c4da1172a20dc67db2504e05ede4ac2eee1252359f78b0bfd7b19ef180aec427c2fce6493ae782d73a04cddd
  languageName: node
  linkType: hard

"interpret@npm:^3.1.1":
  version: 3.1.1
  resolution: "interpret@npm:3.1.1"
  checksum: 35cebcf48c7351130437596d9ab8c8fe131ce4038da4561e6d665f25640e0034702a031cf7e3a5cea60ac7ac548bf17465e0571ede126f3d3a6933152171ac82
  languageName: node
  linkType: hard

"is-core-module@npm:^2.9.0":
  version: 2.11.0
  resolution: "is-core-module@npm:2.11.0"
  dependencies:
    has: ^1.0.3
  checksum: f96fd490c6b48eb4f6d10ba815c6ef13f410b0ba6f7eb8577af51697de523e5f2cd9de1c441b51d27251bf0e4aebc936545e33a5d26d5d51f28d25698d4a8bab
  languageName: node
  linkType: hard

"is-number@npm:^7.0.0":
  version: 7.0.0
  resolution: "is-number@npm:7.0.0"
  checksum: 456ac6f8e0f3111ed34668a624e45315201dff921e5ac181f8ec24923b99e9f32ca1a194912dc79d539c97d33dba17dc635202ff0b2cf98326f608323276d27a
  languageName: node
  linkType: hard

"is-plain-object@npm:^2.0.4":
  version: 2.0.4
  resolution: "is-plain-object@npm:2.0.4"
  dependencies:
    isobject: ^3.0.1
  checksum: 2a401140cfd86cabe25214956ae2cfee6fbd8186809555cd0e84574f88de7b17abacb2e477a6a658fa54c6083ecbda1e6ae404c7720244cd198903848fca70ca
  languageName: node
  linkType: hard

"isexe@npm:^2.0.0":
  version: 2.0.0
  resolution: "isexe@npm:2.0.0"
  checksum: 26bf6c5480dda5161c820c5b5c751ae1e766c587b1f951ea3fcfc973bafb7831ae5b54a31a69bd670220e42e99ec154475025a468eae58ea262f813fdc8d1c62
  languageName: node
  linkType: hard

"isobject@npm:^3.0.1":
  version: 3.0.1
  resolution: "isobject@npm:3.0.1"
  checksum: db85c4c970ce30693676487cca0e61da2ca34e8d4967c2e1309143ff910c207133a969f9e4ddb2dc6aba670aabce4e0e307146c310350b298e74a31f7d464703
  languageName: node
  linkType: hard

"jest-worker@npm:^27.4.5":
  version: 27.5.1
  resolution: "jest-worker@npm:27.5.1"
  dependencies:
    "@types/node": "*"
    merge-stream: ^2.0.0
    supports-color: ^8.0.0
  checksum: 98cd68b696781caed61c983a3ee30bf880b5bd021c01d98f47b143d4362b85d0737f8523761e2713d45e18b4f9a2b98af1eaee77afade4111bb65c77d6f7c980
  languageName: node
  linkType: hard

"js-tokens@npm:^3.0.0 || ^4.0.0":
  version: 4.0.0
  resolution: "js-tokens@npm:4.0.0"
  checksum: 8a95213a5a77deb6cbe94d86340e8d9ace2b93bc367790b260101d2f36a2eaf4e4e22d9fa9cf459b38af3a32fb4190e638024cf82ec95ef708680e405ea7cc78
  languageName: node
  linkType: hard

"json-parse-even-better-errors@npm:^2.3.1":
  version: 2.3.1
  resolution: "json-parse-even-better-errors@npm:2.3.1"
  checksum: 798ed4cf3354a2d9ccd78e86d2169515a0097a5c133337807cdf7f1fc32e1391d207ccfc276518cc1d7d8d4db93288b8a50ba4293d212ad1336e52a8ec0a941f
  languageName: node
  linkType: hard

"json-schema-traverse@npm:^0.4.1":
  version: 0.4.1
  resolution: "json-schema-traverse@npm:0.4.1"
  checksum: 7486074d3ba247769fda17d5181b345c9fb7d12e0da98b22d1d71a5db9698d8b4bd900a3ec1a4ffdd60846fc2556274a5c894d0c48795f14cb03aeae7b55260b
  languageName: node
  linkType: hard

"kind-of@npm:^6.0.2":
  version: 6.0.3
  resolution: "kind-of@npm:6.0.3"
  checksum: 3ab01e7b1d440b22fe4c31f23d8d38b4d9b91d9f291df683476576493d5dfd2e03848a8b05813dd0c3f0e835bc63f433007ddeceb71f05cb25c45ae1b19c6d3b
  languageName: node
  linkType: hard

"loader-runner@npm:^4.2.0":
  version: 4.3.0
  resolution: "loader-runner@npm:4.3.0"
  checksum: a90e00dee9a16be118ea43fec3192d0b491fe03a32ed48a4132eb61d498f5536a03a1315531c19d284392a8726a4ecad71d82044c28d7f22ef62e029bf761569
  languageName: node
  linkType: hard

"locate-path@npm:^5.0.0":
  version: 5.0.0
  resolution: "locate-path@npm:5.0.0"
  dependencies:
    p-locate: ^4.1.0
  checksum: 83e51725e67517287d73e1ded92b28602e3ae5580b301fe54bfb76c0c723e3f285b19252e375712316774cf52006cb236aed5704692c32db0d5d089b69696e30
  languageName: node
  linkType: hard

"lodash@npm:^4.17.15":
  version: 4.17.21
  resolution: "lodash@npm:4.17.21"
  checksum: eb835a2e51d381e561e508ce932ea50a8e5a68f4ebdd771ea240d3048244a8d13658acbd502cd4829768c56f2e16bdd4340b9ea141297d472517b83868e677f7
  languageName: node
  linkType: hard

"loose-envify@npm:^1.1.0":
  version: 1.4.0
  resolution: "loose-envify@npm:1.4.0"
  dependencies:
    js-tokens: ^3.0.0 || ^4.0.0
  bin:
    loose-envify: cli.js
  checksum: 6517e24e0cad87ec9888f500c5b5947032cdfe6ef65e1c1936a0c48a524b81e65542c9c3edc91c97d5bddc806ee2a985dbc79be89215d613b1de5db6d1cfe6f4
  languageName: node
  linkType: hard

"lru-cache@npm:^6.0.0":
  version: 6.0.0
  resolution: "lru-cache@npm:6.0.0"
  dependencies:
    yallist: ^4.0.0
  checksum: f97f499f898f23e4585742138a22f22526254fdba6d75d41a1c2526b3b6cc5747ef59c5612ba7375f42aca4f8461950e925ba08c991ead0651b4918b7c978297
  languageName: node
  linkType: hard

"merge-stream@npm:^2.0.0":
  version: 2.0.0
  resolution: "merge-stream@npm:2.0.0"
  checksum: 6fa4dcc8d86629705cea944a4b88ef4cb0e07656ebf223fa287443256414283dd25d91c1cd84c77987f2aec5927af1a9db6085757cb43d90eb170ebf4b47f4f4
  languageName: node
  linkType: hard

"micromatch@npm:^4.0.0":
  version: 4.0.5
  resolution: "micromatch@npm:4.0.5"
  dependencies:
    braces: ^3.0.2
    picomatch: ^2.3.1
  checksum: 02a17b671c06e8fefeeb6ef996119c1e597c942e632a21ef589154f23898c9c6a9858526246abb14f8bca6e77734aa9dcf65476fca47cedfb80d9577d52843fc
  languageName: node
  linkType: hard

"mime-db@npm:1.52.0":
  version: 1.52.0
  resolution: "mime-db@npm:1.52.0"
  checksum: 0d99a03585f8b39d68182803b12ac601d9c01abfa28ec56204fa330bc9f3d1c5e14beb049bafadb3dbdf646dfb94b87e24d4ec7b31b7279ef906a8ea9b6a513f
  languageName: node
  linkType: hard

"mime-types@npm:^2.1.27":
  version: 2.1.35
  resolution: "mime-types@npm:2.1.35"
  dependencies:
    mime-db: 1.52.0
  checksum: 89a5b7f1def9f3af5dad6496c5ed50191ae4331cc5389d7c521c8ad28d5fdad2d06fd81baf38fed813dc4e46bb55c8145bb0ff406330818c9cf712fb2e9b3836
  languageName: node
  linkType: hard

"neo-async@npm:^2.6.2":
  version: 2.6.2
  resolution: "neo-async@npm:2.6.2"
  checksum: deac9f8d00eda7b2e5cd1b2549e26e10a0faa70adaa6fdadca701cc55f49ee9018e427f424bac0c790b7c7e2d3068db97f3093f1093975f2acb8f8818b936ed9
  languageName: node
  linkType: hard

"node-releases@npm:^2.0.8":
  version: 2.0.10
  resolution: "node-releases@npm:2.0.10"
  checksum: d784ecde25696a15d449c4433077f5cce620ed30a1656c4abf31282bfc691a70d9618bae6868d247a67914d1be5cc4fde22f65a05f4398cdfb92e0fc83cadfbc
  languageName: node
  linkType: hard

"object-assign@npm:^4.1.1":
  version: 4.1.1
  resolution: "object-assign@npm:4.1.1"
  checksum: fcc6e4ea8c7fe48abfbb552578b1c53e0d194086e2e6bbbf59e0a536381a292f39943c6e9628af05b5528aa5e3318bb30d6b2e53cadaf5b8fe9e12c4b69af23f
  languageName: node
  linkType: hard

"onboarding@workspace:.":
  version: 0.0.0-use.local
  resolution: "onboarding@workspace:."
  dependencies:
    "@gx-design/onboarding": ^2.1.6
    "@pepita/babelfish": ^1.8.5
    "@types/node": ^17.0.2
    "@types/react": ^17.0.2
    "@types/react-dom": ^17.0.2
    react: ^17.0.2
    react-dom: ^17.0.2
    ts-loader: ^9.4.2
    typescript: ^4.9.5
    webpack: ^5.75.0
    webpack-cli: ^5.0.1
    webpack-merge: ^4.1.4
  languageName: unknown
  linkType: soft

"p-limit@npm:^2.2.0":
  version: 2.3.0
  resolution: "p-limit@npm:2.3.0"
  dependencies:
    p-try: ^2.0.0
  checksum: 84ff17f1a38126c3314e91ecfe56aecbf36430940e2873dadaa773ffe072dc23b7af8e46d4b6485d302a11673fe94c6b67ca2cfbb60c989848b02100d0594ac1
  languageName: node
  linkType: hard

"p-locate@npm:^4.1.0":
  version: 4.1.0
  resolution: "p-locate@npm:4.1.0"
  dependencies:
    p-limit: ^2.2.0
  checksum: 513bd14a455f5da4ebfcb819ef706c54adb09097703de6aeaa5d26fe5ea16df92b48d1ac45e01e3944ce1e6aa2a66f7f8894742b8c9d6e276e16cd2049a2b870
  languageName: node
  linkType: hard

"p-try@npm:^2.0.0":
  version: 2.2.0
  resolution: "p-try@npm:2.2.0"
  checksum: f8a8e9a7693659383f06aec604ad5ead237c7a261c18048a6e1b5b85a5f8a067e469aa24f5bc009b991ea3b058a87f5065ef4176793a200d4917349881216cae
  languageName: node
  linkType: hard

"path-exists@npm:^4.0.0":
  version: 4.0.0
  resolution: "path-exists@npm:4.0.0"
  checksum: 505807199dfb7c50737b057dd8d351b82c033029ab94cb10a657609e00c1bc53b951cfdbccab8de04c5584d5eff31128ce6afd3db79281874a5ef2adbba55ed1
  languageName: node
  linkType: hard

"path-key@npm:^3.1.0":
  version: 3.1.1
  resolution: "path-key@npm:3.1.1"
  checksum: 55cd7a9dd4b343412a8386a743f9c746ef196e57c823d90ca3ab917f90ab9f13dd0ded27252ba49dbdfcab2b091d998bc446f6220cd3cea65db407502a740020
  languageName: node
  linkType: hard

"path-parse@npm:^1.0.7":
  version: 1.0.7
  resolution: "path-parse@npm:1.0.7"
  checksum: 49abf3d81115642938a8700ec580da6e830dde670be21893c62f4e10bd7dd4c3742ddc603fe24f898cba7eb0c6bc1777f8d9ac14185d34540c6d4d80cd9cae8a
  languageName: node
  linkType: hard

"picocolors@npm:^1.0.0":
  version: 1.0.0
  resolution: "picocolors@npm:1.0.0"
  checksum: a2e8092dd86c8396bdba9f2b5481032848525b3dc295ce9b57896f931e63fc16f79805144321f72976383fc249584672a75cc18d6777c6b757603f372f745981
  languageName: node
  linkType: hard

"picomatch@npm:^2.3.1":
  version: 2.3.1
  resolution: "picomatch@npm:2.3.1"
  checksum: 050c865ce81119c4822c45d3c84f1ced46f93a0126febae20737bd05ca20589c564d6e9226977df859ed5e03dc73f02584a2b0faad36e896936238238b0446cf
  languageName: node
  linkType: hard

"pkg-dir@npm:^4.2.0":
  version: 4.2.0
  resolution: "pkg-dir@npm:4.2.0"
  dependencies:
    find-up: ^4.0.0
  checksum: 9863e3f35132bf99ae1636d31ff1e1e3501251d480336edb1c211133c8d58906bed80f154a1d723652df1fda91e01c7442c2eeaf9dc83157c7ae89087e43c8d6
  languageName: node
  linkType: hard

"punycode@npm:^2.1.0":
  version: 2.3.0
  resolution: "punycode@npm:2.3.0"
  checksum: 39f760e09a2a3bbfe8f5287cf733ecdad69d6af2fe6f97ca95f24b8921858b91e9ea3c9eeec6e08cede96181b3bb33f95c6ffd8c77e63986508aa2e8159fa200
  languageName: node
  linkType: hard

"randombytes@npm:^2.1.0":
  version: 2.1.0
  resolution: "randombytes@npm:2.1.0"
  dependencies:
    safe-buffer: ^5.1.0
  checksum: d779499376bd4cbb435ef3ab9a957006c8682f343f14089ed5f27764e4645114196e75b7f6abf1cbd84fd247c0cb0651698444df8c9bf30e62120fbbc52269d6
  languageName: node
  linkType: hard

"react-dom@npm:^17.0.2":
  version: 17.0.2
  resolution: "react-dom@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
    scheduler: ^0.20.2
  peerDependencies:
    react: 17.0.2
  checksum: 1c1eaa3bca7c7228d24b70932e3d7c99e70d1d04e13bb0843bbf321582bc25d7961d6b8a6978a58a598af2af496d1cedcfb1bf65f6b0960a0a8161cb8dab743c
  languageName: node
  linkType: hard

"react@npm:^17.0.2":
  version: 17.0.2
  resolution: "react@npm:17.0.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: b254cc17ce3011788330f7bbf383ab653c6848902d7936a87b09d835d091e3f295f7e9dd1597c6daac5dc80f90e778c8230218ba8ad599f74adcc11e33b9d61b
  languageName: node
  linkType: hard

"rechoir@npm:^0.8.0":
  version: 0.8.0
  resolution: "rechoir@npm:0.8.0"
  dependencies:
    resolve: ^1.20.0
  checksum: ad3caed8afdefbc33fbc30e6d22b86c35b3d51c2005546f4e79bcc03c074df804b3640ad18945e6bef9ed12caedc035655ec1082f64a5e94c849ff939dc0a788
  languageName: node
  linkType: hard

"resolve-cwd@npm:^3.0.0":
  version: 3.0.0
  resolution: "resolve-cwd@npm:3.0.0"
  dependencies:
    resolve-from: ^5.0.0
  checksum: 546e0816012d65778e580ad62b29e975a642989108d9a3c5beabfb2304192fa3c9f9146fbdfe213563c6ff51975ae41bac1d3c6e047dd9572c94863a057b4d81
  languageName: node
  linkType: hard

"resolve-from@npm:^5.0.0":
  version: 5.0.0
  resolution: "resolve-from@npm:5.0.0"
  checksum: 4ceeb9113e1b1372d0cd969f3468fa042daa1dd9527b1b6bb88acb6ab55d8b9cd65dbf18819f9f9ddf0db804990901dcdaade80a215e7b2c23daae38e64f5bdf
  languageName: node
  linkType: hard

"resolve@npm:^1.20.0":
  version: 1.22.1
  resolution: "resolve@npm:1.22.1"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 07af5fc1e81aa1d866cbc9e9460fbb67318a10fa3c4deadc35c3ad8a898ee9a71a86a65e4755ac3195e0ea0cfbe201eb323ebe655ce90526fd61917313a34e4e
  languageName: node
  linkType: hard

"resolve@patch:resolve@^1.20.0#~builtin<compat/resolve>":
  version: 1.22.1
  resolution: "resolve@patch:resolve@npm%3A1.22.1#~builtin<compat/resolve>::version=1.22.1&hash=c3c19d"
  dependencies:
    is-core-module: ^2.9.0
    path-parse: ^1.0.7
    supports-preserve-symlinks-flag: ^1.0.0
  bin:
    resolve: bin/resolve
  checksum: 5656f4d0bedcf8eb52685c1abdf8fbe73a1603bb1160a24d716e27a57f6cecbe2432ff9c89c2bd57542c3a7b9d14b1882b73bfe2e9d7849c9a4c0b8b39f02b8b
  languageName: node
  linkType: hard

"safe-buffer@npm:^5.1.0":
  version: 5.2.1
  resolution: "safe-buffer@npm:5.2.1"
  checksum: b99c4b41fdd67a6aaf280fcd05e9ffb0813654894223afb78a31f14a19ad220bba8aba1cb14eddce1fcfb037155fe6de4e861784eb434f7d11ed58d1e70dd491
  languageName: node
  linkType: hard

"scheduler@npm:^0.20.2":
  version: 0.20.2
  resolution: "scheduler@npm:0.20.2"
  dependencies:
    loose-envify: ^1.1.0
    object-assign: ^4.1.1
  checksum: c4b35cf967c8f0d3e65753252d0f260271f81a81e427241295c5a7b783abf4ea9e905f22f815ab66676f5313be0a25f47be582254db8f9241b259213e999b8fc
  languageName: node
  linkType: hard

"schema-utils@npm:^3.1.0, schema-utils@npm:^3.1.1":
  version: 3.1.1
  resolution: "schema-utils@npm:3.1.1"
  dependencies:
    "@types/json-schema": ^7.0.8
    ajv: ^6.12.5
    ajv-keywords: ^3.5.2
  checksum: fb73f3d759d43ba033c877628fe9751620a26879f6301d3dbeeb48cf2a65baec5cdf99da65d1bf3b4ff5444b2e59cbe4f81c2456b5e0d2ba7d7fd4aed5da29ce
  languageName: node
  linkType: hard

"semver@npm:^7.3.4":
  version: 7.3.8
  resolution: "semver@npm:7.3.8"
  dependencies:
    lru-cache: ^6.0.0
  bin:
    semver: bin/semver.js
  checksum: ba9c7cbbf2b7884696523450a61fee1a09930d888b7a8d7579025ad93d459b2d1949ee5bbfeb188b2be5f4ac163544c5e98491ad6152df34154feebc2cc337c1
  languageName: node
  linkType: hard

"serialize-javascript@npm:^6.0.0":
  version: 6.0.1
  resolution: "serialize-javascript@npm:6.0.1"
  dependencies:
    randombytes: ^2.1.0
  checksum: 3c4f4cb61d0893b988415bdb67243637333f3f574e9e9cc9a006a2ced0b390b0b3b44aef8d51c951272a9002ec50885eefdc0298891bc27eb2fe7510ea87dc4f
  languageName: node
  linkType: hard

"shallow-clone@npm:^3.0.0":
  version: 3.0.1
  resolution: "shallow-clone@npm:3.0.1"
  dependencies:
    kind-of: ^6.0.2
  checksum: 39b3dd9630a774aba288a680e7d2901f5c0eae7b8387fc5c8ea559918b29b3da144b7bdb990d7ccd9e11be05508ac9e459ce51d01fd65e583282f6ffafcba2e7
  languageName: node
  linkType: hard

"shebang-command@npm:^2.0.0":
  version: 2.0.0
  resolution: "shebang-command@npm:2.0.0"
  dependencies:
    shebang-regex: ^3.0.0
  checksum: 6b52fe87271c12968f6a054e60f6bde5f0f3d2db483a1e5c3e12d657c488a15474121a1d55cd958f6df026a54374ec38a4a963988c213b7570e1d51575cea7fa
  languageName: node
  linkType: hard

"shebang-regex@npm:^3.0.0":
  version: 3.0.0
  resolution: "shebang-regex@npm:3.0.0"
  checksum: 1a2bcae50de99034fcd92ad4212d8e01eedf52c7ec7830eedcf886622804fe36884278f2be8be0ea5fde3fd1c23911643a4e0f726c8685b61871c8908af01222
  languageName: node
  linkType: hard

"source-map-support@npm:~0.5.20":
  version: 0.5.21
  resolution: "source-map-support@npm:0.5.21"
  dependencies:
    buffer-from: ^1.0.0
    source-map: ^0.6.0
  checksum: 43e98d700d79af1d36f859bdb7318e601dfc918c7ba2e98456118ebc4c4872b327773e5a1df09b0524e9e5063bb18f0934538eace60cca2710d1fa687645d137
  languageName: node
  linkType: hard

"source-map@npm:^0.6.0":
  version: 0.6.1
  resolution: "source-map@npm:0.6.1"
  checksum: 59ce8640cf3f3124f64ac289012c2b8bd377c238e316fb323ea22fbfe83da07d81e000071d7242cad7a23cd91c7de98e4df8830ec3f133cb6133a5f6e9f67bc2
  languageName: node
  linkType: hard

"supports-color@npm:^7.1.0":
  version: 7.2.0
  resolution: "supports-color@npm:7.2.0"
  dependencies:
    has-flag: ^4.0.0
  checksum: 3dda818de06ebbe5b9653e07842d9479f3555ebc77e9a0280caf5a14fb877ffee9ed57007c3b78f5a6324b8dbeec648d9e97a24e2ed9fdb81ddc69ea07100f4a
  languageName: node
  linkType: hard

"supports-color@npm:^8.0.0":
  version: 8.1.1
  resolution: "supports-color@npm:8.1.1"
  dependencies:
    has-flag: ^4.0.0
  checksum: c052193a7e43c6cdc741eb7f378df605636e01ad434badf7324f17fb60c69a880d8d8fcdcb562cf94c2350e57b937d7425ab5b8326c67c2adc48f7c87c1db406
  languageName: node
  linkType: hard

"supports-preserve-symlinks-flag@npm:^1.0.0":
  version: 1.0.0
  resolution: "supports-preserve-symlinks-flag@npm:1.0.0"
  checksum: 53b1e247e68e05db7b3808b99b892bd36fb096e6fba213a06da7fab22045e97597db425c724f2bbd6c99a3c295e1e73f3e4de78592289f38431049e1277ca0ae
  languageName: node
  linkType: hard

"tapable@npm:^2.1.1, tapable@npm:^2.2.0":
  version: 2.2.1
  resolution: "tapable@npm:2.2.1"
  checksum: 3b7a1b4d86fa940aad46d9e73d1e8739335efd4c48322cb37d073eb6f80f5281889bf0320c6d8ffcfa1a0dd5bfdbd0f9d037e252ef972aca595330538aac4d51
  languageName: node
  linkType: hard

"terser-webpack-plugin@npm:^5.1.3":
  version: 5.3.6
  resolution: "terser-webpack-plugin@npm:5.3.6"
  dependencies:
    "@jridgewell/trace-mapping": ^0.3.14
    jest-worker: ^27.4.5
    schema-utils: ^3.1.1
    serialize-javascript: ^6.0.0
    terser: ^5.14.1
  peerDependencies:
    webpack: ^5.1.0
  peerDependenciesMeta:
    "@swc/core":
      optional: true
    esbuild:
      optional: true
    uglify-js:
      optional: true
  checksum: 8f3448d7fdb0434ce6a0c09d95c462bfd2f4a5a430233d854163337f734a7f5c07c74513d16081e06d4ca33d366d5b1a36f5444219bc41a7403afd6162107bad
  languageName: node
  linkType: hard

"terser@npm:^5.14.1":
  version: 5.16.5
  resolution: "terser@npm:5.16.5"
  dependencies:
    "@jridgewell/source-map": ^0.3.2
    acorn: ^8.5.0
    commander: ^2.20.0
    source-map-support: ~0.5.20
  bin:
    terser: bin/terser
  checksum: f2c1a087fac7f4ff04b1b4e79bffc52e2fc0b068b98912bfcc0b341184c284c30c19ed73f76ac92b225b71668f7f8fc586d99a7e50a29cdc1c916cb1265522ec
  languageName: node
  linkType: hard

"to-regex-range@npm:^5.0.1":
  version: 5.0.1
  resolution: "to-regex-range@npm:5.0.1"
  dependencies:
    is-number: ^7.0.0
  checksum: f76fa01b3d5be85db6a2a143e24df9f60dd047d151062d0ba3df62953f2f697b16fe5dad9b0ac6191c7efc7b1d9dcaa4b768174b7b29da89d4428e64bc0a20ed
  languageName: node
  linkType: hard

"ts-loader@npm:^9.4.2":
  version: 9.4.2
  resolution: "ts-loader@npm:9.4.2"
  dependencies:
    chalk: ^4.1.0
    enhanced-resolve: ^5.0.0
    micromatch: ^4.0.0
    semver: ^7.3.4
  peerDependencies:
    typescript: "*"
    webpack: ^5.0.0
  checksum: 6f306ee4c615c2a159fb177561e3fb86ca2cbd6c641e710d408a64b4978e1ff3f2c9733df07bff27d3f82efbfa7c287523d4306049510c7485ac2669a9c37eb0
  languageName: node
  linkType: hard

"typescript@npm:^4.9.5":
  version: 4.9.5
  resolution: "typescript@npm:4.9.5"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: ee000bc26848147ad423b581bd250075662a354d84f0e06eb76d3b892328d8d4440b7487b5a83e851b12b255f55d71835b008a66cbf8f255a11e4400159237db
  languageName: node
  linkType: hard

"typescript@patch:typescript@^4.9.5#~builtin<compat/typescript>":
  version: 4.9.5
  resolution: "typescript@patch:typescript@npm%3A4.9.5#~builtin<compat/typescript>::version=4.9.5&hash=23ec76"
  bin:
    tsc: bin/tsc
    tsserver: bin/tsserver
  checksum: ab417a2f398380c90a6cf5a5f74badd17866adf57f1165617d6a551f059c3ba0a3e4da0d147b3ac5681db9ac76a303c5876394b13b3de75fdd5b1eaa06181c9d
  languageName: node
  linkType: hard

"update-browserslist-db@npm:^1.0.10":
  version: 1.0.10
  resolution: "update-browserslist-db@npm:1.0.10"
  dependencies:
    escalade: ^3.1.1
    picocolors: ^1.0.0
  peerDependencies:
    browserslist: ">= 4.21.0"
  bin:
    browserslist-lint: cli.js
  checksum: 12db73b4f63029ac407b153732e7cd69a1ea8206c9100b482b7d12859cd3cd0bc59c602d7ae31e652706189f1acb90d42c53ab24a5ba563ed13aebdddc5561a0
  languageName: node
  linkType: hard

"uri-js@npm:^4.2.2":
  version: 4.4.1
  resolution: "uri-js@npm:4.4.1"
  dependencies:
    punycode: ^2.1.0
  checksum: 7167432de6817fe8e9e0c9684f1d2de2bb688c94388f7569f7dbdb1587c9f4ca2a77962f134ec90be0cc4d004c939ff0d05acc9f34a0db39a3c797dada262633
  languageName: node
  linkType: hard

"watchpack@npm:^2.4.0":
  version: 2.4.0
  resolution: "watchpack@npm:2.4.0"
  dependencies:
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.1.2
  checksum: 23d4bc58634dbe13b86093e01c6a68d8096028b664ab7139d58f0c37d962d549a940e98f2f201cecdabd6f9c340338dc73ef8bf094a2249ef582f35183d1a131
  languageName: node
  linkType: hard

"webpack-cli@npm:^5.0.1":
  version: 5.0.1
  resolution: "webpack-cli@npm:5.0.1"
  dependencies:
    "@discoveryjs/json-ext": ^0.5.0
    "@webpack-cli/configtest": ^2.0.1
    "@webpack-cli/info": ^2.0.1
    "@webpack-cli/serve": ^2.0.1
    colorette: ^2.0.14
    commander: ^9.4.1
    cross-spawn: ^7.0.3
    envinfo: ^7.7.3
    fastest-levenshtein: ^1.0.12
    import-local: ^3.0.2
    interpret: ^3.1.1
    rechoir: ^0.8.0
    webpack-merge: ^5.7.3
  peerDependencies:
    webpack: 5.x.x
  peerDependenciesMeta:
    "@webpack-cli/generators":
      optional: true
    webpack-bundle-analyzer:
      optional: true
    webpack-dev-server:
      optional: true
  bin:
    webpack-cli: bin/cli.js
  checksum: b1544eea669442e78c3dba9f79c0f8d0136759b8b2fe9cd32c0d410250fd719988ae037778ba88993215d44971169f2c268c0c934068be561711615f1951bd53
  languageName: node
  linkType: hard

"webpack-merge@npm:^4.1.4":
  version: 4.2.2
  resolution: "webpack-merge@npm:4.2.2"
  dependencies:
    lodash: ^4.17.15
  checksum: ce58bc8ab53a3dd5d9a0df65684571349eef53372bf8f224521072110485391335b26ab097c5f07829b88d0c146056944149566e5a953f05997b0fe2cbaf8dd6
  languageName: node
  linkType: hard

"webpack-merge@npm:^5.7.3":
  version: 5.8.0
  resolution: "webpack-merge@npm:5.8.0"
  dependencies:
    clone-deep: ^4.0.1
    wildcard: ^2.0.0
  checksum: 88786ab91013f1bd2a683834ff381be81c245a4b0f63304a5103e90f6653f44dab496a0768287f8531761f8ad957d1f9f3ccb2cb55df0de1bd9ee343e079da26
  languageName: node
  linkType: hard

"webpack-sources@npm:^3.2.3":
  version: 3.2.3
  resolution: "webpack-sources@npm:3.2.3"
  checksum: 989e401b9fe3536529e2a99dac8c1bdc50e3a0a2c8669cbafad31271eadd994bc9405f88a3039cd2e29db5e6d9d0926ceb7a1a4e7409ece021fe79c37d9c4607
  languageName: node
  linkType: hard

"webpack@npm:^5.75.0":
  version: 5.75.0
  resolution: "webpack@npm:5.75.0"
  dependencies:
    "@types/eslint-scope": ^3.7.3
    "@types/estree": ^0.0.51
    "@webassemblyjs/ast": 1.11.1
    "@webassemblyjs/wasm-edit": 1.11.1
    "@webassemblyjs/wasm-parser": 1.11.1
    acorn: ^8.7.1
    acorn-import-assertions: ^1.7.6
    browserslist: ^4.14.5
    chrome-trace-event: ^1.0.2
    enhanced-resolve: ^5.10.0
    es-module-lexer: ^0.9.0
    eslint-scope: 5.1.1
    events: ^3.2.0
    glob-to-regexp: ^0.4.1
    graceful-fs: ^4.2.9
    json-parse-even-better-errors: ^2.3.1
    loader-runner: ^4.2.0
    mime-types: ^2.1.27
    neo-async: ^2.6.2
    schema-utils: ^3.1.0
    tapable: ^2.1.1
    terser-webpack-plugin: ^5.1.3
    watchpack: ^2.4.0
    webpack-sources: ^3.2.3
  peerDependenciesMeta:
    webpack-cli:
      optional: true
  bin:
    webpack: bin/webpack.js
  checksum: 2bcc5f3c195f375944e8af2f00bf2feea39cb9fda5f763b0d1b00077f1c51783db25c94d3fae96a07dead9fa085e6ae7474417e5ab31719c9776ea5969ceb83a
  languageName: node
  linkType: hard

"which@npm:^2.0.1":
  version: 2.0.2
  resolution: "which@npm:2.0.2"
  dependencies:
    isexe: ^2.0.0
  bin:
    node-which: ./bin/node-which
  checksum: 1a5c563d3c1b52d5f893c8b61afe11abc3bab4afac492e8da5bde69d550de701cf9806235f20a47b5c8fa8a1d6a9135841de2596535e998027a54589000e66d1
  languageName: node
  linkType: hard

"wildcard@npm:^2.0.0":
  version: 2.0.0
  resolution: "wildcard@npm:2.0.0"
  checksum: 1f4fe4c03dfc492777c60f795bbba597ac78794f1b650d68f398fbee9adb765367c516ebd4220889b6a81e9626e7228bbe0d66237abb311573c2ee1f4902a5ad
  languageName: node
  linkType: hard

"yallist@npm:^4.0.0":
  version: 4.0.0
  resolution: "yallist@npm:4.0.0"
  checksum: 343617202af32df2a15a3be36a5a8c0c8545208f3d3dfbc6bb7c3e3b7e8c6f8e7485432e4f3b88da3031a6e20afa7c711eded32ddfb122896ac5d914e75848d5
  languageName: node
  linkType: hard
