import * as React from 'react';

export interface Props {
    name: string | Symbol;
    [key: string]: any;
}
interface FillContext {
    bus: {
        emit: (event: string, payload: any) => void;
    };
}

export default class Fill extends React.Component<Props, {}> {
    UNSAFE_componentWillMount() {
        this.context.bus.emit('fill-mount', {
            fill: this,
        });
    }

    componentDidUpdate() {
        this.context.bus.emit('fill-updated', {
            fill: this,
        });
    }

    componentWillUnmount() {
        this.context.bus.emit('fill-unmount', {
            fill: this,
        });
    }

    render() {
        return null;
    }
}
