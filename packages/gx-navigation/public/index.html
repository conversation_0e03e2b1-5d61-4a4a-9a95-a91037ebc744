<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap"
      rel="stylesheet"
    />
  </head>
  <body>
    <div id="gx-navigation"></div>
    <script src="/gx-navigation.js"></script>
    <script>
      const defaultProps = {
        agencyImage: 'https://placehold.co/400x100',
        agencyName: 'CasaMia',
        agentName: '<PERSON>',
        agentEmail: '<EMAIL>',
        agentAvatar: 'https://placehold.co/100x100',
        logo: '/images/logo-getrix.svg',
        homeUrl: '/',
        siteName: 'Getrix',
      }

      GxNavigation.initialize({ context: defaultProps })
    </script>
  </body>
</html>
