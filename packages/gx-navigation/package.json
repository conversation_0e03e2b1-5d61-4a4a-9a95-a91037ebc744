{"name": "gx-navigation", "version": "1.0.0", "license": "proprietary", "private": true, "source": "./src/index.tsx", "main": "./public/gx-navigation.js", "scripts": {"build": "webpack --config ./webpack.prod.js", "watch": "webpack --watch --config ./webpack.dev.js", "start": "webpack serve --config ./webpack.dev.js --open", "lint": "eslint src/**/*.{js,jsx,ts,tsx,json}", "lint:fix": "eslint --fix 'src/**/*.{js,jsx,ts,tsx,json}'", "format": "prettier --write 'src/**/*.{js,jsx,ts,tsx,css,md,json}' --config ./.prettierrc"}, "volta": {"node": "18.14.2", "yarn": "3.4.1"}, "dependencies": {"@gx-design/action-list": "^3.2.0", "@gx-design/badge": "^5.2.0", "@gx-design/button": "^5.2.0", "@gx-design/dropdown": "^5.2.0", "@gx-design/icon": "5.4.0", "@gx-design/popover": "3.2.2", "@gx-design/tag": "^5.2.0", "@gx-design/tooltip": "3.2.4", "@gx-design/use-media-match": "^3.0.1", "@gx-design/use-on-click-outside": "^3.0.1", "@pepita-i18n/babelfish": "^2.0.4", "@pepita/estats": "^1.0.9", "@sentry/browser": "^7.100.1", "clsx": "^1.2.1", "css-loader": "^6.7.3", "estats": "workspace:^", "react": "^18.3.1", "react-dom": "^18.3.1", "style-loader": "^3.3.2", "webpack-manifest-plugin": "^5.0.0"}, "devDependencies": {"@types/node": "^17.0.2", "@types/react": "^18.3.20", "@types/react-dom": "^18.3.6", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "eslint": "^8.39.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^4.6.0", "prettier": "^2.8.8", "sass": "^1.62.1", "sass-loader": "^13.2.2", "ts-loader": "^9.4.2", "typescript": "^5.1.6", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "webpack-dev-server": "^4.13.3", "webpack-merge": "^4.1.4"}, "packageManager": "yarn@3.4.1", "browserslist": ["last 2 versions", "safari >= 7", "ie >= 10"], "repository": {"type": "git", "url": "https://gitlab.pepita.io/getrix/mls-site.git", "directory": "packages/gx-navigation"}}