import { createContext, PropsWithChildren, useContext, useEffect, useState } from 'react';
import { CountersApiRespObj } from '../../../types';
import { on } from '../../../events/utils';
import { UPDATE_STATS } from '../../../events/inputTypes';

const CountersContext = createContext<CountersApiRespObj | undefined>(undefined);

export const useCounters = () => {
  const [counter, setCounter] = useState<CountersApiRespObj>({
    newAgencyActiveSearches: 0,
    newMatches: 0,
    unreadThreads: 0,
  });

  useEffect(() => {
    const unsubscribe = on(UPDATE_STATS, (evt: CountersApiRespObj) => {
      setCounter(evt);
    });

    return () => unsubscribe();
  }, []);

  return counter;
};

export const CountersProvider = ({ children }: PropsWithChildren) => {
  const counters = useCounters();

  return <CountersContext.Provider value={counters}>{children}</CountersContext.Provider>;
};

export const useCountersContext = () => {
  const context = useContext(CountersContext);
  if (!context) {
    throw new Error('useCountersContext must be used within a CountersProvider');
  }
  return context;
};
