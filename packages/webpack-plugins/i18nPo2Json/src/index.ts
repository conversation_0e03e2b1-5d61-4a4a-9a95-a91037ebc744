import * as webpack from 'webpack';
import { Compilation } from 'webpack'
const { sources: { RawSource } } = require('webpack');
import { readFile } from 'fs'
import { promisify } from 'util';
import { join } from 'path';
import * as gettext from 'gettext-parser'
import { createHash } from 'crypto';
import * as glob from 'glob';

type IFormatter = (input: string) => string
export type TranslationSources = { locale: string; files: string[] }[];

const fileRead = promisify(readFile);

const checkColons = /^\w+\: +(.+)$/
const addInitSpaceToColWord: IFormatter = (input: string) => checkColons.test(input) ? ' ' + input : input
const formatters: IFormatter[] = [addInitSpaceToColWord]

const formatMsgstr = (input: string) => {
    let out = input

    for (let formatter of formatters) {
        out = formatter(out)
    }

    return out
}



const mergeTranslations = (list: TranslationSources[]) => {
    return list.reduce((result, source) => {
        source.forEach(({ locale, files }) => {
            const value = result.find(value => value.locale === locale);

            if (value) {
                value.files.push(...files);
            } else {
                result.push({ locale, files });
            }
        });

        return result;
    })
};

export const locateTranslations = (translationsDirs: string[]) =>
    translationsDirs.map((translationsDir) => {
        const files = glob
            .sync(['*.po', '*.json'], {
                cwd: translationsDir,
            })

        return files.map((file) => ({
            locale: file.match(/(locale|messages)\.(.+?)\.(po|json)/)[2],
            files: [join(translationsDir, file)],
        }))
    })





export class I18nPo2Json {
    sources: string[];
    hashed: boolean;
    constructor(sources: string[], hashed: boolean = false) {
        this.sources = sources;
        this.hashed = hashed;
    }

    apply(compiler: webpack.Compiler) {
        const plugin = 'I18nPo2Json';
        compiler.hooks.beforeRun.tap(plugin, compilation => {

        })

        compiler.hooks.thisCompilation.tap(plugin, (compilation) => {

            compilation.hooks.processAssets.tapPromise(
                {
                    name: 'I18nPo2Json',
                    stage: Compilation.PROCESS_ASSETS_STAGE_ADDITIONAL,
                },
                () => this.generateTranslations(compilation)
            )
        })
    }

    async generateTranslations(compilation: webpack.Compilation) {
        let extractedTranslations: Set<string> | null = null;

        try {
            extractedTranslations = new Set(
                require(join(process.cwd(), 'clientTranslationKeys.json'))
            );
        } catch (o_O) { }

        function isUsefulTranslation(key: string) {
            return (
                extractedTranslations === null || extractedTranslations.has(key)
            );
        }

        await Promise.all(

            mergeTranslations(
                locateTranslations(this.sources)
            )
                .map(async ({ locale, files }) => {
                    const sources = await Promise.all(
                        files.map(async (file) => {
                            const buffer = await fileRead(file)
                            const ext = /.*?\.([^\.]*?)(\?|\/$|$|#).*/.exec(file)[1]

                            return {
                                buffer,
                                ext
                            }
                        })
                    );

                    const translations = sources.map(
                        (source) => {
                            switch (source.ext) {
                                case 'json':
                                    return JSON.parse(source.buffer.toString()) as gettext.GetTextTranslations['translations']['']
                                case 'po':
                                    return gettext.po.parse(source.buffer).translations['']
                            }
                        }
                    );

                    const result: {
                        [id: string]: string
                    } = {};

                    //TODO Plural position based on language
                    translations.forEach((translation) =>
                        Object.entries(translation).forEach(([id, data]) => {
                            if (id === '') return; //Skips the po heading

                            if (isUsefulTranslation(id)) {
                                const msgstr = data.msgstr.join('|')
                                result[id] = formatMsgstr(msgstr)
                            }

                            if (
                                data.msgid_plural &&
                                isUsefulTranslation(data.msgid_plural)
                            ) {
                                const msgStrPlural = data.msgstr
                                    .slice(1)
                                    .join('|')

                                result[data.msgid_plural] = formatMsgstr(msgStrPlural)
                            }
                        })
                    );

                    const content = JSON.stringify(result);

                    if (this.hashed) {
                        const hash = createHash('sha256')
                            .update(content)
                            .digest('hex')
                            .slice(0, 6);

                        compilation.emitAsset(`locale.${locale}.${hash}.json`, new RawSource(content), {
                            sourceFilename: `locale.${locale}.json`
                        })
                    } else {
                        compilation.emitAsset(`locale.${locale}.json`, new RawSource(content), {
                            sourceFilename: `locale.${locale}.json`
                        })
                    }
                })
        )
    }
}
