{% extends 'base.html.twig' %}

{% block rawContent %}

    <div class="auth2-box">
        <div class="auth2-box__logo">
            {% if gtxConstants.APP_NAME == 'Getrix' %}
                <img src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo-gtx/logo.svg" alt="{{ appName }}" />
            {% else %}
                <img src="/bundles/base/getrix/common/img/country/{{ gtxConstants.COUNTRY_TAG }}/logo/logo.svg" alt="{{ appName }}" />
            {% endif %}                
        </div>
        <div data-role="form-container">
            {% include 'base/two_factor_authentication/partials/' ~ templateInnerBlock ~ '.html.twig' %}
            <div class="gx-alert gx-alert--marginTop gx-alert--info">
                <svg class="gx-icon gx-icon--info" xmlns="http://www.w3.org/2000/svg" id="info-circle" viewBox="0 0 24 24" fill-rule="evenodd">
                    <path d="M12 10.5a.75.75 0 0 0-.75.75v6a.75.75 0 1 0 1.5 0v-6a.75.75 0 0 0-.75-.75ZM12 7a1 1 0 1 0 0 2 1 1 0 0 0 0-2Z"/>
                    <path d="M2 12C2 6.34 6.34 2 12 2s10 4.34 10 10-4.34 10-10 10S2 17.66 2 12Zm10-8.5c-4.832 0-8.5 3.668-8.5 8.5s3.668 8.5 8.5 8.5 8.5-3.668 8.5-8.5-3.668-8.5-8.5-8.5Z"/>
                </svg>
                <span>{{ trans("a2f.info", {'%APP_NAME%': gtxConstants.APP_NAME}, gtxConstants.DOMAIN_CMS) }}</span>
            </div>
        </div>
    </div>

{% endblock %}

{% block javascripts %}
    {{ parent() }}
    {{ webpack_asset('base/a2f') }}
{% endblock %}

{% block onboarding_js %}
{% endblock %}
