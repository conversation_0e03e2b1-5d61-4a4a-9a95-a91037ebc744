{% extends getrixCommonTemplatesPath ~ 'header.html.twig' %}

{% block actions %}
    <div data-role="headerBackBtn" class="{% if not internalBackPath %}hidden{% endif %}">
        <a role="button" class="gx-button gx-button--iconOnly gx-head-section__back gx-head-section__back--top" href="{% if internalBackPath %}{{ internalBackPath }}{% endif %}">
            <svg class="gx-icon">
                <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#long-arrow-left_icon"></use>
            </svg>
        </a>
    </div>
    <div data-role="headerAddBtn" class="{% if not internalAddPath %}hidden{% endif %}">
        <a href="{% if internalAddPath %}{{ internalAddPath }}{% endif %}" role="button" class="gx-button gx-button--small gx-button--accent gx-button--iconOnly-xs">
            <svg class="gx-icon">
                <use xlink:href="{{ gtxConstants.ICONS_SVG_SPRITE_FILE_PATH }}#plus_icon"></use>
            </svg>
            <span>{{ 'label.add'|trans({}, gtxConstants.DOMAIN_CMS) }}</span>
        </a>
    </div>
{% endblock %}
