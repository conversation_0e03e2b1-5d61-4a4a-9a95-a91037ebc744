<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <meta name="viewport" content="width=device-width" />
    <title>{{ gtxConstants.APP_NAME }}</title>
    <style>
        [style*="Open Sans"] {
            font-family: 'Open Sans', Arial, sans-serif !important;
        }
    </style>
</head>
<body style="width: 100% !important; min-width: 100%; -webkit-text-size-adjust: 100%; -ms-text-size-adjust: 100%; -moz-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; color: #333333; font-family: Arial, sans-serif, 'Open Sans'; margin: 0; padding: 0;">
<style type="text/css">
    @font-face {
        font-family: 'Open Sans'; font-style: normal; font-weight: 400; src: local('Open Sans'), local('OpenSans'), url('https://fonts.gstatic.com/s/opensans/v13/cJZKeOuBrn4kERxqtaUH3aCWcynf_cDxXwCLxiixG1c.ttf') format('truetype');
    }
    @font-face {
        font-family: 'Open Sans'; font-style: normal; font-weight: 600; src: local('Open Sans Semibold'), local('OpenSans-Semibold'), url('https://fonts.gstatic.com/s/opensans/v13/MTP_ySUJH_bn48VBG8sNSonF5uFdDttMLvmWuJdhhgs.ttf') format('truetype');
    }
</style>
<table class="body" style="border-spacing: 0; border-collapse: collapse; width: 100%; background: #f2f2f2; padding: 0;" bgcolor="#f2f2f2"><tr><td class="float-center" align="center" valign="top" style="padding: 0;">
            <center data-parsed="">
                <!--[if gte mso]>

                <table width="640" cellpadding="0" cellspacing="0" border="0" style="margin: 0 auto;">
                    <tr align="center">
                        <td align="center">

                <![endif]-->
                <table align="center" class="container" style="border-spacing: 0; border-collapse: collapse; width: 90%; max-width: 640px; min-width: 300px; text-align: inherit; margin: 20px auto 0;">
                    <tr>
                        <td style="padding: 0;">
                            <table align="center" class="main" style="border-spacing: 0; border-collapse: collapse; width: 100%; text-align: center; border-radius: 3px; background: #ffffff; padding: 0; border: 1px solid #dddddd;" bgcolor="#ffffff">
                                <tr>
                                    <td class="header" style="text-align: center; background: #fafafa; padding: 25px 0;" align="center" bgcolor="#fafafa">
                                        <center data-parsed="">
                                            <img class="logo" src="{{ globalConfigs.getrix.baseurl }}{{ asset('base/getrix/common/img/country/' ~ gtxConstants.COUNTRY_TAG ~ '/email/logo.png') }}" alt="{{ gtxConstants.APP_NAME }}" style="height:30px; margin: 0 auto;" />
                                        </center>
                                    </td>
                                </tr>
                                <tr>
                                    <td class="content" style="padding: 25px 20px 0;">
                                        {% set btnBgColor = '#e50013' %}
                                        {% block content %}{% endblock %}
                                    </td>
                                </tr>
                            </table>
                        </td>
                    </tr>
                    <tr>
                        <td class="footer" style="text-align: center; font-family: Arial, sans-serif; color: #777777; padding: 20px 0 40px;" align="center">
                            {% block footer %}{% endblock %}
                        </td>
                    </tr>
                </table>
                <!--[if gte mso]>
                <td>
                    </tr>
                    </table>

                <![endif]-->
            </center>
        </td>
    </tr>
</table>
</body>
</html>
