# you can read more about security in the related section of the documentation
# http://symfony.com/doc/current/book/security.html
security:
  access_control:
    - { path: ^/mail/invite$, roles: IS_AUTHENTICATED_ANONYMOUSLY } # Chi<PERSON><PERSON> senza https da script getrix
    - { path: ^/rest/status$, roles: IS_AUTHENTICATED_ANONYMOUSLY } # Chiamata senza https da Nagios
    - { path: ^/rest/sso, roles: IS_AUTHENTICATED_ANONYMOUSLY } # Chiamata senza https da Immobiliare.it
    - { path: ^/rest/product-sso, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/rest/profile/me, roles: ROLE_USER } # Chiamata senza https da GetrixV2 (checkPersistentSession)
    - { path: ^/rest/suggest, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/rest/agency/new, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/rest/security/login$, roles: IS_AUTHENTICATED_ANONYMOUSLY } # <PERSON><PERSON><PERSON> senza https da GetrixV2 (login legacy per app mobile)
    - { path: ^/rest/security/set-password$, roles: IS_AUTHENTICATED_ANONYMOUSLY } # Chiamata senza https da Immobiliare Backoffice (forcing della procedura di recupero password)
    - { path: ^/rest/security/set-password/migration$, roles: IS_AUTHENTICATED_ANONYMOUSLY } # Chiamata senza https da Immobiliare Backoffice (migrazione agenzie es)
    - { path: ^/rest/security/password-recovery$, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/rest/portals/sso, roles: ROLE_USER }
    - { path: ^/rest/property/get-portals-publish-available, roles: ROLE_USER }
    - { path: ^/rest/property/get-portals-property, roles: ROLE_USER }
    - { path: ^/rest/property/get-zone-property-portal, roles: ROLE_USER }
    - { path: ^/rest/property/set-publish-portals, roles: ROLE_USER }
    - { path: ^/rest/property/remove-publish-portals, roles: ROLE_USER }
    - { path: ^/rest/property/set-zone-property-portal, roles: ROLE_USER }
    - { path: ^/rest/property/get-private-property, roles: ROLE_USER }
    - { path: ^/rest/nuove-costruzioni/get-portals-project, roles: ROLE_USER }
    - { path: ^/rest/nuove-costruzioni/set-publish-portals, roles: ROLE_USER }
    - { path: ^/rest/nuove-costruzioni/remove-publish-portals, roles: ROLE_USER }
    - { path: ^/internal, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/api/geography/cities, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/registrati, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/landing, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/signup, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/api/agency/registration$, roles: IS_AUTHENTICATED_ANONYMOUSLY, methods: [POST] }
    - { path: ^/signin, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/password-recovery$, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/set-password, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/reset-password, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/validate-profile-email, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/set-phone-number, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/login/client_app, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/events/immovisita/track, roles: IS_AUTHENTICATED_ANONYMOUSLY }
    - { path: ^/, roles: ROLE_USER }
