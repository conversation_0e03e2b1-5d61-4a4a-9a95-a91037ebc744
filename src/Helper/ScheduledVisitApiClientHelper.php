<?php

namespace App\Helper;

use App\Constants\Base\ImmovisitaCostants;
use App\Constants\Base\ScheduledVisitCostants;
use App\Constants\PerformanceProfiler;
use App\Exception\ImmovisitaException;
use App\Model\ScheduledVisit\ScheduledVisit;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;

class ScheduledVisitApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';
    const RESOURCE_SCHEDULED_VISIT = 'scheduled-visit';

    protected function formatEndpoint(int $agencyId, int $agentId): string
    {
        return \sprintf('%s/api/%s/agency/%d/agents/%d', $this->apiBaseurl, self::VERSION, $agencyId, $agentId);
    }

    /**
     * @throws ImmovisitaException
     */
    public function create(int $agencyId, int $agentId, Request $request): ScheduledVisit
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $scheduledVisitRequest = \App\Builder\Model\ApiModel\ScheduledVisit\ScheduledVisitRequestBuilder::newBuilder()
            ->fromRequest(\json_decode($request->getContent(), true), $request->get('scheduledVisitType'))
            ->build();

        $url = \sprintf(
            '%s/%s/%s',
            $this->formatEndpoint($agencyId, $agentId),
            self::RESOURCE_SCHEDULED_VISIT,
            ScheduledVisitCostants::VISIT_TYPES_ID_SLUG_MAPPING[$scheduledVisitRequest->getVisitType()]
        );

        $headers = $this->getDefaultJsonHeaders();

        if ($request->headers->has('X-Thread-Id')) {
            $headers = $this->getDefaultJsonHeaders() +
                [
                    'X-Thread-Id' => (int) $request->headers->get('X-Thread-Id'),
                    'X-Agent-Id' => $agentId,
                ];
        }

        $result = $this->httpClient->execRequestWithResponse(
            'POST',
            $url,
            $scheduledVisitRequest,
            $headers
        );

        if (Response::HTTP_CREATED !== $result->getStatusCode()) {
            throw new ImmovisitaException(ImmovisitaCostants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $scheduledVisit = \App\Builder\Model\ScheduledVisit\ScheduledVisitBuilder::newBuilder()
            ->fromApiResponse($decoded['data'] ?? [])
            ->build();

        $this->performanceProfiler->stop(__METHOD__);

        return $scheduledVisit;
    }
}
