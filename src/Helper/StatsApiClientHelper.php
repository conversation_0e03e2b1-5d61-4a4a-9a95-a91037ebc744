<?php

namespace App\Helper;

use App\Constants\Base\StatsConstants;
use App\Exception\ApiException;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Performance\ProfilerInterface;
use Symfony\Component\HttpFoundation\Response;

class StatsApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';
    const RESOURCE = 'stats';

    private string $endpoint;

    public function __construct(
        HttpClientHelper $httpClient,
        ProfilerInterface $performanceProfiler,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->endpoint = \sprintf('%s/api/%s/%s', $this->apiBaseurl, self::VERSION, self::RESOURCE);
    }

    /**
     * @throws ApiException
     */
    public function setModuleAccess(int $agencyId, int $extensionId): void
    {
        $url = \sprintf(
            '%s/modules/%s/access',
            $this->endpoint,
            $extensionId
        );

        $payload = [
            'agencyId' => $agencyId,
        ];

        $result = $this->httpClient->execRequestWithResponse('PUT', $url, $payload, $this->getDefaultJsonHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(StatsConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }
    }

    /**
     * @throws ApiException
     */
    public function isModuleAccessed(int $agencyId, int $extensionId)
    {
        $url = \sprintf(
            '%s/modules/%s/access/%s',
            $this->endpoint,
            $extensionId,
            $agencyId
        );

        $result = $this->httpClient->execRequestWithResponse('GET', $url, [], $this->getDefaultJsonHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(StatsConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        return \json_decode($result->getBody()->getContents(), true);
    }

    /**
     * @throws ApiException
     */
    public function setRequestService(string $product, string $action): void
    {
        $url = \sprintf(
            '%s/products/%s/actions/%s',
            $this->endpoint,
            $product,
            $action
        );

        $result = $this->httpClient->execRequestWithResponse('PUT', $url, [], $this->getDefaultJsonHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new ApiException(StatsConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }
    }
}
