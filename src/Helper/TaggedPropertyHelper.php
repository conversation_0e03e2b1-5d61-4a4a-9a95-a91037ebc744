<?php

namespace App\Helper;

use App\Annotation\TaggedProperty;
use Doctrine\Common\Annotations\Reader;
use RuntimeException;

class TaggedPropertyHelper
{
    private Reader $reader;

    public function __construct(Reader $reader)
    {
        $this->reader = $reader;
    }

    public function filterByTags($data, array $tags, array $exclude = [])
    {
        if (!\is_object($data)) {
            throw new RuntimeException('Not an object');
        }

        $filteredData = [];

        $reflectionClass = new \ReflectionClass($data);
        foreach ($reflectionClass->getProperties() as $reflectionProperty) {
            $reflectionProperty->setAccessible(true);

            /** @var TaggedProperty $propertyAnnotation */
            $propertyAnnotation = $this->reader
                ->getPropertyAnnotation($reflectionProperty, TaggedProperty::class);

            if (null === $propertyAnnotation) {
                continue;
            }

            if (!$propertyAnnotation->hasTag($tags) || $propertyAnnotation->hasTag($exclude)) {
                continue;
            }

            $propertyName = $reflectionProperty->getName();
            $propertyValue = $reflectionProperty->getValue($data);

            if (\is_object($propertyValue)) {
                $filteredData[$propertyName] = $this->filterByTags($propertyValue, $tags);
                continue;
            }

            if (\is_array($propertyValue)) {
                $filteredData[$propertyName] = $this->filterArray($propertyValue, $tags);
                continue;
            }

            $filteredData[$propertyName] = $propertyValue;
        }

        return $filteredData;
    }

    private function filterArray(array $propertyValue, array $tags)
    {
        return \array_map(function ($item) use ($tags) {
            return \is_object($item) ?
                $this->filterByTags($item, $tags) :
                $item;
        }, $propertyValue);
    }
}
