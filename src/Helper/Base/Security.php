<?php

namespace App\Helper\Base;

use App\Constants\Base\LanguagesConstants;
use App\Entity\Agency;
use App\Exception\AgentException;
use App\Exception\ApiException;
use App\Exception\EmailNotAvailableException;
use App\Exception\GetrixBadCredentialsException;
use App\Exception\GetrixDisabledAgencyException;
use App\Exception\GetrixDisabledAgentException;
use App\Helper\AgencyApiClientHelper;
use App\Helper\AgentApiClientHelper;
use App\Helper\LoginHelper;
use App\Security\User\GetrixUser;
use App\Service\Base\GetrixRequestProxy;
use App\Service\Base\Jwt\JwtService;
use App\Service\Mail\Mailer;
use App\Service\MigrationVerifier\Client\MigrationVerifierClient;
use App\Service\MigrationVerifier\Constants;
use Psr\Log\LoggerInterface;
use Services\Getrix\LoginTypes\Log;
use Symfony\Component\Form\Extension\Core\Type\FormType;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;
use Symfony\Component\Form\FormFactoryInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\RequestStack;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpFoundation\Session\SessionInterface;
use Symfony\Component\Routing\RouterInterface;
use Symfony\Component\Security\Core\Authentication\Token\Storage\TokenStorageInterface;
use Symfony\Component\Security\Core\Exception\UsernameNotFoundException;
use Symfony\Component\Translation\TranslatorInterface;

class Security
{
    protected AgencyApiClientHelper $agencyApiClientHelper;
    protected AgentApiClientHelper $agentApiClientHelper;
    protected FormFactoryInterface $formFactory;
    protected GetrixRequestProxy $getrixProxy;
    private JwtService $jwtService;
    protected LoggerInterface $logger;
    protected LoginHelper $loginHelper;
    protected Mailer $mailer;
    protected MigrationVerifierClient $migrationVerifierClient;
    protected RequestStack $requestStack;
    protected RouterInterface $router;
    protected SessionInterface $session;
    protected TokenStorageInterface $tokenStorage;
    protected TranslatorInterface $translator;
    protected User $userHelper;

    const TOKEN_TYPE_AGENCY = 'a';
    const TOKEN_TYPE_AGENT = 'u';

    const MAX_SECONDS_TOKEN_LIFE = 3600;

    public function __construct(
        FormFactoryInterface $formFactory,
        RouterInterface $router,
        Mailer $mailer,
        TokenStorageInterface $tokenStorage,
        GetrixRequestProxy $proxy,
        LoggerInterface $logger,
        SessionInterface $session,
        TranslatorInterface $translator,
        MigrationVerifierClient $migrationVerifierClient,
        RequestStack $requestStack,
        User $userHelper,
        AgencyApiClientHelper $agencyApiClientHelper,
        AgentApiClientHelper $agentApiClientHelper,
        LoginHelper $loginHelper,
        JwtService $jwtService
    ) {
        $this->formFactory = $formFactory;
        $this->router = $router;
        $this->mailer = $mailer;
        $this->tokenStorage = $tokenStorage;
        $this->getrixProxy = $proxy;
        $this->logger = $logger;
        $this->session = $session;
        $this->translator = $translator;
        $this->migrationVerifierClient = $migrationVerifierClient;
        $this->requestStack = $requestStack;
        $this->userHelper = $userHelper;
        $this->agencyApiClientHelper = $agencyApiClientHelper;
        $this->agentApiClientHelper = $agentApiClientHelper;
        $this->loginHelper = $loginHelper;
        $this->jwtService = $jwtService;
    }

    /**
     * @throws \Exception
     */
    public function getUserIdFromJwtToken(string $token): array
    {
        $tokenData = $this->userHelper->getUserDataFromJwtToken($token);
        $now = \time();

        if (empty($tokenData) || ($now - $tokenData->time) < 0 || ($now - $tokenData->time) > self::MAX_SECONDS_TOKEN_LIFE) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        return [
            'agencyId' => $tokenData->agencyId,
            'agentId' => $tokenData->agentId,
        ];
    }

    /**
     * @throws \Exception
     */
    public function hasMobileNumbers(int $agentId, int $agencyId): bool
    {
        return $this->userHelper->hasMobileNumbers($agentId, $agencyId);
    }

    public function setAgentMobilePhoneNumber(int $agentId, int $agencyId, string $phoneNumber): bool
    {
        try {
            return $this->userHelper->setAgentMobilePhoneNumber($agentId, $agencyId, $phoneNumber);
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * @throws EmailNotAvailableException
     * @throws \Exception
     */
    public function changeAgentEmail(Request $request)
    {
        try {
            $agentToken = $this->agentApiClientHelper
                ->getTokenByToken($request->get('token'));
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        $this->agentApiClientHelper
            ->changeEmail(
                $agentToken->getAgencyId(),
                $agentToken->getAgentId(),
                $request->get('token')
            );
    }

    /**
     * @param $isChangePassword
     *
     * @throws \Exception
     * @return mixed
     */
    public function setPassword(Request $request, $isChangePassword)
    {
        $type = $request->get('type');

        if (self::TOKEN_TYPE_AGENCY === $type) {
            return $this->setPasswordAgency($request);
        }

        return $this->setPasswordAgent($request, $isChangePassword);
    }

    /**
     * @param $isChangePassword
     *
     * @throws \Exception
     * @return mixed
     */
    public function setPasswordAgent(Request $request, $isChangePassword)
    {
        if (!$isChangePassword) {
            $action = $this->router->generate('app_set_password', [
                'token' => $request->get('token'),
            ]);
        } else {
            $action = $this->router->generate('app_reset_password', [
                'type' => $request->get('type'),
                'token' => $request->get('token'),
            ]);
        }

        $form = $this->getSetPasswordForm($action);
        $form->handleRequest($request);

        if ($form->isSubmitted()) {
            if ($form->isValid()) {
                try {
                    true === $isChangePassword ?
                        $this->executeResetPasswordAgente($request) :
                        $this->executeInitializePassword($request);
                } catch (\Throwable $t) {
                    $this->session->getFlashBag()->set('error', $this->translator->trans('label.saving_error', [], LanguagesConstants::DOMAIN_CMS));

                    return $form;
                }

                $this->tokenStorage->setToken(null);
                throw new \Exception($this->translator->trans('label.password_set', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_OK);
            }
            $this->session->getFlashBag()->set('error', $this->translator->trans('label.saving_error', [], LanguagesConstants::DOMAIN_CMS));
        }

        return $form;
    }

    /**
     * @throws \Exception
     * @return mixed
     */
    public function setPasswordAgency(Request $request)
    {
        $action = $this->router->generate('app_reset_password', [
            'type' => $request->get('type'),
            'token' => $request->get('token'),
        ]);

        $form = $this->getSetPasswordForm($action);
        $form->handleRequest($request);

        $token = $request->get('token');

        try {
            $resetPasswordToken = $this->agencyApiClientHelper
                ->getResetPasswordTokenByToken($token);
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        if ($form->isSubmitted()) {
            if ($form->isValid()) {
                $aForm = $request->get('form');
                $password = !empty($aForm['password']) && !empty($aForm['password']['first']) ? $aForm['password']['first'] : '';

                try {
                    $this->agencyApiClientHelper
                        ->resetPassword(
                            $resetPasswordToken->getAgency()->idAgenzia,
                            $request->get('token'),
                            $password,
                            $request->getClientIp(),
                            $request->headers->get('User-Agent')
                        );
                } catch (\Throwable $t) {
                    $this->session->getFlashBag()->set('error', $this->translator->trans('label.saving_error', [], LanguagesConstants::DOMAIN_CMS));

                    return $form;
                }

                $this->tokenStorage->setToken(null);
                throw new \Exception($this->translator->trans('label.password_set', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_OK);
            }
            $this->session->getFlashBag()->set('error', $this->translator->trans('label.saving_error', [], LanguagesConstants::DOMAIN_CMS));
        }

        return $form;
    }

    /**
     * @param $action
     *
     * @return mixed
     */
    public function getSetPasswordForm($action)
    {
        return $this->formFactory->createBuilder(FormType::class, null, ['csrf_protection' => false])
            ->add('password', RepeatedType::class, [
                'type' => PasswordType::class,
                'invalid_message' => $this->translator->trans('label.passwords_must_match', [], LanguagesConstants::DOMAIN_CMS),
                'required' => true,
                'first_options' => ['label' => $this->translator->trans('label.password', [], LanguagesConstants::DOMAIN_CMS)],
                'second_options' => ['label' => $this->translator->trans('label.repeat_password', [], LanguagesConstants::DOMAIN_CMS)],
            ])
            ->setMethod('POST')
            ->setAction($action)
            ->getForm();
    }

    /**
     * @throws \Exception
     */
    protected function executeResetPasswordAgente(Request $request)
    {
        try {
            $agentToken = $this->agentApiClientHelper
                ->getTokenByToken($request->get('token'));
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        $aForm = $request->get('form');
        $password = !empty($aForm['password']) && !empty($aForm['password']['first']) ? $aForm['password']['first'] : '';

        $this->agentApiClientHelper
            ->resetPassword(
                $agentToken->getAgencyId(),
                $agentToken->getAgentId(),
                $request->get('token'),
                $password
            );

        return null;
    }

    /**
     * @throws \Exception
     */
    protected function executeInitializePassword(Request $request)
    {
        try {
            $agentToken = $this->agentApiClientHelper
                ->getTokenByToken($request->get('token'));
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        try {
            $agency = $this->agencyApiClientHelper
                ->getAgency($agentToken->getAgencyId());
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        try {
            $agent = $this->agentApiClientHelper->getAgent(
                $agentToken->getAgentId(),
                $agentToken->getAgencyId()
            );
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.not_valid_link', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        $aForm = $request->get('form');
        $password = !empty($aForm['password']) && !empty($aForm['password']['first']) ? $aForm['password']['first'] : '';

        $this->agentApiClientHelper
            ->initializePassword(
                $agentToken->getAgencyId(),
                $agentToken->getAgentId(),
                $request->get('token'),
                $password
            );

        $emailNotificationAgent = new \stdClass();
        $emailNotificationAgent->nome = $agent->nome;
        $emailNotificationAgent->cognome = $agent->cognome;
        $emailNotificationAgent->email = $agent->email;

        $this->mailer->sendActivationToUser($emailNotificationAgent);

        $emailNotificationAgencyOwner = new \stdClass();
        $emailNotificationAgencyOwner->nome = $agency->nomeTitolare;
        $emailNotificationAgencyOwner->cognome = $agency->cognomeTitolare;
        $emailNotificationAgencyOwner->email = $agency->emailTitolare;

        $this->mailer->sendActivationToOwner($emailNotificationAgent, $emailNotificationAgencyOwner);
    }

    public function getMessagePageParameters($data): array
    {
        return [
            'template' => 'base/security/message.html.twig',
            'params' => [
                'messageTitle' => !empty($data['title']) ? $data['title'] : null,
                'message' => $data['message'],
                'messageIsRaw' => true,
                'messageType' => $data['type'],
            ],
        ];
    }

    /**
     * @param bool $logAction
     * @param bool $updateStats
     * @param bool $isSso
     *
     * @return Log
     */
    protected function createLog($logAction = true, $updateStats = true, $isSso = false)
    {
        $request = $this->requestStack->getCurrentRequest();

        return new Log([
            'logAction' => $logAction,
            'updateStats' => $logAction && $updateStats,
            'datiGet' => \serialize($request->query->all()),
            'datiPost' => \serialize($request->request->all()),
            'ip' => $request->getClientIp(),
            'sso' => $isSso ? 1 : 0,
        ]);
    }

    /**
     * @param string $email
     *
     * @throws \Exception
     * @throws \GuzzleHttp\Exception\GuzzleException
     *
     *  TODO BIG REFACTORING!!!!
     */
    public function recoveryPassword($email)
    {
        try {
            $serviceToken = $this->loginHelper
                ->loginByUsername($email);

            if ($this->migrationVerifierClient->isVerificationEnabled()) {
                $migrationVerifyResponse = $this->migrationVerifierClient
                    ->verify($serviceToken->data->idAgenzia);

                if (Constants::STATUS_MIGRATED !== $migrationVerifyResponse->getStatus() && Constants::STATUS_NOT_FOUND !== $migrationVerifyResponse->getStatus()) {
                    throw new UsernameNotFoundException($this->translator->trans('password_recovery.text', ['%email%' => $email], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
                }
            }
        } catch (GetrixDisabledAgencyException|GetrixDisabledAgentException|GetrixBadCredentialsException $e) {
            throw new UsernameNotFoundException($this->translator->trans('password_recovery.text', ['%email%' => $email], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        } catch (\Exception $e) {
            throw new \Exception($this->translator->trans('label.service_temporarily_unavailable', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            $agency = $this->agencyApiClientHelper
                ->getAgencyByEmail($email);
        } catch (ApiException $e) {
            $agency = null;
        }

        null !== $agency ?
            $this->agencyPasswordRecovery($agency) :
            $this->agentPasswordRecovery($email);
    }

    /**
     * @throws \Exception
     */
    private function agencyPasswordRecovery(Agency $agency)
    {
        try {
            $resetPasswordToken = $this->agencyApiClientHelper
                ->setResetPasswordToken($agency->idAgenzia);
        } catch (ApiException $e) {
            throw new \Exception($this->translator->trans('label.service_temporarily_unavailable', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            $data = new \stdClass();
            $data->nome = $agency->nomeTitolare;
            $data->cognome = $agency->cognomeTitolare;
            $data->email = $agency->email;

            $changePasswordLink = $this->router->generate('app_reset_password', [
                'type' => self::TOKEN_TYPE_AGENCY,
                'token' => $resetPasswordToken->getToken()->getToken(),
            ]);
            $this->mailer->sendResetPassword($data, $changePasswordLink);
        } catch (\Throwable $t) {
            throw new \Exception($this->translator->trans('label.service_temporarily_unavailable', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    /**
     * @throws \Exception
     */
    private function agentPasswordRecovery(string $email)
    {
        try {
            $agent = $this->agentApiClientHelper->getAgentByEmail($email);
        } catch (AgentException $e) {
            throw new \Exception($this->translator->trans('label.account_not_found', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_BAD_REQUEST);
        }

        try {
            $resetPasswordToken = $this->agentApiClientHelper
                ->setResetPasswordToken($agent->fkAgenzia, $agent->idAgente);
        } catch (ApiException $e) {
            throw new \Exception($this->translator->trans('label.service_temporarily_unavailable', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        try {
            $emailRecipient = new \stdClass();
            $emailRecipient->nome = $agent->nome;
            $emailRecipient->cognome = $agent->cognome;
            $emailRecipient->email = $agent->email;

            $changePasswordLink = $this->router->generate('app_reset_password', [
                'type' => self::TOKEN_TYPE_AGENT,
                'token' => $resetPasswordToken->getToken(),
            ]);

            $this->mailer->sendResetPassword($emailRecipient, $changePasswordLink);
        } catch (\Throwable $t) {
            throw new \Exception($this->translator->trans('label.service_temporarily_unavailable', [], LanguagesConstants::DOMAIN_CMS), Response::HTTP_INTERNAL_SERVER_ERROR);
        }
    }

    public function isValidJwtToken(string $token): bool
    {
        if (!$this->jwtService->validate($token)) {
            return false;
        }
        $tokenData = $this->jwtService->decode($token);

        if (empty($tokenData->agencyId) || empty($tokenData->agentId) || empty($tokenData->time)) {
            return false;
        }
        $now = \time();

        $user = $this->getUser();
        $loggedAgencyId = !empty($user) ? $user->getIdAgenzia() : null;
        $loggedAgentId = !empty($user) ? $user->getId() : null;

        if (empty($tokenData)
            || ($now - $tokenData->time) < 0
            || ($now - $tokenData->time) > self::MAX_SECONDS_TOKEN_LIFE
            || empty($loggedAgencyId)
            || empty($loggedAgentId)
            || $tokenData->agencyId != $loggedAgencyId
            || $tokenData->agentId != $loggedAgentId) {
            return false;
        }

        return true;
    }

    public function getUser(): ?GetrixUser
    {
        $token = $this->tokenStorage->getToken();

        if (empty($token)) {
            return null;
        }

        $user = $token->getUser();

        if (empty($user) || !$user instanceof GetrixUser) {
            return null;
        }

        return $user;
    }
}
