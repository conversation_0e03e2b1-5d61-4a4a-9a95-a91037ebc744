<?php

namespace App\Helper;

use App\Constants\Base\AppointmentsConstants;
use App\Constants\PerformanceProfiler;
use App\Exception\AppointmentsException;
use App\Formatter\AppointmentsFormatter;
use App\Helper\Base\HttpClientHelper;
use App\Helper\Base\ParametersBag;
use App\Model\Appointment\ListFilter;
use App\Model\Shared\Order;
use App\Model\Shared\PaginationRequest;
use App\Performance\ProfilerInterface;
use Symfony\Component\HttpFoundation\Response;

class AppointmentsApiClientHelper extends ApiClientHelper
{
    const VERSION = 'v2';
    const RESOURCE = 'appointments';

    protected AppointmentsFormatter $appointmentsFormatter;
    private string $endpoint;

    public function __construct(
        HttpClientHelper $httpClient,
        ParametersBag $parametersBag,
        ApiWsseHeaderGenerator $apiWsseHeaderGenerator,
        AppointmentsFormatter $appointmentsFormatter,
        ProfilerInterface $performanceProfiler
    ) {
        parent::__construct($httpClient, $performanceProfiler, $parametersBag, $apiWsseHeaderGenerator);

        $this->appointmentsFormatter = $appointmentsFormatter;

        $this->endpoint = \sprintf('%s/api/%s/agency', $this->apiBaseurl, self::VERSION);
    }

    /**
     * @throws AppointmentsException
     */
    public function list(int $agencyId, ListFilter $filter, Order $order, PaginationRequest $pagination): array
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s?%s',
            $this->endpoint,
            $agencyId,
            self::RESOURCE,
            \http_build_query($this->buildAppointmentsListRequestParameters($filter, $order, $pagination))
        );

        $result = $this->httpClient->execRequestWithResponse('GET', $url, [], $this->getDefaultHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new AppointmentsException(AppointmentsConstants::EXCEPTION_MESSAGES['generic'], Response::HTTP_BAD_REQUEST);
        }

        $decoded = \json_decode($result->getBody()->getContents(), true);

        $this->performanceProfiler->stop(__METHOD__);

        return [
            'appointments' => \array_map(function ($appointment) {
                return $this->appointmentsFormatter->formatRemoteToLocalEntity($appointment);
            }, $decoded['data']) ?? null,
            'pagination' => [
                'total' => (int) $result->getHeaderLine('X-Total-Count'),
            ],
        ];
    }

    /**
     * @throws AppointmentsException
     */
    public function delete(int $agencyId, int $appointmentId): void
    {
        $this->performanceProfiler->start(__METHOD__, PerformanceProfiler::API_GTX_PROFILER_CATEGORY);

        $url = \sprintf(
            '%s/%s/%s/%s',
            $this->endpoint,
            $agencyId,
            self::RESOURCE,
            $appointmentId
        );

        $result = $this->httpClient->execRequestWithResponse('DELETE', $url, [], $this->getDefaultJsonHeaders());

        if (Response::HTTP_OK !== $result->getStatusCode()) {
            throw new AppointmentsException(AppointmentsConstants::EXCEPTION_MESSAGES['generic'], $result->getStatusCode());
        }

        $this->performanceProfiler->stop(__METHOD__);
    }

    private function buildAppointmentsListRequestParameters(ListFilter $filter, Order $order, PaginationRequest $pagination): array
    {
        $params = [];

        if (null !== $filter->getClientId()) {
            $params['clientId'] = $filter->getClientId();
        }

        if (null !== $filter->getStart()) {
            $params['start'] = $filter->getStart()->format('Y-m-d');
        }

        if (null !== $filter->getEnd()) {
            $params['end'] = $filter->getEnd()->format('Y-m-d');
        }

        if (!empty($order->getDirection()) && !empty($order->getField())) {
            $params['sort'] = $order->getDirection() . $order->getField();
        }

        if (!empty($pagination->getStart())) {
            $params['offset'] = $pagination->getStart();
        }

        if (!empty($pagination->getResults())) {
            $params['limit'] = $pagination->getResults();
        }

        return $params;
    }
}
