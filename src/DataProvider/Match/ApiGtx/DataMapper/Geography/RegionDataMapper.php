<?php

declare(strict_types=1);

namespace App\DataProvider\Match\ApiGtx\DataMapper\Geography;

use App\DataMapper\AbstractDataMapper;
use App\Model\Match\Geography\Region;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class RegionDataMapper extends AbstractDataMapper
{
    /**
     * @var CountryDataMapper
     */
    private $countryDataMapper;

    public function __construct(
        CountryDataMapper $countryDataMapper
    ) {
        $this->countryDataMapper = $countryDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return Region::class;
    }

    /**
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('country', function () use ($data) {
                return !empty($data['country']) ?
                    $this->countryDataMapper->map($data['country']) :
                    null;
            })
        ;

        return $config;
    }
}
