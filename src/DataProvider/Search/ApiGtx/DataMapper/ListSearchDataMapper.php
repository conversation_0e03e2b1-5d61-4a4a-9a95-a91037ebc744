<?php

declare(strict_types=1);

namespace App\DataProvider\Search\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Searches\ListSearch;
use Carbon\Carbon;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class ListSearchDataMapper extends AbstractDataMapper
{
    /** @var TypologyDataMapper */
    private $typologyDataMapper;

    /** @var ContractDataMapper */
    private $contractDataMapper;

    /** @var IntRangeDataMapper */
    private $intRangeDataMapper;

    /** @var SearchStatusDataMapper */
    private $searchStatusDataMapper;

    /** @var GeographyInformationDataMapper */
    private $geographyInformationDataMapper;

    /** @var CustomerDataMapper */
    private $customerDataMapper;

    public function __construct(
        TypologyDataMapper $typologyDataMapper,
        ContractDataMapper $contractDataMapper,
        IntRangeDataMapper $intRangeDataMapper,
        SearchStatusDataMapper $searchStatusDataMapper,
        GeographyInformationDataMapper $geographyInformationDataMapper,
        CustomerDataMapper $customerDataMapper
    ) {
        $this->typologyDataMapper             = $typologyDataMapper;
        $this->contractDataMapper             = $contractDataMapper;
        $this->intRangeDataMapper             = $intRangeDataMapper;
        $this->searchStatusDataMapper         = $searchStatusDataMapper;
        $this->geographyInformationDataMapper = $geographyInformationDataMapper;
        $this->customerDataMapper             = $customerDataMapper;
    }

    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return ListSearch::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('typologies', function () use ($data) {
                return array_map(function (array $typology) {
                    return $this->typologyDataMapper->map($typology);
                }, $data['typologies'] ?? []);
            })
            ->forMember('contract', function () use ($data) {
                return $this->contractDataMapper->map($data['contract']);
            })
            ->forMember('price', function () use ($data) {
                return null !== $data['price'] ? $this->intRangeDataMapper->map($data['price']) : null;
            })
            ->forMember('surface', function () use ($data) {
                return null !== $data['surface'] ? $this->intRangeDataMapper->map($data['surface']) : null;
            })
            ->forMember('rooms', function () use ($data) {
                return null !== $data['rooms'] ? $this->intRangeDataMapper->map($data['rooms']) : null;
            })
            ->forMember('status', function () use ($data) {
                return $this->searchStatusDataMapper->map($data['status']);
            })
            ->forMember('geographyInformation', function () use ($data) {
                return null !== $data['geographyInformation'] ? $this->geographyInformationDataMapper->map($data['geographyInformation']) : null;
            })
            ->forMember('customer', function () use ($data) {
                return null !== $data['client'] ? $this->customerDataMapper->map($data['client']) : null;
            })
            ->forMember('preferred', function () use ($data) {
                return $data['favourite'] ?? false;
            })
            ->forMember('date', function () use ($data) {
                return Carbon::parse($data['date'], 'UTC')->setTimezone('Europe/Rome');
            })
        ;

        return $config;
    }
}
