<?php

declare(strict_types=1);

namespace App\DataProvider\Agency\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\DTO\DataProvider\Agency\ApiGtx\PaginationRequest;
use App\Model\Agency\MultiAgencyListMembers;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class MultiAgencyListMembersDataMapper extends AbstractDataMapper
{
    const CONTEXT_RESULTS_COUNT      = 'total_count';
    const CONTEXT_PAGINATION_REQUEST = 'pagination_request';

    /** @var AgencyDataMapper */
    private $agencyDataMapper;

    /** @var PaginationDataMapper */
    private $paginationDataMapper;

    public function __construct(
        AgencyDataMapper $agencyDataMapper,
        PaginationDataMapper $paginationDataMapper
    ) {
        $this->agencyDataMapper     = $agencyDataMapper;
        $this->paginationDataMapper = $paginationDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return MultiAgencyListMembers::class;
    }

    /**
     * @param array $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();

        $resultsCount = $this->resolveResultsCountFromContext();

        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('resultsCount', function () use ($resultsCount) {
                return $resultsCount;
            })
            ->forMember('agencies', function () use ($data) {
                return !empty($data) ?
                    array_map(function (array $agency) {
                        return $this->agencyDataMapper->map($agency);
                    }, $data) : [];
            })
            ->forMember('pagination', function () use ($data, $resultsCount) {
                return $this->paginationDataMapper
                    ->withContextData([
                        self::CONTEXT_RESULTS_COUNT      => $resultsCount,
                        self::CONTEXT_PAGINATION_REQUEST => $this->resolvePaginationRequestFromContext(),
                    ])
                    ->map($data);
            })
        ;

        return $config;
    }

    private function resolveResultsCountFromContext(): int
    {
        try {
            return (int) self::getContextDataByKey(self::CONTEXT_RESULTS_COUNT);
        } catch (\Throwable $throwable) {
            return 0;
        }
    }

    private function resolvePaginationRequestFromContext(): PaginationRequest
    {
        try {
            return self::getContextDataByKey(self::CONTEXT_PAGINATION_REQUEST);
        } catch (\Throwable $throwable) {
            return new PaginationRequest();
        }
    }
}
