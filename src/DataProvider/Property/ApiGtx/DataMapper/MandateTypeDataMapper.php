<?php

declare(strict_types=1);

namespace App\DataProvider\Property\ApiGtx\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\Model\Property\MandateType;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class MandateTypeDataMapper extends AbstractDataMapper
{
    /**
     * {@inheritdoc}
     */
    protected function getDestinationClass(): string
    {
        return MandateType::class;
    }

    /**
     * {@inheritdoc}
     */
    protected function getConfiguration($data): ObjectMapperConfig
    {
        /** @var array $data */
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
        ;

        return $config;
    }
}
