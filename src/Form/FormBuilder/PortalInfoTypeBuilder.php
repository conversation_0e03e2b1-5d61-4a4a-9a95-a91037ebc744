<?php

namespace App\Form\FormBuilder;

    use App\Form\PortalInfoType;
    use Symfony\Component\Form\FormFactoryInterface;

    class PortalInfoTypeBuilder
    {
        /** @var \Symfony\Component\Form\FormBuilderInterface */
        private $builder;

        public function __construct(FormFactoryInterface $formFactory)
        {
            $this->builder = $formFactory->createBuilder(PortalInfoType::class);
        }

        /**
         * @param      $child
         * @param null $type
         *
         * @return $this
         */
        public function add($child, $type = null, array $options = []): self
        {
            $this->builder
                ->add($child, $type, $options);

            return $this;
        }

        /**
         * @param     $eventName
         * @param     $listener
         * @param int $priority
         *
         * @return $this
         */
        public function addEventListener($eventName, $listener, $priority = 0): self
        {
            $this->builder
                ->addEventListener($eventName, $listener, $priority);

            return $this;
        }

        public function getForm(): \Symfony\Component\Form\FormInterface
        {
            return $this->builder
                ->getForm();
        }
    }
