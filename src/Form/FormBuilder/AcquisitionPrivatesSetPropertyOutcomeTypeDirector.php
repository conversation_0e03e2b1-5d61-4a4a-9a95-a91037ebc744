<?php

namespace App\Form\FormBuilder;

    use Symfony\Component\Form\Extension\Core\Type\CheckboxType;
    use Symfony\Component\Form\FormEvent;
    use Symfony\Component\Form\FormEvents;

    class AcquisitionPrivatesSetPropertyOutcomeTypeDirector implements FormTypeDirectorInterface
    {
        /**
         * @var AcquisitionPrivatesSetPropertyOutcomeTypeBuilder
         */
        private $builder;
        /**
         * @var AcquisitionPrivatesSetPropertyOutcomeTypeMapping
         */
        private $mapping;

        /**
         * PortalInfoTypeDirector constructor.
         */
        public function __construct(AcquisitionPrivatesSetPropertyOutcomeTypeBuilder $builder, AcquisitionPrivatesSetPropertyOutcomeTypeMapping $mapping)
        {
            $this->builder = $builder;
            $this->mapping = $mapping;
        }

        public function build(array $payload): \Symfony\Component\Form\FormInterface
        {
            $payloadFields = array_keys($payload);
            $fieldsMapping = $this->mapping->getMapping();

            foreach ($payloadFields as $payloadField) {
                if (\array_key_exists($payloadField, $fieldsMapping)) {
                    $this->builder->add($payloadField, $fieldsMapping[$payloadField]['classType'], $fieldsMapping[$payloadField]['options']);
                }
            }

            $this->builder->addEventListener(FormEvents::PRE_SUBMIT, function (FormEvent $event) use ($fieldsMapping) {
                $form = $event->getData();
                $localFields = array_keys($form);

                foreach ($localFields as $field) {
                    if (\array_key_exists($field, $fieldsMapping) && CheckboxType::class === $fieldsMapping[$field]['classType']) {
                        $form[$field] = filter_var($form[$field], \FILTER_VALIDATE_BOOLEAN);
                    }
                }
                $event->setData($form);
            });

            $this->builder->addEventListener(FormEvents::POST_SUBMIT, function (FormEvent $event) {
                $form = $event->getForm()->getData();

                foreach (array_keys((array) $form) as $field) {
                    if (property_exists($form, $field) && null === $form->{$field}) {
                        $form->{$field} = '';
                    }
                }
            });

            return $this->builder->getForm();
        }
    }
