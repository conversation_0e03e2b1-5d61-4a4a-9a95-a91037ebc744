<?php

namespace App\Model\Messaging;

class MessageVisitRequest
{
    /** @var int */
    public $visitType;

    /** @var \DateTime[]|null */
    public $visitDays;

    /** @var string[]|null */
    public $visitTimetables;

    /** @var bool */
    public $atAnyTime;

    /** @var bool */
    public $asSoonAsPossible;

    /** @var string|null */
    public $note;

    /** @return int */
    public function getVisitType(): int
    {
        return $this->visitType;
    }

    /** @param int $visitType */
    public function setVisitType(int $visitType)
    {
        $this->visitType = $visitType;
    }

    /** @return \DateTime[]|null */
    public function getVisitDays()
    {
        return $this->visitDays;
    }

    /** @param \DateTime[]|null $visitDays */
    public function setVisitDays(array $visitDays = null)
    {
        $this->visitDays = $visitDays;
    }

    /** @return string[]|null */
    public function getVisitTimetables()
    {
        return $this->visitTimetables;
    }

    /** @param string[]|null $visitTimetables */
    public function setVisitTimetables(array $visitTimetables = null)
    {
        $this->visitTimetables = $visitTimetables;
    }

    /** @return bool */
    public function isAtAnyTime(): bool
    {
        return $this->atAnyTime;
    }

    /** @param bool $atAnyTime */
    public function setAtAnyTime(bool $atAnyTime)
    {
        $this->atAnyTime = $atAnyTime;
    }

    /** @return bool */
    public function isAsSoonAsPossible(): bool
    {
        return $this->asSoonAsPossible;
    }

    /** @param bool $asSoonAsPossible */
    public function setAsSoonAsPossible(bool $asSoonAsPossible)
    {
        $this->asSoonAsPossible = $asSoonAsPossible;
    }

    /** @return string|null */
    public function getNote()
    {
        return $this->note;
    }

    /** @param string|null $note */
    public function setNote(string $note = null)
    {
        $this->note = $note;
    }
}
