<?php

declare(strict_types=1);

namespace App\Model\Request\Suggestions;

use App\Constants\Property\TypeConstants;
use Symfony\Component\Validator\Constraints as Assert;

class SuggestionsRequest
{
    /**
     * @Assert\NotBlank(message="The search term cannot be empty")
     * @Assert\Length(
     *     min=1,
     *     minMessage="The search term must contain at least {{ limit }} characters"
     * )
     */
    private string $term;

    /**
     * @Assert\NotBlank(message="The search type is mandatory")
     * @Assert\Choice(callback="getValidTypes", message="The selected type is invalid")
     */
    private string $type;

    public function __construct()
    {
        $this->term = '';
        $this->type = '';
    }

    public function getTerm(): string
    {
        return $this->term;
    }

    public function setTerm(string $term): self
    {
        $this->term = $term;

        return $this;
    }

    public function getType(): string
    {
        return $this->type;
    }

    public function setType(string $type): self
    {
        $this->type = $type;

        return $this;
    }

    public static function getValidTypes(): array
    {
        return [
            TypeConstants::ACTIVE,
            TypeConstants::ARCHIVED,
            TypeConstants::DRAFT,
            TypeConstants::SOLD,
        ];
    }
}
