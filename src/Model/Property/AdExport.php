<?php

declare(strict_types=1);

namespace App\Model\Property;

class AdExport
{
    private ?string $code = null;
    private ?int $id = null;
    private ?int $categoryId = null;
    private ?int $typologyId = null;
    private ?string $contractType = null;
    private ?float $surface = null;
    private ?int $priceRangeId = null;
    private ?float $salePrice = null;
    private ?float $rentalPrice = null;
    private ?int $propertyConditionId = null;
    private ?string $rooms = null;
    private ?string $bathrooms = null;
    private ?int $kitchenId = null;
    private ?int $floorId = null;
    private ?int $heatingId = null;
    private ?bool $terrace = null;
    private ?int $garageId = null;
    private ?string $regionName = null;
    private ?string $provinceName = null;
    private ?string $cityName = null;
    private ?string $macroZoneName = null;
    private ?string $microZoneName = null;
    private ?string $postalCode = null;
    private ?string $address = null;
    private ?string $streetNumber = null;
    private ?string $virtualTour = null;
    private ?string $description = null;
    private ?string $notes = null;
    private ?int $visitCount = null;
    private ?int $savedCount = null;
    private ?int $hiddenCount = null;
    private ?int $contactCount = null;
    private ?bool $isPremium = null;
    private ?bool $hasShowcaseVisibility = null;
    private ?bool $hasStarVisibility = null;
    private ?bool $hasTopVisibility = null;
    private ?bool $isGuaranteed = null;
    private ?int $qualityIndex = null;
    private ?string $creationDate = null;
    private ?string $lastModifiedDate = null;
    private ?int $licenseId = null;
    private ?bool $hasWalls = null;
    private ?bool $hasWorkshop = null;
    private ?bool $hasCommercialActivity = null;
    private ?array $buildingUse = null;
    private ?int $adStatusId = null;
    private ?int $searchPosition = null;
    private ?string $agentEmail = null;

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): void
    {
        $this->code = $code;
    }

    public function getId(): ?int
    {
        return $this->id;
    }

    public function setId(?int $id): void
    {
        $this->id = $id;
    }

    public function getCategoryId(): ?int
    {
        return $this->categoryId;
    }

    public function setCategoryId(?int $categoryId): void
    {
        $this->categoryId = $categoryId;
    }

    public function getTypologyId(): ?int
    {
        return $this->typologyId;
    }

    public function setTypologyId(?int $typologyId): void
    {
        $this->typologyId = $typologyId;
    }

    public function getContractType(): ?string
    {
        return $this->contractType;
    }

    public function setContractType(?string $contractType): void
    {
        $this->contractType = $contractType;
    }

    public function getSurface(): ?float
    {
        return $this->surface;
    }

    public function setSurface(?float $surface): void
    {
        $this->surface = $surface;
    }

    public function getPriceRangeId(): ?int
    {
        return $this->priceRangeId;
    }

    public function setPriceRangeId(?int $priceRangeId): void
    {
        $this->priceRangeId = $priceRangeId;
    }

    public function getSalePrice(): ?float
    {
        return $this->salePrice;
    }

    public function setSalePrice(?float $salePrice): void
    {
        $this->salePrice = $salePrice;
    }

    public function getRentalPrice(): ?float
    {
        return $this->rentalPrice;
    }

    public function setRentalPrice(?float $rentalPrice): void
    {
        $this->rentalPrice = $rentalPrice;
    }

    public function getPropertyConditionId(): ?int
    {
        return $this->propertyConditionId;
    }

    public function setPropertyConditionId(?int $propertyConditionId): void
    {
        $this->propertyConditionId = $propertyConditionId;
    }

    public function getRooms(): ?string
    {
        return $this->rooms;
    }

    public function setRooms(?string $rooms): void
    {
        $this->rooms = $rooms;
    }

    public function getBathrooms(): ?string
    {
        return $this->bathrooms;
    }

    public function setBathrooms(?string $bathrooms): void
    {
        $this->bathrooms = $bathrooms;
    }

    public function getKitchenId(): ?int
    {
        return $this->kitchenId;
    }

    public function setKitchenId(?int $kitchenId): void
    {
        $this->kitchenId = $kitchenId;
    }

    public function getFloorId(): ?int
    {
        return $this->floorId;
    }

    public function setFloorId(?int $floorId): void
    {
        $this->floorId = $floorId;
    }

    public function getHeatingId(): ?int
    {
        return $this->heatingId;
    }

    public function setHeatingId(?int $heatingId): void
    {
        $this->heatingId = $heatingId;
    }

    public function getTerrace(): ?bool
    {
        return $this->terrace;
    }

    public function setTerrace(?bool $terrace): void
    {
        $this->terrace = $terrace;
    }

    public function getGarageId(): ?int
    {
        return $this->garageId;
    }

    public function setGarageId(?int $garageId): void
    {
        $this->garageId = $garageId;
    }

    public function getRegionName(): ?string
    {
        return $this->regionName;
    }

    public function setRegionName(?string $regionName): void
    {
        $this->regionName = $regionName;
    }

    public function getProvinceName(): ?string
    {
        return $this->provinceName;
    }

    public function setProvinceName(?string $provinceName): void
    {
        $this->provinceName = $provinceName;
    }

    public function getCityName(): ?string
    {
        return $this->cityName;
    }

    public function setCityName(?string $cityName): void
    {
        $this->cityName = $cityName;
    }

    public function getMacroZoneName(): ?string
    {
        return $this->macroZoneName;
    }

    public function setMacroZoneName(?string $macroZoneName): void
    {
        $this->macroZoneName = $macroZoneName;
    }

    public function getMicroZoneName(): ?string
    {
        return $this->microZoneName;
    }

    public function setMicroZoneName(?string $microZoneName): void
    {
        $this->microZoneName = $microZoneName;
    }

    public function getPostalCode(): ?string
    {
        return $this->postalCode;
    }

    public function setPostalCode(?string $postalCode): void
    {
        $this->postalCode = $postalCode;
    }

    public function getAddress(): ?string
    {
        return $this->address;
    }

    public function setAddress(?string $address): void
    {
        $this->address = $address;
    }

    public function getStreetNumber(): ?string
    {
        return $this->streetNumber;
    }

    public function setStreetNumber(?string $streetNumber): void
    {
        $this->streetNumber = $streetNumber;
    }

    public function getVirtualTour(): ?string
    {
        return $this->virtualTour;
    }

    public function setVirtualTour(?string $virtualTour): void
    {
        $this->virtualTour = $virtualTour;
    }

    public function getDescription(): ?string
    {
        return $this->description;
    }

    public function setDescription(?string $description): void
    {
        $this->description = $description;
    }

    public function getNotes(): ?string
    {
        return $this->notes;
    }

    public function setNotes(?string $notes): void
    {
        $this->notes = $notes;
    }

    public function getVisitCount(): ?int
    {
        return $this->visitCount;
    }

    public function setVisitCount(?int $visitCount): void
    {
        $this->visitCount = $visitCount;
    }

    public function getSavedCount(): ?int
    {
        return $this->savedCount;
    }

    public function setSavedCount(?int $savedCount): void
    {
        $this->savedCount = $savedCount;
    }

    public function getHiddenCount(): ?int
    {
        return $this->hiddenCount;
    }

    public function setHiddenCount(?int $hiddenCount): void
    {
        $this->hiddenCount = $hiddenCount;
    }

    public function getContactCount(): ?int
    {
        return $this->contactCount;
    }

    public function setContactCount(?int $contactCount): void
    {
        $this->contactCount = $contactCount;
    }

    public function getIsPremium(): ?bool
    {
        return $this->isPremium;
    }

    public function setIsPremium(?bool $isPremium): void
    {
        $this->isPremium = $isPremium;
    }

    public function getHasShowcaseVisibility(): ?bool
    {
        return $this->hasShowcaseVisibility;
    }

    public function setHasShowcaseVisibility(?bool $hasShowcaseVisibility): void
    {
        $this->hasShowcaseVisibility = $hasShowcaseVisibility;
    }

    public function getHasStarVisibility(): ?bool
    {
        return $this->hasStarVisibility;
    }

    public function setHasStarVisibility(?bool $hasStarVisibility): void
    {
        $this->hasStarVisibility = $hasStarVisibility;
    }

    public function getHasTopVisibility(): ?bool
    {
        return $this->hasTopVisibility;
    }

    public function setHasTopVisibility(?bool $hasTopVisibility): void
    {
        $this->hasTopVisibility = $hasTopVisibility;
    }

    public function getIsGuaranteed(): ?bool
    {
        return $this->isGuaranteed;
    }

    public function setIsGuaranteed(?bool $isGuaranteed): void
    {
        $this->isGuaranteed = $isGuaranteed;
    }

    public function getQualityIndex(): ?int
    {
        return $this->qualityIndex;
    }

    public function setQualityIndex(?int $qualityIndex): void
    {
        $this->qualityIndex = $qualityIndex;
    }

    public function getCreationDate(): ?string
    {
        return $this->creationDate;
    }

    public function setCreationDate(?string $creationDate): void
    {
        $this->creationDate = $creationDate;
    }

    public function getLastModifiedDate(): ?string
    {
        return $this->lastModifiedDate;
    }

    public function setLastModifiedDate(?string $lastModifiedDate): void
    {
        $this->lastModifiedDate = $lastModifiedDate;
    }

    public function getLicenseId(): ?int
    {
        return $this->licenseId;
    }

    public function setLicenseId(?int $licenseId): void
    {
        $this->licenseId = $licenseId;
    }

    public function getHasWalls(): ?bool
    {
        return $this->hasWalls;
    }

    public function setHasWalls(?bool $hasWalls): void
    {
        $this->hasWalls = $hasWalls;
    }

    public function getHasWorkshop(): ?bool
    {
        return $this->hasWorkshop;
    }

    public function setHasWorkshop(?bool $hasWorkshop): void
    {
        $this->hasWorkshop = $hasWorkshop;
    }

    public function getHasCommercialActivity(): ?bool
    {
        return $this->hasCommercialActivity;
    }

    public function setHasCommercialActivity(?bool $hasCommercialActivity): void
    {
        $this->hasCommercialActivity = $hasCommercialActivity;
    }

    public function getBuildingUse(): ?array
    {
        return $this->buildingUse;
    }

    public function setBuildingUse(?array $buildingUse): void
    {
        $this->buildingUse = $buildingUse;
    }

    public function getAdStatusId(): ?int
    {
        return $this->adStatusId;
    }

    public function setAdStatusId(?int $adStatusId): void
    {
        $this->adStatusId = $adStatusId;
    }

    public function getSearchPosition(): ?int
    {
        return $this->searchPosition;
    }

    public function setSearchPosition(?int $searchPosition): void
    {
        $this->searchPosition = $searchPosition;
    }

    public function getAgentEmail(): ?string
    {
        return $this->agentEmail;
    }

    public function setAgentEmail(?string $agentEmail): void
    {
        $this->agentEmail = $agentEmail;
    }
}
