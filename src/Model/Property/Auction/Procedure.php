<?php

declare(strict_types=1);

namespace App\Model\Property\Auction;

class Procedure
{
    private ?AdType $adType = null;
    private ?Register $register = null;
    private ?BatchCategory $batchCategory = null;
    private ?string $batchNumber = null;
    private ?string $code = null;
    private ?string $court = null;
    private ?string $number = null;
    private ?string $otherOrganization = null;
    private ?string $procedureRitual = null;
    private ?string $referent = null;
    private ?ProcedureType $type = null;
    private ?string $urlPvp = null;
    private ?string $year = null;

    public function getAdType(): ?AdType
    {
        return $this->adType;
    }

    public function setAdType(?AdType $adType): Procedure
    {
        $this->adType = $adType;

        return $this;
    }

    public function getRegister(): ?Register
    {
        return $this->register;
    }

    public function setRegister(?Register $register): Procedure
    {
        $this->register = $register;

        return $this;
    }

    public function getBatchCategory(): ?BatchCategory
    {
        return $this->batchCategory;
    }

    public function setBatchCategory(?BatchCategory $batchCategory): Procedure
    {
        $this->batchCategory = $batchCategory;

        return $this;
    }

    public function getBatchNumber(): ?string
    {
        return $this->batchNumber;
    }

    public function setBatchNumber(?string $batchNumber): Procedure
    {
        $this->batchNumber = $batchNumber;

        return $this;
    }

    public function getCode(): ?string
    {
        return $this->code;
    }

    public function setCode(?string $code): Procedure
    {
        $this->code = $code;

        return $this;
    }

    public function getCourt(): ?string
    {
        return $this->court;
    }

    public function setCourt(?string $court): Procedure
    {
        $this->court = $court;

        return $this;
    }

    public function getNumber(): ?string
    {
        return $this->number;
    }

    public function setNumber(?string $number): Procedure
    {
        $this->number = $number;

        return $this;
    }

    public function getOtherOrganization(): ?string
    {
        return $this->otherOrganization;
    }

    public function setOtherOrganization(?string $otherOrganization): Procedure
    {
        $this->otherOrganization = $otherOrganization;

        return $this;
    }

    public function getProcedureRitual(): ?string
    {
        return $this->procedureRitual;
    }

    public function setProcedureRitual(?string $procedureRitual): Procedure
    {
        $this->procedureRitual = $procedureRitual;

        return $this;
    }

    public function getReferent(): ?string
    {
        return $this->referent;
    }

    public function setReferent(?string $referent): Procedure
    {
        $this->referent = $referent;

        return $this;
    }

    public function getType(): ?ProcedureType
    {
        return $this->type;
    }

    public function setType(?ProcedureType $type): Procedure
    {
        $this->type = $type;

        return $this;
    }

    public function getUrlPvp(): ?string
    {
        return $this->urlPvp;
    }

    public function setUrlPvp(?string $urlPvp): Procedure
    {
        $this->urlPvp = $urlPvp;

        return $this;
    }

    public function getYear(): ?string
    {
        return $this->year;
    }

    public function setYear(?string $year): Procedure
    {
        $this->year = $year;

        return $this;
    }
}
