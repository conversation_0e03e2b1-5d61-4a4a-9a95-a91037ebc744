<?php

namespace App\Model\Estimates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\EstimatesTypes\EstimatePropertyCondition")
 */
class EstimatePropertyCondition
{
    /**
     * @Mapping\Property(name="id", type="string")
     */
    protected $id;
    /**
     * @Mapping\Property(name="name", type="string")
     */
    protected $name;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return mixed
     */
    public function getName()
    {
        return $this->name;
    }

    /**
     * @param mixed $name
     */
    public function setName($name)
    {
        $this->name = $name;
    }
}
