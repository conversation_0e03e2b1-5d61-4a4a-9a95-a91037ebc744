<?php

namespace App\Model\Estimates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\EstimatesTypes\GetDetailResponse")
 */
class GetEstimateDetailResponse
{
    /**
     * @Mapping\OneToOne(name="data", type="App\Model\Estimates\Detail")
     *
     * @var \App\Model\Estimates\Detail
     */
    public $detail;

    public function getDetail(): Detail
    {
        return $this->detail;
    }

    public function setDetail(Detail $detail)
    {
        $this->detail = $detail;
    }
}
