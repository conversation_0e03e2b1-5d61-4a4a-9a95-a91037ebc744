<?php

namespace App\Model\Estimates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\EstimatesTypes\GetDetailMsg")
 */
class GetEstimateDetailRequest
{
    /**
     * @Mapping\Property(name="id", type="string")
     */
    protected $id;

    /**
     * @return mixed
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param mixed $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }
}
