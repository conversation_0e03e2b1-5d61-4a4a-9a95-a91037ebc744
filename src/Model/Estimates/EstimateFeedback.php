<?php

namespace App\Model\Estimates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\EstimatesTypes\EstimateFeedback")
 */
class EstimateFeedback
{
    /**
     * @Mapping\Property(name="opinion", type="integer")
     */
    public $opinion;
    /**
     * @Mapping\Property(name="note", type="string")
     */
    public $note;
    /**
     * @Mapping\Property(name="estimation", type="integer")
     */
    public $estimation;

    /**
     * @return mixed
     */
    public function getOpinion()
    {
        return $this->opinion;
    }

    /**
     * @param mixed $opinion
     */
    public function setOpinion($opinion)
    {
        $this->opinion = $opinion;
    }

    /**
     * @return mixed
     */
    public function getNote()
    {
        return $this->note;
    }

    /**
     * @param mixed $note
     */
    public function setNote($note)
    {
        $this->note = $note;
    }

    /**
     * @return mixed
     */
    public function getEstimation()
    {
        return $this->estimation;
    }

    /**
     * @param mixed $estimation
     */
    public function setEstimation($estimation)
    {
        $this->estimation = $estimation;
    }
}
