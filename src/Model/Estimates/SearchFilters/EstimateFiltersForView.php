<?php

namespace App\Model\Estimates\SearchFilters;

class EstimateFiltersForView
{
    protected $city;
    protected $zones;
    protected $typology;
    protected $surfaceFrom;
    protected $surfaceTo;
    protected $dateFrom;
    protected $dateTo;
    protected $priceFrom;
    protected $priceTo;

    /**
     * @return mixed
     */
    public function getCity()
    {
        return $this->city;
    }

    /**
     * @param mixed $city
     */
    public function setCity($city)
    {
        $this->city = $city;
    }

    /**
     * @return mixed
     */
    public function getZones()
    {
        return $this->zones;
    }

    /**
     * @param mixed $zones
     */
    public function setZones($zones)
    {
        $this->zones = $zones;
    }

    /**
     * @return mixed
     */
    public function getTypology()
    {
        return $this->typology;
    }

    /**
     * @param mixed $typology
     */
    public function setTypology($typology)
    {
        $this->typology = $typology;
    }

    /**
     * @return mixed
     */
    public function getSurfaceFrom()
    {
        return $this->surfaceFrom;
    }

    /**
     * @param mixed $surfaceFrom
     */
    public function setSurfaceFrom($surfaceFrom)
    {
        $this->surfaceFrom = $surfaceFrom;
    }

    /**
     * @return mixed
     */
    public function getSurfaceTo()
    {
        return $this->surfaceTo;
    }

    /**
     * @param mixed $surfaceTo
     */
    public function setSurfaceTo($surfaceTo)
    {
        $this->surfaceTo = $surfaceTo;
    }

    /**
     * @return mixed
     */
    public function getDateFrom()
    {
        return $this->dateFrom;
    }

    /**
     * @param mixed $dateFrom
     */
    public function setDateFrom($dateFrom)
    {
        $this->dateFrom = $dateFrom;
    }

    /**
     * @return mixed
     */
    public function getDateTo()
    {
        return $this->dateTo;
    }

    /**
     * @param mixed $dateTo
     */
    public function setDateTo($dateTo)
    {
        $this->dateTo = $dateTo;
    }

    /**
     * @return mixed
     */
    public function getPriceFrom()
    {
        return $this->priceFrom;
    }

    /**
     * @param mixed $priceFrom
     */
    public function setPriceFrom($priceFrom)
    {
        $this->priceFrom = $priceFrom;
    }

    /**
     * @return mixed
     */
    public function getPriceTo()
    {
        return $this->priceTo;
    }

    /**
     * @param mixed $priceTo
     */
    public function setPriceTo($priceTo)
    {
        $this->priceTo = $priceTo;
    }
}
