<?php

namespace App\Model\Estimates;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\EstimatesTypes\EstimateValue")
 */
class EstimateValue
{
    /**
     * @Mapping\Property(name="price", type="integer")
     */
    public $avg;
    /**
     * @Mapping\Property(name="minPrice", type="integer")
     */
    public $min;
    /**
     * @Mapping\Property(name="maxPrice", type="integer")
     */
    public $max;

    /**
     * @return mixed
     */
    public function getAvg()
    {
        return $this->avg;
    }

    /**
     * @param mixed $avg
     */
    public function setAvg($avg)
    {
        $this->avg = $avg;
    }

    /**
     * @return mixed
     */
    public function getMin()
    {
        return $this->min;
    }

    /**
     * @param mixed $min
     */
    public function setMin($min)
    {
        $this->min = $min;
    }

    /**
     * @return mixed
     */
    public function getMax()
    {
        return $this->max;
    }

    /**
     * @param mixed $max
     */
    public function setMax($max)
    {
        $this->max = $max;
    }
}
