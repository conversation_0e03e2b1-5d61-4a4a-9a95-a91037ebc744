<?php

namespace App\Model\Shared;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\SharedTypes\PaginazioneInfo")
 */
class PaginationResponse
{
    /**
     * @Mapping\Property(name="inizio", type="integer")
     */
    public $start;
    /**
     * @Mapping\Property(name="limite", type="integer")
     */
    public $results;
    /**
     * @Mapping\Property(name="numeroRisultati", type="integer")
     */
    public $total;

    /**
     * @return mixed
     */
    public function getStart()
    {
        return $this->start;
    }

    /**
     * @param mixed $start
     */
    public function setStart($start)
    {
        $this->start = $start;
    }

    /**
     * @return mixed
     */
    public function getResults()
    {
        return $this->results;
    }

    /**
     * @param mixed $results
     */
    public function setResults($results)
    {
        $this->results = $results;
    }

    /**
     * @return mixed
     */
    public function getTotal()
    {
        return $this->total;
    }

    /**
     * @param mixed $total
     */
    public function setTotal($total)
    {
        $this->total = $total;
    }
}
