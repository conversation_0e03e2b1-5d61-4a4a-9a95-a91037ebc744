<?php

namespace App\Model\ApiModel\Agency;

use App\Model\ApiModel\AbstractApiModel;

class AgencyVideoRequest extends AbstractApiModel
{
    /**
     * @var int|null
     */
    private $id;

    /**
     * @var string|null
     */
    private $code;

    /**
     * @var string|null
     */
    private $thumbUrl;

    /**
     * @var int|null
     */
    private $duration;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    public function getCode()
    {
        return $this->code;
    }

    public function setCode(string $code = null)
    {
        $this->code = $code;
    }

    public function getThumbUrl()
    {
        return $this->thumbUrl;
    }

    public function setThumbUrl(string $thumbUrl = null)
    {
        $this->thumbUrl = $thumbUrl;
    }

    public function getDuration()
    {
        return $this->duration;
    }

    public function setDuration(int $duration = null)
    {
        $this->duration = $duration;
    }
}
