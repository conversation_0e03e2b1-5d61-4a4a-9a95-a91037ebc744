<?php

declare(strict_types=1);

namespace App\Model\Match\Geography;

class Country
{
    /**
     * @var string|null
     */
    private $id;

    /**
     * @var string|null
     */
    private $name;

    /**
     * @return string|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(string $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }
}
