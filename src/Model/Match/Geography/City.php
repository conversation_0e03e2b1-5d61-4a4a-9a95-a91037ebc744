<?php

declare(strict_types=1);

namespace App\Model\Match\Geography;

class City
{
    /**
     * @var int|null
     */
    public $id;

    /**
     * @var string|null
     */
    public $name;

    /**
     * @var Province|null
     */
    public $province;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    /**
     * @return Province|null
     */
    public function getProvince()
    {
        return $this->province;
    }

    public function setProvince(Province $province = null)
    {
        $this->province = $province;
    }
}
