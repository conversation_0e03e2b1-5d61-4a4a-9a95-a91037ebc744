<?php

declare(strict_types=1);

namespace App\Model\Response\VirtualTour;

class Coordinate
{
    /** @var float|null */
    private $ath;

    /** @var float|null */
    private $atv;

    /**
     * @return float|null
     */
    public function getAth()
    {
        return $this->ath;
    }

    /**
     * @param float|null $ath
     */
    public function setAth(float $ath)
    {
        $this->ath = $ath;
    }

    /**
     * @return float|null
     */
    public function getAtv()
    {
        return $this->atv;
    }

    /**
     * @param float|null $atv
     */
    public function setAtv(float $atv)
    {
        $this->atv = $atv;
    }
}
