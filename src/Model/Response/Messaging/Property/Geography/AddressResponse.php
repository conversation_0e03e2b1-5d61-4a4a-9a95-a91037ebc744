<?php

namespace App\Model\Response\Messaging\Property\Geography;

class AddressResponse
{
    /** @var string|null */
    private $street;

    /** @var string|null */
    private $number;

    /**
     * @return string|null
     */
    public function getStreet()
    {
        return $this->street;
    }

    public function setStreet(string $street = null)
    {
        $this->street = $street;
    }

    /**
     * @return string|null
     */
    public function getNumber()
    {
        return $this->number;
    }

    public function setNumber(string $number = null)
    {
        $this->number = $number;
    }
}
