<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\AgencyActiveSearch\Geography;

class CityResponse
{
    /**
     * @var int|null
     */
    public $id;

    /**
     * @var string|null
     */
    public $name;

    /**
     * @var ProvinceResponse|null
     */
    public $province;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return string|null
     */
    public function getName()
    {
        return $this->name;
    }

    public function setName(string $name = null)
    {
        $this->name = $name;
    }

    /**
     * @return ProvinceResponse|null
     */
    public function getProvince()
    {
        return $this->province;
    }

    public function setProvince(ProvinceResponse $province = null)
    {
        $this->province = $province;
    }
}
