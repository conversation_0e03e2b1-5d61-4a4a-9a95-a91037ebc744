<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Search;

class PolygonAreaResponse
{
    /**
     * @var CoordinatesResponse[]|null
     */
    private $points;

    /**
     * @return CoordinatesResponse[]|null
     */
    public function getPoints()
    {
        return $this->points;
    }

    /**
     * @param CoordinatesResponse[]|null $points
     */
    public function setPoints(array $points = null)
    {
        $this->points = $points;
    }
}
