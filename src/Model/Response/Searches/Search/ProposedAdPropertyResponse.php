<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Search;

class ProposedAdPropertyResponse
{
    /** @var int */
    private $id;

    /** @var \App\Model\Response\Property\GeographyInformationResponse */
    private $geographyInformation;

    /** @var int|null */
    private $surface;

    /** @var int|null */
    private $rooms;

    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null)
    {
        $this->id = $id;
    }

    /**
     * @return \App\Model\Response\Property\GeographyInformationResponse
     */
    public function getGeographyInformation()
    {
        return $this->geographyInformation;
    }

    /**
     * @param \App\Model\Response\Property\GeographyInformationResponse $geographyInformation
     */
    public function setGeographyInformation(\App\Model\Response\Property\GeographyInformationResponse $geographyInformation = null)
    {
        $this->geographyInformation = $geographyInformation;
    }

    /**
     * @return int|null
     */
    public function getSurface()
    {
        return $this->surface;
    }

    public function setSurface(int $surface = null)
    {
        $this->surface = $surface;
    }

    /**
     * @return int|null
     */
    public function getRooms()
    {
        return $this->rooms;
    }

    public function setRooms(int $rooms = null)
    {
        $this->rooms = $rooms;
    }
}
