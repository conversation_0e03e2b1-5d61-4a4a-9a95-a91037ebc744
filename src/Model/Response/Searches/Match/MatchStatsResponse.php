<?php

declare(strict_types=1);

namespace App\Model\Response\Searches\Match;

class MatchStatsResponse
{
    /** @var int */
    private $total;

    /** @var int */
    private $new;

    public function getTotal(): int
    {
        return $this->total;
    }

    public function setTotal(int $total)
    {
        $this->total = $total;
    }

    public function getNew(): int
    {
        return $this->new;
    }

    public function setNew(int $new)
    {
        $this->new = $new;
    }
}
