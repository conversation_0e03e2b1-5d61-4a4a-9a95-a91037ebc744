<?php

declare(strict_types=1);

namespace App\Model\Response\Zones;

class ZonesResponse
{
    /** @var ZoneResponse|null */
    private $zone;

    /** @var CityResponse|null */
    private $city;

    public function getZone()
    {
        return $this->zone;
    }

    public function setZone(ZoneResponse $zone = null)
    {
        $this->zone = $zone;
    }

    public function getCity()
    {
        return $this->city;
    }

    public function setCity(CityResponse $city = null)
    {
        $this->city = $city;
    }
}
