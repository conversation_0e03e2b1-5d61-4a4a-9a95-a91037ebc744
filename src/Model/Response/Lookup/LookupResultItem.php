<?php

namespace App\Model\Response\Lookup;

class LookupResultItem
{
    /**
     * @var string
     */
    public $value;

    /**
     * @var string
     */
    public $label;

    /**
     * @var string|null
     */
    public $extra;

    public function getValue(): string
    {
        return $this->value;
    }

    /**
     * @return void
     */
    public function setValue(string $value)
    {
        $this->value = $value;
    }

    public function getLabel(): string
    {
        return $this->label;
    }

    /**
     * @return void
     */
    public function setLabel(string $label)
    {
        $this->label = $label;
    }

    /**
     * @return string|null
     */
    public function getExtra()
    {
        return $this->extra;
    }

    /**
     * @param string|null
     *
     * @return void
     */
    public function setExtra($extra)
    {
        $this->extra = $extra;
    }
}
