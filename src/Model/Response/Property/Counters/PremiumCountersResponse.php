<?php

declare(strict_types=1);

namespace App\Model\Response\Property\Counters;

class PremiumCountersResponse
{
    private SoldCountersResponse $sale;
    private RentCountersResponse $rent;
    private ?SoldCountersResponse $newConstruction = null;

    public function getSale(): SoldCountersResponse
    {
        return $this->sale;
    }

    public function setSale(SoldCountersResponse $sale): void
    {
        $this->sale = $sale;
    }

    public function getRent(): RentCountersResponse
    {
        return $this->rent;
    }

    public function setRent(RentCountersResponse $rent): void
    {
        $this->rent = $rent;
    }

    public function getNewConstruction(): ?SoldCountersResponse
    {
        return $this->newConstruction;
    }

    public function setNewConstruction(?SoldCountersResponse $newConstruction): void
    {
        $this->newConstruction = $newConstruction;
    }
}
