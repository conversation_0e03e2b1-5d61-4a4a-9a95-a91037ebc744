<?php

namespace App\Model\Response\Property;

class IndustrialResponse
{
    /** @var string|null */
    private $shedHeight;

    /** @var string|null */
    private $subbeamHeight;

    /** @var string|null */
    private $pillars;

    /** @var int|null */
    private $loadingDocks;

    /** @var int|null */
    private $cranes;

    /** @var string|null */
    private $loadCapacity;

    /** @var int|null */
    private $spans;

    /** @var string|null */
    private $officeSurface;

    public function getShedHeight()
    {
        return $this->shedHeight;
    }

    public function setShedHeight(string $shedHeight = null)
    {
        $this->shedHeight = $shedHeight;
    }

    public function getSubbeamHeight()
    {
        return $this->subbeamHeight;
    }

    public function setSubbeamHeight(string $subbeamHeight = null)
    {
        $this->subbeamHeight = $subbeamHeight;
    }

    public function getPillars()
    {
        return $this->pillars;
    }

    public function setPillars(string $pillars = null)
    {
        $this->pillars = $pillars;
    }

    public function getLoadingDocks()
    {
        return $this->loadingDocks;
    }

    public function setLoadingDocks(int $loadingDocks = null)
    {
        $this->loadingDocks = $loadingDocks;
    }

    public function getCranes()
    {
        return $this->cranes;
    }

    public function setCranes(int $cranes = null)
    {
        $this->cranes = $cranes;
    }

    public function getLoadCapacity()
    {
        return $this->loadCapacity;
    }

    public function setLoadCapacity(string $loadCapacity = null)
    {
        $this->loadCapacity = $loadCapacity;
    }

    public function getSpans()
    {
        return $this->spans;
    }

    public function setSpans(int $spans = null)
    {
        $this->spans = $spans;
    }

    public function getOfficeSurface()
    {
        return $this->officeSurface;
    }

    public function setOfficeSurface(string $officeSurface = null)
    {
        $this->officeSurface = $officeSurface;
    }
}
