<?php

declare(strict_types=1);

namespace App\Model\Response\Property;

class AdStatsItemResponse
{
    private ?int $adId;
    private ?AdStats $stats;

    public function getAdId(): ?int
    {
        return $this->adId;
    }

    public function setAdId(?int $adId = null)
    {
        $this->adId = $adId;
    }

    public function getStats(): ?AdStats
    {
        return $this->stats;
    }

    public function setStats(?AdStats $stats = null)
    {
        $this->stats = $stats;
    }
}
