<?php

namespace App\Model\Lookup\InternationalPhonePrefixes;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\LookupsTypes\InternationalPhonePrefix")
 */
class InternationalPhonePrefixItem
{
    /**
     * @Mapping\Property(name="id", type="int")
     */
    public $id;
    /**
     * @Mapping\Property(name="phonePrefix", type="string")
     */
    public $phonePrefix;
    /**
     * @Mapping\Property(name="countryName", type="string")
     */
    public $countryName;
    /**
     * @Mapping\Property(name="countryShortCode", type="string")
     */
    public $phoncountryShortCodeePrefix;

    /**
     * @return int
     */
    public function getId()
    {
        return $this->id;
    }

    /**
     * @param int $id
     */
    public function setId($id)
    {
        $this->id = $id;
    }

    /**
     * @return string
     */
    public function getPhonePrefix()
    {
        return $this->phonePrefix;
    }

    /**
     * @param string $phonePrefix
     */
    public function setPhonePrefix($phonePrefix)
    {
        $this->phonePrefix = $phonePrefix;
    }

    /**
     * @return string
     */
    public function getCountryName()
    {
        return $this->countryName;
    }

    /**
     * @param string $countryName
     */
    public function setCountryName($countryName)
    {
        $this->countryName = $countryName;
    }

    /**
     * @return string
     */
    public function getCountryShortCode()
    {
        return $this->countryShortCode;
    }

    /**
     * @param string $countryShortCode
     */
    public function setCountryShortCode($countryShortCode)
    {
        $this->countryShortCode = $countryShortCode;
    }
}
