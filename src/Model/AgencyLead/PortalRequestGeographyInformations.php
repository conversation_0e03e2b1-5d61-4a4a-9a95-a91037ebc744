<?php

namespace App\Model\AgencyLead;

use App\Model\Geography\City;
use App\Model\Geography\Province;
use App\Model\Geography\Region;

class PortalRequestGeographyInformations
{
    /** @var Region|null */
    private $region;

    /** @var Province|null */
    private $province;

    /** @var City|null */
    private $city;

    public function getRegion()
    {
        return $this->region;
    }

    public function setRegion(Region $region = null)
    {
        $this->region = $region;

        return $this;
    }

    public function getProvince()
    {
        return $this->province;
    }

    public function setProvince(Province $province = null)
    {
        $this->province = $province;

        return $this;
    }

    public function getCity()
    {
        return $this->city;
    }

    public function setCity(City $city = null)
    {
        $this->city = $city;
    }
}
