<?php

namespace App\Model\Login;

use App\Model\AbstractJsonSerializableModel;

class SsoLogin extends AbstractJsonSerializableModel
{
    /** @var int|null */
    private $exp;

    /** @var string|null */
    private $username;

    /** @var string|null */
    private $source;

    /** @var string|null */
    private $backurl;

    /** @var bool|null */
    private $skip2FA;

    /** @var string|null */
    private $session2FAUuid;

    /** @var int|null */
    private $session2FAExpitarion;

    /** @var array|null */
    private $metadata;

    /** @var string|null */
    private $appId;

    /** @var string|null */
    private $productUsername;

    /** @return int|null */
    public function getExp()
    {
        return $this->exp;
    }

    public function setExp(int $exp = null)
    {
        $this->exp = $exp;

        return $this;
    }

    /** @return string|null */
    public function getUsername()
    {
        return $this->username;
    }

    public function setUsername(string $username = null)
    {
        $this->username = $username;
    }

    /** @return string|null */
    public function getSource()
    {
        return $this->source;
    }

    public function setSource(string $source = null)
    {
        $this->source = $source;
    }

    /** @return string|null */
    public function getBackurl()
    {
        return $this->backurl;
    }

    public function setBackurl(string $backurl = null)
    {
        $this->backurl = $backurl;
    }

    /** @return bool|null */
    public function getSkip2FA()
    {
        return $this->skip2FA;
    }

    public function setSkip2FA(bool $skip2FA = null)
    {
        $this->skip2FA = $skip2FA;
    }

    /** @return string|null */
    public function getSession2FAUuid()
    {
        return $this->session2FAUuid;
    }

    public function setSession2FAUuid(string $session2FAUuid = null)
    {
        $this->session2FAUuid = $session2FAUuid;
    }

    /** @return int|null */
    public function getSession2FAExpitarion()
    {
        return $this->session2FAExpitarion;
    }

    public function setSession2FAExpitarion(int $session2FAExpitarion = null)
    {
        $this->session2FAExpitarion = $session2FAExpitarion;
    }

    /** @return array|null */
    public function getMetadata()
    {
        return $this->metadata;
    }

    public function setMetadata(array $metadata = null)
    {
        $this->metadata = $metadata;
    }

    /** @return string|null */
    public function getAppId()
    {
        return $this->appId;
    }

    public function setAppId(string $appId = null)
    {
        $this->appId = $appId;
    }

    /** @return string|null */
    public function getProductUsername()
    {
        return $this->productUsername;
    }

    public function setProductUsername(string $productUsername = null)
    {
        $this->productUsername = $productUsername;
    }
}
