<?php

declare(strict_types=1);

namespace App\Model\Agency;

class MultiAgency
{
    /** @var bool|null */
    private $isMaster;

    /** @var int|null */
    private $masterId;

    /** @return bool|null */
    public function getIsMaster()
    {
        return $this->isMaster;
    }

    public function setIsMaster(bool $isMaster = null)
    {
        $this->isMaster = $isMaster;
    }

    /** @return int|null */
    public function getMasterId()
    {
        return $this->masterId;
    }

    public function setMasterId(int $masterId = null)
    {
        $this->masterId = $masterId;
    }
}
