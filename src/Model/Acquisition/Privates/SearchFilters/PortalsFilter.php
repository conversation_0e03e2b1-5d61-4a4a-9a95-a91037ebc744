<?php

namespace App\Model\Acquisition\Privates\SearchFilters;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AnnunciTypes\FiltroPortali")
 */
class PortalsFilter
{
    /**
     * @Mapping\Property(name="portale", type="integer")
     */
    public $portal;

    /**
     * @return mixed
     */
    public function getPortal()
    {
        return $this->portal;
    }

    /**
     * @param mixed $portal
     */
    public function setPortal($portal)
    {
        $this->portal = $portal;
    }
}
