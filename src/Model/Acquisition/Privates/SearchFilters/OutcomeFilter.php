<?php

namespace App\Model\Acquisition\Privates\SearchFilters;

use App\DataTransformer\Mapping;

/**
 * @Mapping\Struct(name="\Services\Getrix\AcquisizioneTypes\FiltroEsito")
 */
class OutcomeFilter
{
    /**
     * @Mapping\Property(name="esito", type="integer")
     */
    public $outcome;

    /**
     * @return mixed
     */
    public function getOutcome()
    {
        return $this->outcome;
    }

    /**
     * @param mixed $outcome
     */
    public function setOutcome($outcome)
    {
        $this->outcome = $outcome;
    }
}
