<?php

declare(strict_types=1);

namespace App\Utils;

class ImmobiliareSendMail
{
    private function me_get_file_extension(string $file): string
    {
        $ext = explode('.', $file);
        if (isset($ext[1])) {
            return $ext[1];
        } else {
            return '';
        }
    }

    private function me_get_mimetype(string $filePath = ''): string
    {
        $ct['htm']   = 'text/html';
        $ct['html']  = 'text/html';
        $ct['txt']   = 'text/plain';
        $ct['asc']   = 'text/plain';
        $ct['bmp']   = 'image/bmp';
        $ct['gif']   = 'image/gif';
        $ct['jpeg']  = 'image/jpeg';
        $ct['jpg']   = 'image/jpeg';
        $ct['jpe']   = 'image/jpeg';
        $ct['png']   = 'image/png';
        $ct['ico']   = 'image/vnd.microsoft.icon';
        $ct['mpeg']  = 'video/mpeg';
        $ct['mpg']   = 'video/mpeg';
        $ct['mpe']   = 'video/mpeg';
        $ct['qt']    = 'video/quicktime';
        $ct['mov']   = 'video/quicktime';
        $ct['avi']   = 'video/x-msvideo';
        $ct['wmv']   = 'video/x-ms-wmv';
        $ct['mp2']   = 'audio/mpeg';
        $ct['mp3']   = 'audio/mpeg';
        $ct['rm']    = 'audio/x-pn-realaudio';
        $ct['ram']   = 'audio/x-pn-realaudio';
        $ct['rpm']   = 'audio/x-pn-realaudio-plugin';
        $ct['ra']    = 'audio/x-realaudio';
        $ct['wav']   = 'audio/x-wav';
        $ct['css']   = 'text/css';
        $ct['zip']   = 'application/zip';
        $ct['pdf']   = 'application/pdf';
        $ct['doc']   = 'application/msword';
        $ct['bin']   = 'application/octet-stream';
        $ct['exe']   = 'application/octet-stream';
        $ct['class'] = 'application/octet-stream';
        $ct['dll']   = 'application/octet-stream';
        $ct['xls']   = 'application/vnd.ms-excel';
        $ct['ppt']   = 'application/vnd.ms-powerpoint';
        $ct['wbxml'] = 'application/vnd.wap.wbxml';
        $ct['wmlc']  = 'application/vnd.wap.wmlc';
        $ct['wmlsc'] = 'application/vnd.wap.wmlscriptc';
        $ct['dvi']   = 'application/x-dvi';
        $ct['spl']   = 'application/x-futuresplash';
        $ct['gtar']  = 'application/x-gtar';
        $ct['gzip']  = 'application/x-gzip';
        $ct['js']    = 'application/x-javascript';
        $ct['json']  = 'application/json';
        $ct['swf']   = 'application/x-shockwave-flash';
        $ct['tar']   = 'application/x-tar';
        $ct['xhtml'] = 'application/xhtml+xml';
        $ct['csv']   = 'application/csv';
        $ct['au']    = 'audio/basic';
        $ct['snd']   = 'audio/basic';
        $ct['midi']  = 'audio/midi';
        $ct['mid']   = 'audio/midi';
        $ct['m3u']   = 'audio/x-mpegurl';
        $ct['tiff']  = 'image/tiff';
        $ct['tif']   = 'image/tiff';
        $ct['rtf']   = 'text/rtf';
        $ct['wml']   = 'text/vnd.wap.wml';
        $ct['wmls']  = 'text/vnd.wap.wmlscript';
        $ct['xsl']   = 'text/xml';
        $ct['xml']   = 'text/xml';

        $extension = $this->me_get_file_extension($filePath);
        if (!$type = $ct[strtolower($extension)]) {
            $type = 'application/octet-stream';
        }

        return $type;
    }

    public function html2text(string $badStr): string
    {
        //remove PHP if it exists
        while (substr_count($badStr, '<' . '?') && substr_count($badStr, '?' . '>') && strpos($badStr, '?' . '>', strpos($badStr, '<' . '?')) > strpos($badStr, '<' . '?')) {
            $badStr = substr($badStr, 0, strpos($badStr, '<' . '?')) . substr($badStr, strpos($badStr, '?' . '>', strpos($badStr, '<' . '?')) + 2);
        }
        //remove comments
        while (substr_count($badStr, '<!--') && substr_count($badStr, '-->') && strpos($badStr, '-->', strpos($badStr, '<!--')) > strpos($badStr, '<!--')) {
            $badStr = substr($badStr, 0, strpos($badStr, '<!--')) . substr($badStr, strpos($badStr, '-->', strpos($badStr, '<!--')) + 3);
        }
        //now make sure all HTML tags are correctly written (> not in between quotes)
        for ($x = 0, $goodStr = '', $is_open_tb = false, $is_open_sq = false, $is_open_dq = false; @\strlen($chr = $badStr[$x]); ++$x) {
            //take each letter in turn and check if that character is permitted there
            switch ($chr) {
                case '<':
                    if (!$is_open_tb && 'style' == strtolower(substr($badStr, $x + 1, 5))) {
                        $badStr = substr($badStr, 0, $x) . substr($badStr, strpos(strtolower($badStr), '</style>', $x) + 7);
                        $chr    = '';
                    } elseif (!$is_open_tb && 'script' == strtolower(substr($badStr, $x + 1, 6))) {
                        $badStr = substr($badStr, 0, $x) . substr($badStr, strpos(strtolower($badStr), '</script>', $x) + 8);
                        $chr    = '';
                    } elseif (!$is_open_tb) {
                        $is_open_tb = true;
                    } else {
                        $chr = '&lt;';
                    }
                    break;
                case '>':
                    if (!$is_open_tb || $is_open_dq || $is_open_sq) {
                        $chr = '&gt;';
                    } else {
                        $is_open_tb = false;
                    }
                    break;
                case '"':
                    if ($is_open_tb && !$is_open_dq && !$is_open_sq) {
                        $is_open_dq = true;
                    } elseif ($is_open_tb && $is_open_dq && !$is_open_sq) {
                        $is_open_dq = false;
                    } else {
                        $chr = '&quot;';
                    }
                    break;
                case "'":
                    if ($is_open_tb && !$is_open_dq && !$is_open_sq) {
                        $is_open_sq = true;
                    } elseif ($is_open_tb && !$is_open_dq && $is_open_sq) {
                        $is_open_sq = false;
                    }
            }
            $goodStr .= $chr;
        }
        //now that the page is valid (I hope) for strip_tags, strip all unwanted tags
        $goodStr = strip_tags($goodStr, '<title><hr><h1><h2><h3><h4><h5><h6><div><p><pre><sup><ul><ol><br><dl><dt><table><caption><tr><li><dd><th><td><a><area><img><form><input><textarea><button><select><option>');
        //strip extra whitespace except between <pre> and <textarea> tags
        $badStr = preg_split("/<\/?pre[^>]*>/i", $goodStr);
        for ($x = 0; @\is_string($badStr[$x]); ++$x) {
            if ($x % 2) {
                $badStr[$x] = '<pre>' . $badStr[$x] . '</pre>';
            } else {
                $goodStr = preg_split("/<\/?textarea[^>]*>/i", $badStr[$x]);
                for ($z = 0; @\is_string($goodStr[$z]); ++$z) {
                    if ($z % 2) {
                        $goodStr[$z] = '<textarea>' . $goodStr[$z] . '</textarea>';
                    } else {
                        $goodStr[$z] = preg_replace("/\s+/", ' ', $goodStr[$z]);
                    }
                }
                $badStr[$x] = implode('', $goodStr);
            }
        }
        $goodStr = implode('', $badStr);
        //remove all options from select inputs
        $goodStr = preg_replace('/<option[^>]*>[^<]*/i', '', $goodStr);
        //replace all tags with their text equivalents
        $goodStr = preg_replace("/<(\/title|hr)[^>]*>/i", "\n          --------------------\n", $goodStr);
        $goodStr = preg_replace('/<(h|div|p)[^>]*>/i', "\n\n", $goodStr);
        $goodStr = preg_replace('/<sup[^>]*>/i', '^', $goodStr);
        $goodStr = preg_replace("/<(ul|ol|br|dl|dt|table|caption|\/textarea|tr[^>]*>\s*<(td|th))[^>]*>/i", "\n", $goodStr);
        $goodStr = preg_replace('/<li[^>]*>/i', "\n· ", $goodStr);
        $goodStr = preg_replace('/<dd[^>]*>/i', "\n\t", $goodStr);
        $goodStr = preg_replace('/<(th|td)[^>]*>/i', "\t", $goodStr);
        $goodStr = preg_replace("/<a[^>]* href=(\"((?!\"|#|javascript:)[^\"#]*)(\"|#)|'((?!'|#|javascript:)[^'#]*)('|#)|((?!'|\"|>|#|javascript:)[^#\"'> ]*))[^>]*>/i", '[LINK: $2$4$6] ', $goodStr);
        $goodStr = preg_replace("/<img[^>]* alt=(\"([^\"]+)\"|'([^']+)'|([^\"'> ]+))[^>]*>/i", '[IMAGE: $2$3$4] ', $goodStr);
        $goodStr = preg_replace("/<form[^>]* action=(\"([^\"]+)\"|'([^']+)'|([^\"'> ]+))[^>]*>/i", "\n[FORM: $2$3$4] ", $goodStr);
        $goodStr = preg_replace('/<(input|textarea|button|select)[^>]*>/i', '[INPUT] ', $goodStr);
        //strip all remaining tags (mostly closing tags)
        $goodStr = strip_tags($goodStr);
        //convert HTML entities
        $goodStr = strtr($goodStr, array_flip(get_html_translation_table(\HTML_ENTITIES)));
        //$goodStr = preg_replace("/&#(\d+);/me", "chr('$1')", $goodStr);
        //wordwrap
        $goodStr = wordwrap($goodStr);
        //make sure there are no more than 3 linebreaks in a row and trim whitespace
        return utf8_encode(preg_replace("/^\n*|\n*$/", '', preg_replace("/[ \t]+(\n|$)/", '$1', preg_replace("/\n(\s*\n){2}/", "\n\n\n", preg_replace("/\r\n?|\f/", "\n", str_replace(\chr(160), ' ', $goodStr))))));
    }
}
