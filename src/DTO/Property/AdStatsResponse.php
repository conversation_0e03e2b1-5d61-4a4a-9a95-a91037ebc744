<?php

declare(strict_types=1);

namespace App\DTO\Property;

class AdStatsResponse
{
    private ?AdStatsDataResponse $impressions;
    private ?AdStatsDataResponse $mailAlertImpressions;
    private ?AdStatsDataResponse $clientDetailViews;
    private ?AdStatsDataResponse $phoneClicks;
    private ?AdStatsDataResponse $leads;
    private ?AdStatsDataResponse $visitRequests;
    private ?AdStatsDataResponse $bookmarks;
    private ?AdStatsDataResponse $blacklists;
    private ?AdStatsDataResponse $shares;
    private ?AdStatsDataResponse $mapPinSelected;
    private ?AdStatsDataResponse $priceProposals;
    private ?AdStatsDataResponse $carouselListingChanged;
    private ?array $performanceIndexes;

    public function getImpressions(): ?AdStatsDataResponse
    {
        return $this->impressions;
    }

    public function setImpressions(?AdStatsDataResponse $impressions = null)
    {
        $this->impressions = $impressions;
    }

    public function getMailAlertImpressions(): ?AdStatsDataResponse
    {
        return $this->mailAlertImpressions;
    }

    public function setMailAlertImpressions(?AdStatsDataResponse $mailAlertImpressions = null)
    {
        $this->mailAlertImpressions = $mailAlertImpressions;
    }

    public function getClientDetailViews(): ?AdStatsDataResponse
    {
        return $this->clientDetailViews;
    }

    public function setClientDetailViews(?AdStatsDataResponse $clientDetailViews = null)
    {
        $this->clientDetailViews = $clientDetailViews;
    }

    public function getPhoneClicks(): ?AdStatsDataResponse
    {
        return $this->phoneClicks;
    }

    public function setPhoneClicks(?AdStatsDataResponse $phoneClicks = null)
    {
        $this->phoneClicks = $phoneClicks;
    }

    public function getLeads(): ?AdStatsDataResponse
    {
        return $this->leads;
    }

    public function setLeads(?AdStatsDataResponse $leads = null)
    {
        $this->leads = $leads;
    }

    public function getVisitRequests(): ?AdStatsDataResponse
    {
        return $this->visitRequests;
    }

    public function setVisitRequests(?AdStatsDataResponse $visitRequests = null)
    {
        $this->visitRequests = $visitRequests;
    }

    public function getBookmarks(): ?AdStatsDataResponse
    {
        return $this->bookmarks;
    }

    public function setBookmarks(?AdStatsDataResponse $bookmarks = null)
    {
        $this->bookmarks = $bookmarks;
    }

    public function getBlacklists(): ?AdStatsDataResponse
    {
        return $this->blacklists;
    }

    public function setBlacklists(?AdStatsDataResponse $blacklists = null)
    {
        $this->blacklists = $blacklists;
    }

    public function getShares(): ?AdStatsDataResponse
    {
        return $this->shares;
    }

    public function setShares(?AdStatsDataResponse $shares = null)
    {
        $this->shares = $shares;
    }

    public function getMapPinSelected(): ?AdStatsDataResponse
    {
        return $this->mapPinSelected;
    }

    public function setMapPinSelected(?AdStatsDataResponse $mapPinSelected = null)
    {
        $this->mapPinSelected = $mapPinSelected;
    }

    public function getPriceProposals(): ?AdStatsDataResponse
    {
        return $this->priceProposals;
    }

    public function setPriceProposals(?AdStatsDataResponse $priceProposals = null)
    {
        $this->priceProposals = $priceProposals;
    }

    public function getCarouselListingChanged(): ?AdStatsDataResponse
    {
        return $this->carouselListingChanged;
    }

    public function setCarouselListingChanged(?AdStatsDataResponse $carouselListingChanged = null)
    {
        $this->carouselListingChanged = $carouselListingChanged;
    }

    /** @return array|null */
    public function getPerformanceIndexes(): ?array
    {
        return $this->performanceIndexes;
    }

    public function setPerformanceIndexes(?array $performanceIndexes = null)
    {
        $this->performanceIndexes = $performanceIndexes;
    }
}
