<?php

namespace App\DTO\Messaging;

use App\Helper\ListHelper;
use Symfony\Component\HttpFoundation\Request;

class ThreadPaginationRequest
{
    const LIMIT_DEFAULT  = 30;
    const OFFSET_DEFAULT = 1;

    const OFFSET = 'offset';
    const LIMIT  = 'limit';

    /** @var int */
    private $page;

    /** @var int */
    private $offset;

    /** @var int */
    private $limit;

    public function __construct(int $page, int $offset, int $limit)
    {
        $this->page   = $page;
        $this->offset = $offset;
        $this->limit  = $limit;
    }

    public function getPage(): int
    {
        return $this->page;
    }

    public function getOffset(): int
    {
        return $this->offset;
    }

    public function getLimit(): int
    {
        return $this->limit;
    }

    public static function createFromRequest(
        Request $request
    ): self {
        $page             = $request->query->get('page') ?? self::OFFSET_DEFAULT;
        $limit            = $request->query->get('results') ?? self::LIMIT_DEFAULT;
        $paginationOffset = ListHelper::getOffset($page, $limit);

        return new self(
            $page,
            $paginationOffset,
            $limit
        );
    }

    public function toArray(): array
    {
        return [
            self::OFFSET => $this->getOffset(),
            self::LIMIT  => $this->getLimit(),
        ];
    }
}
