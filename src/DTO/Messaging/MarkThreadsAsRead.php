<?php

declare(strict_types=1);

namespace App\DTO\Messaging;

class MarkThreadsAsRead
{
    /** @var int[]|null */
    private $threadIds;

    /**
     * @return int[]|null
     */
    public function getThreadIds()
    {
        return $this->threadIds;
    }

    /**
     * @param int[]|null $threadIds
     */
    public function setThreadIds(array $threadIds = [])
    {
        $this->threadIds = $threadIds;
    }
}
