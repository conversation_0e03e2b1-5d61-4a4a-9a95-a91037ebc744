<?php

declare(strict_types=1);

namespace App\DTO\Messaging;

use App\Resolver\RequestParameterResolver;
use Symfony\Component\HttpFoundation\Request;

class ThreadsFilters
{
    /** @var string|null */
    private $fullName;

    /** @var string|null */
    private $email;

    /*** @var string|null */
    private $code;

    /** @var bool|null */
    private $unread;

    /** @var bool|null */
    private $favourite;

    /** @var bool|null */
    private $archived;

    public function __construct(
        string $fullName = null,
        string $email = null,
        string $code = null,
        bool $unread = null,
        bool $favourite = null,
        bool $archived = null
    ) {
        $this->fullName  = $fullName;
        $this->email     = $email;
        $this->code      = $code;
        $this->unread    = $unread;
        $this->favourite = $favourite;
        $this->archived  = $archived;
    }

    /**
     * @return string|null
     */
    public function getFullName()
    {
        return $this->fullName;
    }

    /**
     * @return string|null
     */
    public function getEmail()
    {
        return $this->email;
    }

    /**
     * @return string|null
     */
    public function getCode()
    {
        return $this->code;
    }

    /**
     * @return bool|null
     */
    public function getUnread()
    {
        return $this->unread;
    }

    /**
     * @return bool|null
     */
    public function getFavourite()
    {
        return $this->favourite;
    }

    /**
     * @return bool|null
     */
    public function getArchived()
    {
        return $this->archived;
    }

    public static function createFromRequest(Request $request): self
    {
        return new self(
            $request->query->has(RequestParameterResolver::FULL_NAME) && !empty($request->query->get(RequestParameterResolver::FULL_NAME))
                ? $request->query->get(RequestParameterResolver::FULL_NAME)
                : null,
            $request->query->has(RequestParameterResolver::EMAIL) && !empty($request->query->get(RequestParameterResolver::EMAIL))
                ? $request->query->get(RequestParameterResolver::EMAIL)
                : null,
            $request->query->has(RequestParameterResolver::CODE) && !empty($request->query->get(RequestParameterResolver::CODE))
                ? $request->query->get(RequestParameterResolver::CODE)
                : null,
            $request->query->has(RequestParameterResolver::UNREAD) && true === filter_var($request->query->get(RequestParameterResolver::UNREAD), \FILTER_VALIDATE_BOOLEAN)
                ? filter_var($request->query->get(RequestParameterResolver::UNREAD), \FILTER_VALIDATE_BOOLEAN)
                : null,
            $request->query->has(RequestParameterResolver::PREFERRED) && true === filter_var($request->query->get(RequestParameterResolver::PREFERRED), \FILTER_VALIDATE_BOOLEAN)
                ? filter_var($request->query->get(RequestParameterResolver::PREFERRED), \FILTER_VALIDATE_BOOLEAN)
                : null,
            $request->query->has(RequestParameterResolver::STATUS) && $request->query->get(RequestParameterResolver::STATUS) === RequestParameterResolver::STATUS_ARCHIVED
        );
    }

    public function toArray(): array
    {
        return [
            RequestParameterResolver::FULL_NAME       => $this->getFullName(),
            RequestParameterResolver::EMAIL           => $this->getEmail(),
            RequestParameterResolver::CODE            => $this->getCode(),
            RequestParameterResolver::UNREAD          => $this->getUnread(),
            RequestParameterResolver::FAVOURITE       => $this->getFavourite(),
            RequestParameterResolver::STATUS_ARCHIVED => $this->getArchived(),
        ];
    }

    public function toArrayResult(): array
    {
        if (null !== $this->getArchived() && false === $this->getArchived()) {
            $status = RequestParameterResolver::STATUS_ACTIVE;
        } elseif (null !== $this->getArchived() && true === $this->getArchived()) {
            $status = RequestParameterResolver::STATUS_ARCHIVED;
        } else {
            $status = null;
        }

        return [
            RequestParameterResolver::CODE      => $this->getCode() ?? '',
            RequestParameterResolver::EMAIL     => $this->getEmail() ?? '',
            RequestParameterResolver::FULL_NAME => $this->getFullName() ?? '',
            RequestParameterResolver::UNREAD    => $this->getUnread(),
            RequestParameterResolver::PREFERRED => $this->getFavourite(),
            RequestParameterResolver::STATUS    => $status,
        ];
    }
}
