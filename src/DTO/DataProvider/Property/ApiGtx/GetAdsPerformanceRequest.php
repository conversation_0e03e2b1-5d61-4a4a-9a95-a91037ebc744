<?php

declare(strict_types=1);

namespace App\DTO\DataProvider\Property\ApiGtx;

class GetAdsPerformanceRequest
{
    /**
     * @var string
     */
    private $adIds;

    /**
     * @var string|null
     */
    private $kpi;

    public function getAdIds(): string
    {
        return $this->adIds;
    }

    public function setAdIds(
        string $adIds
    ) {
        $this->adIds = $adIds;
    }

    /**
     * @return string|null
     */
    public function getKpi()
    {
        return $this->kpi;
    }

    public function setKpi(
        string $kpi = null
    ) {
        $this->kpi = $kpi;
    }
}
