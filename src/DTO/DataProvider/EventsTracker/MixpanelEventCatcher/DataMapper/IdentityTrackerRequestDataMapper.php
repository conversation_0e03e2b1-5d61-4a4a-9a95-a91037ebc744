<?php

declare(strict_types=1);

namespace App\DTO\DataProvider\EventsTracker\MixpanelEventCatcher\DataMapper;

use App\DataMapper\AbstractDataMapper;
use App\DTO\DataProvider\EventsTracker\MixpanelEventCatcher\IdentifyTrackerRequest;
use App\Handler\EventsTracker\EventsTrackerHandler;
use ObjectMapper\Configuration\ObjectMapperConfig;

class IdentityTrackerRequestDataMapper extends AbstractDataMapper
{
    private IdentityTrackerMessageRequestDataMapper $identityTrackerMessageRequestDataMapper;

    public function __construct(
        IdentityTrackerMessageRequestDataMapper $identityTrackerMessageRequestDataMapper
    ) {
        $this->identityTrackerMessageRequestDataMapper = $identityTrackerMessageRequestDataMapper;
    }

    protected function getDestinationClass(): string
    {
        return IdentifyTrackerRequest::class;
    }

    /**
     * @param \App\Model\Request\EventsTracker\IdentifyTrackerRequest $data
     */
    protected function getConfiguration(
        $data
    ): ObjectMapperConfig {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(\get_class($data), $this->getDestinationClass())
            ->forMember('distinctId', function () {
                return $this->resolveDistinctIdFromContext();
            })
            ->forMember('message', function () use ($data) {
                return $this->identityTrackerMessageRequestDataMapper
                    ->map($data);
            })
        ;

        return $config;
    }

    private function resolveDistinctIdFromContext(): string
    {
        try {
            return self::getContextDataByKey(EventsTrackerHandler::DISTINCT_ID);
        } catch (\Throwable $throwable) {
            return '';
        }
    }
}
