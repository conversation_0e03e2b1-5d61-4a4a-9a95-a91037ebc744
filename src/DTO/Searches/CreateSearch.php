<?php

declare(strict_types=1);

namespace App\DTO\Searches;

class CreateSearch
{
    /** @var int */
    private $contractId;

    /** @var int|null */
    private $clientId;

    /** @var GeographyInformation|null */
    private $geographyInformation;

    /** @var int[] */
    private $typologyIds;

    /** @var int[] */
    private $licenseIds;

    /** @var IntRange|null */
    private $price;

    /** @var IntRange|null */
    private $rooms;

    /** @var IntRange|null */
    private $surface;

    /** @var int|null */
    private $heatingId;

    /** @var int */
    private $bathrooms;

    /** @var int|null */
    private $propertyOwnershipId;

    /** @var AdditionalInformation */
    private $additionalInformation;

    /** @var int|null */
    private $receivedSearchId;

    /**
     * @return int
     */
    public function getContractId()
    {
        return $this->contractId;
    }

    /**
     * @param int $contractId
     */
    public function setContractId(int $contractId = null)
    {
        $this->contractId = $contractId;
    }

    /**
     * @return int|null
     */
    public function getClientId()
    {
        return $this->clientId;
    }

    public function setClientId(int $clientId = null)
    {
        $this->clientId = $clientId;
    }

    /**
     * @return GeographyInformation|null
     */
    public function getGeographyInformation()
    {
        return $this->geographyInformation;
    }

    public function setGeographyInformation(GeographyInformation $geographyInformation = null)
    {
        $this->geographyInformation = $geographyInformation;
    }

    /**
     * @return int[]
     */
    public function getTypologyIds()
    {
        return $this->typologyIds;
    }

    /**
     * @param int[] $typologyIds
     */
    public function setTypologyIds(array $typologyIds = null)
    {
        $this->typologyIds = $typologyIds;
    }

    /**
     * @return int[]
     */
    public function getLicenseIds()
    {
        return $this->licenseIds;
    }

    /**
     * @param int[] $licenseIds
     */
    public function setLicenseIds(array $licenseIds = null)
    {
        $this->licenseIds = $licenseIds;
    }

    /**
     * @return IntRange|null
     */
    public function getPrice()
    {
        return $this->price;
    }

    public function setPrice(IntRange $price = null)
    {
        $this->price = $price;
    }

    /**
     * @return IntRange|null
     */
    public function getRooms()
    {
        return $this->rooms;
    }

    public function setRooms(IntRange $rooms = null)
    {
        $this->rooms = $rooms;
    }

    /**
     * @return IntRange|null
     */
    public function getSurface()
    {
        return $this->surface;
    }

    public function setSurface(IntRange $surface = null)
    {
        $this->surface = $surface;
    }

    /**
     * @return int|null
     */
    public function getHeatingId()
    {
        return $this->heatingId;
    }

    public function setHeatingId(int $heatingId = null)
    {
        $this->heatingId = $heatingId;
    }

    /**
     * @return int
     */
    public function getBathrooms()
    {
        return $this->bathrooms;
    }

    /**
     * @param int $bathrooms
     */
    public function setBathrooms(int $bathrooms = null)
    {
        $this->bathrooms = $bathrooms;
    }

    /**
     * @return int|null
     */
    public function getPropertyOwnershipId()
    {
        return $this->propertyOwnershipId;
    }

    public function setPropertyOwnershipId(int $propertyOwnershipId = null)
    {
        $this->propertyOwnershipId = $propertyOwnershipId;
    }

    /**
     * @return AdditionalInformation
     */
    public function getAdditionalInformation()
    {
        return $this->additionalInformation;
    }

    /**
     * @param AdditionalInformation $additionalInformation
     */
    public function setAdditionalInformation(AdditionalInformation $additionalInformation = null)
    {
        $this->additionalInformation = $additionalInformation;
    }

    /**
     * @return int|null
     */
    public function getReceivedSearchId()
    {
        return $this->receivedSearchId;
    }

    public function setReceivedSearchId(int $receivedSearchId = null)
    {
        $this->receivedSearchId = $receivedSearchId;
    }
}
