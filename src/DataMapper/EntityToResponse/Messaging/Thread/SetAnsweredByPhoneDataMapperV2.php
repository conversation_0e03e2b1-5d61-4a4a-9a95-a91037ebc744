<?php

declare(strict_types=1);

namespace App\DataMapper\EntityToResponse\Messaging\Thread;

use App\DataMapper\AbstractDataMapper;
use App\Model\Response\Messaging\Thread\SetAnsweredByPhoneResponse;
use ObjectMapper\Configuration\ObjectMapperConfig;
use ObjectMapper\DataType;

class SetAnsweredByPhoneDataMapperV2 extends AbstractDataMapper
{
    protected function getDestinationClass(): string
    {
        return SetAnsweredByPhoneResponse::class;
    }

    protected function getConfiguration($data): ObjectMapperConfig
    {
        $config = $this->getObjectMapperConfig();
        $config
            ->registerMapping(DataType::ARRAY, $this->getDestinationClass())
            ->forMember('answeredByPhone', function () use ($data) {
                $answerByPhone = $data['answeredByPhone'];

                return $answerByPhone->toIso8601String();
            })
            ->forMember('modified', function () {
                return null;
            })
        ;

        return $config;
    }
}
