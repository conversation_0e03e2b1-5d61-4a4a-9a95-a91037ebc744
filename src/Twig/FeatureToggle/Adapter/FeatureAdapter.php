<?php

namespace App\Twig\FeatureToggle\Adapter;

    use App\Component\FeatureToggle\Feature;
    use App\Component\FeatureToggle\FeatureList;

    class FeatureAdapter
    {
        public static function toClient(FeatureList $featureList): array
        {
            $data = [];

            /** @var Feature $feature */
            foreach ($featureList as $feature) {
                $data[$feature->getName()] = [
                    'group' => $feature->getGroup()->getName(),
                ];
            }

            return $data;
        }
    }
