<?php

namespace App\Twig\FeatureToggle;

    use Twig\Token;

    class FeatureToggleGroupTokenParser extends \Twig\TokenParser\AbstractTokenParser
    {
        const TAG_NAME = 'feature_group';

        /**
         * @return \Twig_Node_If
         *
         * @throws \Twig\Error\SyntaxError
         */
        public function parse(Token $token)
        {
            $stream = $this->parser->getStream();

            $seq = $this->parser->getExpressionParser()->parseExpression();

            $stream->expect(Token::BLOCK_END_TYPE);
            // Store the body of the feature.
            $body = $this->parser->subparse([$this, 'decideFeatureEnd'], true);
            $stream->expect(Token::BLOCK_END_TYPE);

            $expression = new \Twig_Node_Expression_Binary_Equal(
                new \Twig_Node_Expression_Function(
                    'feature_group_enabled',
                    new \Twig_Node([$seq]), $token->getLine()
                ),
                new \Twig_Node_Expression_Constant(true, $token->getLine()),
                $token->getLine()
            );

            return new \Twig_Node_If(
                new \Twig_Node([$expression, $body]),
                null,
                $token->getLine()
            );
        }

        /**
         * Test whether the feature is ended or not.
         */
        public function decideFeatureEnd(\Twig_Token $token): bool
        {
            return $token->test($this->getEndTag());
        }

        /**
         * Return the tag that marks the beginning of a feature.
         */
        public function getTag(): string
        {
            return self::TAG_NAME;
        }

        /**
         * Return the tag that marks the end of the feature.
         */
        public function getEndTag(): string
        {
            return 'end' . $this->getTag();
        }
    }
