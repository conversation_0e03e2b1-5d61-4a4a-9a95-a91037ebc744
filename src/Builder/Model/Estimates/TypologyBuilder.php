<?php

namespace App\Builder\Model\Estimates;

use App\Builder\BuilderInterface;
use App\Model\Estimates\Typology;

class TypologyBuilder implements BuilderInterface
{
    /** @var Typology */
    private $instance;

    public static function newBuilder(Typology $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(Typology $entity = null)
    {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new Typology();
        }
    }

    public function build(): Typology
    {
        return $this->instance;
    }

    public function fromApiResponse(array $data): self
    {
        $this->instance->setId($data['id'] ?? null);
        $this->instance->setName($data['name'] ?? null);

        return $this;
    }
}
