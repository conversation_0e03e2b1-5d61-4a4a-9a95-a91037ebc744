<?php

declare(strict_types=1);

namespace App\Builder\Model\Messaging;

use App\Builder\BuilderInterface;
use App\Model\Messaging\AdditionalInfo;
use Carbon\Carbon;

class AdditionalInfoBuilder implements BuilderInterface
{
    /** @var AdditionalInfo */
    private $instance;

    public static function newBuilder(AdditionalInfo $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(AdditionalInfo $entity = null)
    {
        $this->instance = (null !== $entity) ? $entity : new AdditionalInfo();
    }

    public function build(): AdditionalInfo
    {
        return $this->instance;
    }

    public function fromApiResponse(array $data): self
    {
        $answeredByPhone = Carbon::parse($data['answeredByPhone']);
        $modified        = Carbon::parse($data['modified']);

        $this
            ->withAnsweredByPhone($answeredByPhone)
            ->withModified($modified);

        return $this;
    }

    public function withAnsweredByPhone(Carbon $answeredByPhone): self
    {
        $this->instance
            ->setAnsweredByPhone($answeredByPhone);

        return $this;
    }

    public function withModified(Carbon $modified): self
    {
        $this->instance
            ->setModified($modified);

        return $this;
    }
}
