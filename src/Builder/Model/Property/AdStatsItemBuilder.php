<?php

declare(strict_types=1);

namespace App\Builder\Model\Property;

use App\Builder\BuilderInterface;
use App\Model\Property\AdStats;
use App\Model\Property\AdStatsItem;

class AdStatsItemBuilder implements BuilderInterface
{
    private AdStatsItem $instance;

    public static function newBuilder(?AdStatsItem $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(?AdStatsItem $entity = null): void
    {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new AdStatsItem();
        }
    }

    public function build(): AdStatsItem
    {
        return $this->instance;
    }

    public function withAdId(?int $adId = null): self
    {
        $this->instance->setAdId($adId);

        return $this;
    }

    public function withStats(?AdStats $stats = null): self
    {
        $this->instance->setStats($stats);

        return $this;
    }
}
