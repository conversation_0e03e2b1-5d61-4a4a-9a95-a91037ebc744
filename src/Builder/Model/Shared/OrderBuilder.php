<?php

namespace App\Builder\Model\Shared;

use App\Builder\BuilderInterface;
use App\Model\Shared\Order;

class OrderBuilder implements BuilderInterface
{
    /** @var Order */
    private $instance;

    public static function newBuilder(Order $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(Order $entity = null)
    {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new Order();
        }
    }

    public function build(): Order
    {
        return $this->instance;
    }

    public function withField($field = null): self
    {
        $this->instance
            ->setField($field);

        return $this;
    }

    public function withDirection($direction = null): self
    {
        $this->instance
            ->setDirection($direction);

        return $this;
    }
}
