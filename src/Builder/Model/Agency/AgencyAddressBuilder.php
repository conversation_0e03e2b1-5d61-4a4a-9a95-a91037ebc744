<?php

declare(strict_types=1);

namespace App\Builder\Model\Agency;

use App\Builder\BuilderInterface;
use App\Builder\Model\Geography\AddressBuilder;
use App\Builder\Model\Geography\CityBuilder;
use App\Model\Agency\AgencyAddress;
use App\Model\Geography\Address;
use App\Model\Geography\City;

class AgencyAddressBuilder implements BuilderInterface
{
    /** @var AgencyAddress */
    private $instance;

    public static function newBuilder(
        AgencyAddress $entity = null
    ): self {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(
        AgencyAddress $entity = null
    ) {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new AgencyAddress();
        }
    }

    public function build(): AgencyAddress
    {
        return $this->instance;
    }

    public function fromApiResponse(array $data): self
    {
        $this
            ->withAddress(
                AddressBuilder::newBuilder()
                    ->fromApiResponse($data['address'] ?? [])
                    ->build()
            )
            ->withCity(
                CityBuilder::newBuilder()
                    ->fromApiResponse($data['city'] ?? [])
                    ->build()
            )
        ;

        return $this;
    }

    public function withAddress(
        Address $address = null
    ): self {
        $this->instance
            ->setAddress($address);

        return $this;
    }

    public function withCity(
        City $city = null
    ): self {
        $this->instance
            ->setCity($city);

        return $this;
    }
}
