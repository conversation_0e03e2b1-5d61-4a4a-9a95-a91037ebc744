<?php

namespace App\Builder\Model\Agency;

use App\Builder\BuilderInterface;
use App\Model\Agency\TwoFactorAuthenticationType;

class TwoFactorAuthenticationTypeBuilder implements BuilderInterface
{
    /** @var TwoFactorAuthenticationType */
    private $instance;

    public static function newBuilder(TwoFactorAuthenticationType $entity = null): self
    {
        $builder = new self();
        $builder->create($entity);

        return $builder;
    }

    public function create(TwoFactorAuthenticationType $entity = null)
    {
        if (null !== $entity) {
            $this->instance = $entity;
        } else {
            $this->instance = new TwoFactorAuthenticationType();
        }
    }

    public function build(): TwoFactorAuthenticationType
    {
        return $this->instance;
    }

    public function fromApiResponse(array $data): self
    {
        $this
            ->withId($data['id'] ?? null)
            ->withName($data['name'] ?? null)
        ;

        return $this;
    }

    public function withId(int $id = null): self
    {
        $this->instance
            ->setId($id);

        return $this;
    }

    public function withName(string $name = null): self
    {
        $this->instance
            ->setName($name);

        return $this;
    }
}
