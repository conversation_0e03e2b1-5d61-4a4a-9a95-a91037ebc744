<?php

declare(strict_types=1);

namespace App\Entity\Property;

use Symfony\Component\Validator\Constraints as Assert;

class AdCopy
{
    /**
     * @var int|null
     *
     * @Assert\NotNull
     */
    private $id;

    /**
     * @var bool|null
     *
     * @Assert\NotNull
     */
    private $imagesCopySuccess;

    /**
     * @var bool|null
     *
     * @Assert\NotNull
     */
    private $plansCopySuccess;

    /**
     * @var bool|null
     *
     * @Assert\NotNull
     */
    private $documentsCopySuccess;

    /**
     * @return int|null
     */
    public function getId()
    {
        return $this->id;
    }

    public function setId(int $id = null): self
    {
        $this->id = $id;

        return $this;
    }

    /**
     * @return bool|null
     */
    public function getImagesCopySuccess()
    {
        return $this->imagesCopySuccess;
    }

    public function setImagesCopySuccess(bool $imagesCopySuccess = null): self
    {
        $this->imagesCopySuccess = $imagesCopySuccess;

        return $this;
    }

    /**
     * @return bool|null
     */
    public function getPlansCopySuccess()
    {
        return $this->plansCopySuccess;
    }

    public function setPlansCopySuccess(bool $plansCopySuccess = null): self
    {
        $this->plansCopySuccess = $plansCopySuccess;

        return $this;
    }

    /**
     * @return bool|null
     */
    public function getDocumentsCopySuccess()
    {
        return $this->documentsCopySuccess;
    }

    public function setDocumentsCopySuccess(bool $documentsCopySuccess = null): self
    {
        $this->documentsCopySuccess = $documentsCopySuccess;

        return $this;
    }
}
