## Option 1: Virtual machine mode

##### Clone dev-environment repository on your local machine

```bash
cd ~/projects
<NAME_EMAIL>:getrix/dev-environment.git
cd dev-environment

export DEVELOPER="name.surname"
ansible-playbook configure-projects.yml --tags mls
```

##### ssh login 

```bash
ssh developer@vm01.{name.surname}.b2b-devs.it3.ns.farm
cd ~/projects/gestionale/mls
```

## Option 2: Docker mode

##### Docker login registry
```bash
docker login icr.pepita.io
```

##### Install the application

- Generate the local certs

```bash
cd docker/dev && ./mkcerts.sh
```

- Build/run containers in detached mode

```bash
docker network create getrix-dev
docker-compose build
docker-compose up -d
docker-compose exec app bash
composer install
```

- Build the frontend within the local app folder

```bash
volta setup
yarn install 
yarn build
```

now go to: 
- [https://gestionale.it.localhost](https://gestionale.it.localhost/signin/)
- [https://gestionale.es.localhost](https://gestionale.es.localhost/signin/)
- [https://gestionale.lu.localhost](https://gestionale.lu.localhost/signin/)
- [https://gestionale.hr.localhost](https://gestionale.hr.localhost/signin/)
