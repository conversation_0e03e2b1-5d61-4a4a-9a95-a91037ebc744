FROM icr.pepita.io/qa/docker-images/getrix/mls:1.0

ARG root_dir="/application"

## product come from --build-arg parameter
ARG product
ARG version
ENV version=$version

## Add files to image
COPY docker/popeye/health-check /usr/bin/health-check
COPY docker/popeye/nginx.conf /etc/nginx/sites-enabled/gestionale-it.conf
COPY docker/popeye/nginx.conf /etc/nginx/sites-enabled/gestionale-es.conf
COPY docker/popeye/nginx.conf /etc/nginx/sites-enabled/gestionale-lu.conf
COPY docker/popeye/nginx.conf /etc/nginx/sites-enabled/gestionale-hr.conf
COPY docker/popeye/nginx.conf /etc/nginx/sites-enabled/gestionale-rs.conf
RUN rm /etc/nginx/sites-enabled/default
COPY docker/popeye/entrypoint.sh /usr/bin/docker-entrypoint.sh
COPY docker/popeye/exim4.conf /etc/exim4/update-exim4.conf.conf
COPY docker/popeye/php-ini-overrides.ini /etc/php/7.0/fpm/conf.d/99-overrides.ini

## Replaces
RUN sed -i 's#fastcgi_param COUNTRY_TAG "it";#fastcgi_param COUNTRY_TAG "es";#g' /etc/nginx/sites-enabled/gestionale-es.conf
RUN sed -i 's#fastcgi_param COUNTRY_TAG "lu";#fastcgi_param COUNTRY_TAG "lu";#g' /etc/nginx/sites-enabled/gestionale-lu.conf
RUN sed -i 's#fastcgi_param COUNTRY_TAG "hr";#fastcgi_param COUNTRY_TAG "hr";#g' /etc/nginx/sites-enabled/gestionale-hr.conf
RUN sed -i 's#fastcgi_param COUNTRY_TAG "rs";#fastcgi_param COUNTRY_TAG "rs";#g' /etc/nginx/sites-enabled/gestionale-rs.conf

## Project files copy
COPY . ${root_dir}
WORKDIR ${root_dir}

## MTA
RUN echo "* <EMAIL> Ttbc" >> /etc/exim4/conf.d/rewrite/00_exim4-config_header
RUN update-exim4.conf

## 5000 is for Gitlab Auto DevOps
EXPOSE 5000

## Ngnix and PHP boot
CMD ["nginx", "-g", "daemon off;"]
ENTRYPOINT ["/usr/bin/docker-entrypoint.sh"]
