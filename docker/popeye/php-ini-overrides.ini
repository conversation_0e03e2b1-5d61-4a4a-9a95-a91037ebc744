apc.enable_cli = 1
date.timezone = UTC
session.auto_start = Off
short_open_tag = Off
memory_limit = -1
max_execution_time = 300

# http://symfony.com/doc/current/performance.html
opcache.memory_consumption=256
opcache.max_accelerated_files = 20000
# Only in prod
#opcache.validate_timestamps=0

realpath_cache_size = 4096K
realpath_cache_ttl = 600

php_admin_value[post_max_size] = 31M
php_admin_value[upload_max_filesize] = 30M
